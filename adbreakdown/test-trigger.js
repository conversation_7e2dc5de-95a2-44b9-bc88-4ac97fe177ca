// Test the metadata extraction trigger function
const { createClient } = require('@supabase/supabase-js');

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function testTriggerFunction() {
  try {
    console.log('🧪 Testing trigger function with sample marketing analysis...');
    
    // Create a test record with marketing analysis
    const testMarketingAnalysis = {
      metadata: {
        brand: "Test Brand",
        celebrity: ["Test Celebrity 1", "Test Celebrity 2"], 
        campaign_category: ["Brand Building", "Awareness"],
        celebrity_context: "Test context for celebrities",
        product_category: "Technology",
        geography: "Global"
      }
    };
    
    // Insert a test analysis
    const { data: analysis, error: insertError } = await supabase
      .from('ad_analyses')
      .insert([{
        user_id: '00000000-0000-0000-0000-000000000000', // System test user
        youtube_url: 'https://youtube.com/watch?v=test123',
        title: 'Test Trigger Function',
        thumbnail_url: 'https://example.com/thumb.jpg',
        duration_seconds: 30,
        status: 'processing',
        is_public: false,
        slug: `test-trigger-${Date.now()}`,
        youtube_video_id: 'test123',
        marketing_analysis: JSON.stringify(testMarketingAnalysis)
      }])
      .select()
      .single();
    
    if (insertError) {
      console.error('❌ Insert error:', insertError);
      return;
    }
    
    console.log('✅ Test record created:', analysis.id);
    
    // Trigger the function by updating status to 'completed'
    const { error: updateError } = await supabase
      .from('ad_analyses')
      .update({ status: 'completed' })
      .eq('id', analysis.id);
    
    if (updateError) {
      console.error('❌ Update error:', updateError);
      return;
    }
    
    console.log('✅ Status updated to completed, trigger should have fired');
    
    // Wait a moment for trigger to process
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    // Check if metadata was extracted
    const { data: updatedAnalysis, error: fetchError } = await supabase
      .from('ad_analyses')
      .select('id, brand, celebrity, campaign_category, celebrity_context, product_category, geography')
      .eq('id', analysis.id)
      .single();
    
    if (fetchError) {
      console.error('❌ Fetch error:', fetchError);
      return;
    }
    
    console.log('\n🔍 Extracted metadata:');
    console.log(`  Brand: ${updatedAnalysis.brand}`);
    console.log(`  Celebrity: ${JSON.stringify(updatedAnalysis.celebrity)} (${Array.isArray(updatedAnalysis.celebrity) ? 'array' : 'not array'})`);
    console.log(`  Campaign Category: ${JSON.stringify(updatedAnalysis.campaign_category)} (${Array.isArray(updatedAnalysis.campaign_category) ? 'array' : 'not array'})`);
    console.log(`  Celebrity Context: ${updatedAnalysis.celebrity_context}`);
    console.log(`  Product Category: ${updatedAnalysis.product_category}`);
    console.log(`  Geography: ${updatedAnalysis.geography}`);
    
    // Verify extraction worked
    const isSuccess = updatedAnalysis.brand === 'Test Brand' &&
                     Array.isArray(updatedAnalysis.celebrity) &&
                     updatedAnalysis.celebrity.length === 2 &&
                     Array.isArray(updatedAnalysis.campaign_category) &&
                     updatedAnalysis.campaign_category.length === 2;
    
    if (isSuccess) {
      console.log('\n✅ SUCCESS: Trigger function correctly extracted arrays!');
    } else {
      console.log('\n❌ ISSUE: Trigger function did not extract data correctly');
    }
    
    // Clean up test record
    await supabase
      .from('ad_analyses')
      .delete()
      .eq('id', analysis.id);
    
    console.log('🧹 Test record cleaned up');
    
  } catch (error) {
    console.error('❌ Test error:', error);
  }
}

testTriggerFunction();