# breakdown.ad

AI-Powered Video Ad Analysis SaaS Platform

## Overview

breakdown.ad transforms subjective ad evaluations into objective, data-driven insights through AI-powered analysis, empowering marketers and advertisers to optimize their video campaigns effectively.

## Tech Stack

- **Frontend**: Next.js 15, <PERSON>act 19, <PERSON><PERSON>, Tailwind CSS
- **UI Components**: ShadCN/ui, Radix UI
- **Authentication**: Clerk
- **Database**: Supabase (PostgreSQL)
- **AI/ML**: Google Vertex AI (Gemini 2.5 Pro) OR Direct Gemini API
- **Payments**: Lemon Squeezy
- **Video**: Video.js, yt-dlp-wrap

## Getting Started

1. Install dependencies:
   ```bash
   npm install
   ```

2. Copy environment variables:
   ```bash
   cp .env.example .env.local
   ```

3. Update `.env.local` with your API keys

4. Run the development server:
   ```bash
   npm run dev
   ```

5. Open [http://localhost:3000](http://localhost:3000)

## Analysis Flow

### How Video Analysis Works

1. **User Input**: User submits YouTube URL via `PublicAnalysisCard` component
2. **Validation & Record Creation**: 
   - URL validated via `validateYouTubeUrl()` 
   - Analysis record created in `ad_analyses` table with `pending` status
   - User redirected to `/ad/[slug]` page
3. **Auto-Trigger**: `SharedAnalysisPage` detects pending status and automatically starts analysis after 1s delay
4. **Dual Processing Paths** (Feature flag dependent):

   **Path A: Vertex AI Streaming** (`NEXT_PUBLIC_USE_VERTEX_AI=true`)
   - Uses `useAnalysisStream` hook to call Vertex AI endpoint
   - Real-time Server-Sent Events (SSE) stream progress updates
   - Vertex AI (Gemini 2.5 Pro) processes video with Google Search grounding
   
   **Path B: Direct Gemini API** (`NEXT_PUBLIC_USE_VERTEX_AI=false` or unset)
   - Direct frontend call to Gemini API with model fallbacks
   - Uses Gemini 2.5 Pro → 1.5 Pro → 1.5 Flash fallback chain
   - Immediate processing and response

5. **Results Display**: Frontend renders completed analysis with structured insights

### Key Architecture Points
- **NO Queue System**: Queue implementation has been completely removed
- **Feature Flag Control**: `NEXT_PUBLIC_USE_VERTEX_AI` toggles between streaming vs direct API
- **NO Separate Backend**: All AI processing happens in Next.js API routes or frontend
- **Auto-processing**: Analyses start automatically when page loads with pending status
- **Model Fallbacks**: Direct API path includes automatic fallbacks for reliability

## Project Structure

- `/src/app` - Next.js App Router pages
  - `(marketing)/` - Public marketing pages  
  - `(dashboard)/` - Authenticated dashboard pages
  - `(auth)/` - Sign-in/up pages
  - `api/` - Backend API routes
- `/src/components` - React components
  - `analysis/` - Analysis display components
  - `ui/` - Reusable UI components
- `/src/hooks` - Custom React hooks
  - `useAnalysisStream.ts` - SSE streaming hook
- `/src/lib` - Utility functions and services
- `/database` - Database migrations and schema

## Development Commands

- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run build:admin` - Build admin-only version
- `npm run lint` - Run ESLint
- `npm run type-check` - Run TypeScript compiler

## Documentation

See `/documentation` folder for:
- Product Requirements Document (PRD)
- User Flow Documentation
- Task Implementation Plan
- Architecture Overview
