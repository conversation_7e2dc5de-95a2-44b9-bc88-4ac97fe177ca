// Quick verification of database columns and functions
const { createClient } = require('@supabase/supabase-js');

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('Missing environment variables. Please set NEXT_PUBLIC_SUPABASE_URL and SUPABASE_SERVICE_ROLE_KEY');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function verifyColumns() {
  try {
    console.log('🔍 Checking column types...');
    
    // Check table structure
    const { data: tableInfo, error: tableError } = await supabase.rpc('get_table_info', {
      table_name: 'ad_analyses'
    });
    
    if (tableError) {
      console.log('Creating custom query to check columns...');
      
      // Query a sample record to see column types and names
      const { data: sample, error: sampleError } = await supabase
        .from('ad_analyses')
        .select('*')
        .not('celebrity', 'is', null)
        .limit(1);
      
      if (sampleError) {
        console.error('❌ Error querying sample data:', sampleError);
        return;
      }
      
      console.log('✅ Database structure and sample data:');
      if (sample.length > 0) {
        const record = sample[0];
        console.log('\n📋 Available columns:');
        Object.keys(record).sort().forEach(key => {
          const value = record[key];
          const type = Array.isArray(value) ? 'array' : typeof value;
          console.log(`  ${key}: ${type}`);
        });
        
        console.log(`\n📝 Sample Celebrity Data:`);
        console.log(`  Celebrity: ${JSON.stringify(record.celebrity)}`);
        console.log(`  Celebrity Type: ${typeof record.celebrity} (${Array.isArray(record.celebrity) ? 'array' : 'not array'})`);
        console.log(`  Campaign Category: ${JSON.stringify(record.campaign_category)}`);
        console.log(`  Campaign Type: ${typeof record.campaign_category} (${Array.isArray(record.campaign_category) ? 'array' : 'not array'})`);
        console.log(`  Celebrity Context: ${record.celebrity_context}`);
      }
      
      // Check if trigger function exists
      console.log('\n🔧 Checking trigger function...');
      const { data: functions, error: funcError } = await supabase.rpc('sql', {
        query: `
          SELECT 
            proname as function_name,
            obj_description(p.oid, 'pg_proc') as comment
          FROM pg_proc p
          WHERE proname = 'extract_metadata_from_analysis';
        `
      });
      
      if (funcError) {
        console.log('⚠️ Cannot check function directly. Assuming it exists based on migration files.');
      } else {
        console.log('✅ Function exists:', functions);
      }
      
    } else {
      console.log('✅ Table info retrieved:', tableInfo);
    }
    
  } catch (error) {
    console.error('❌ Verification error:', error);
  }
}

verifyColumns();