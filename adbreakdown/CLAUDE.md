# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

# AdBreakdown Development Rules

## Project Context
This is AdBreakdown - an AI-powered video ad analysis SaaS platform.

## Documentation Reference
All project specifications are in the `/documentation` folder:
- `documentation/prd.md` - Complete PRD with features, user stories, and acceptance criteria
- `documentation/user-flow.md` - Detailed user flows and navigation paths  
- `documentation/folder-structure.md` - Project folder structure
- `documentation/task-plan.md` - Phase-by-phase implementation roadmap
- `documentation/ad-id-prototype.ts` - Fully functional prototype of the ad/[id]/page.tsx 

## Development Guidelines
1. ALWAYS reference relevant docs when implementing features
2. Follow the exact folder structure from `documentation/folder-structure.md`
3. Implement user flows as specified in `documentation/user-flow.md`
4. Follow phase progression from `documentation/task-plan.md`
5. Meet acceptance criteria from `documentation/prd.md`
6. Implement ad/[id]/page.tsx the analysi page as it is in `documentation/ad-id-prototype.ts`

## Common Development Commands

### Build and Development
```bash
npm run dev                    # Start development server
npm run build                  # Build for production  
npm run build:admin           # Build admin-only version
npm start                     # Start production server
npm run lint                  # Run ESLint
npm run type-check            # Run TypeScript type checking
```

## Current Analysis Processing Flow (After Queue System Removal)

### Step-by-Step Analysis Trigger Flow (As Actually Implemented)

**1. User Input (PublicAnalysisCard.tsx)**
- User enters YouTube URL in `PublicAnalysisCard` component
- Frontend validates URL format and checks user credits
- Calls `POST /api/analyses` with YouTube URL

**2. Analysis Record Creation (api/analyses/route.ts)**
- Validates YouTube URL via `validateYouTubeUrl()` (checks duration, accessibility)
- Creates user record if doesn't exist (auto-creates for dev/testing)
- Checks for existing analyses (user-specific and public)
- Creates new `ad_analyses` record with:
  - `status: 'pending'`
  - YouTube metadata (title, thumbnail, duration, etc.)
  - Generated slug
- Returns `analysis_id` and `slug` to frontend

**3. Page Redirect & Auto-Trigger (SharedAnalysisPage.tsx)**
- Frontend redirects to `/ad/[slug]` 
- `SharedAnalysisPage` component loads with analysis in `'pending'` status
- Auto-trigger logic (lines ~883-890) detects `status: 'pending'` and automatically calls `handleTriggerInitialAnalysis()` after 1s delay

**4. Analysis Processing - Feature Flag Dependent (lines ~790-851)**

**Path A: Vertex AI with Streaming** (`NEXT_PUBLIC_USE_VERTEX_AI=true`)
- Calls `startStream(analysisId, videoUrl)` from `useAnalysisStream` hook
- Routes to `POST /api/analyses/[id]/trigger-vertex-analysis`
- Real-time Server-Sent Events (SSE) stream updates to frontend:
  - `thinking` messages: Progress updates
  - `done` message: Analysis complete with final slug
  - `error` message: Processing failures
- Uses Vertex AI (Gemini 2.5 Pro) with Google Search grounding

**Path B: Direct Gemini API** (`NEXT_PUBLIC_USE_VERTEX_AI=false` or unset)
- Calls `callGeminiApi()` directly in frontend component (lines ~802-851)
- Uses Gemini 2.5 Pro model with fallbacks (gemini-1.5-pro, gemini-1.5-flash)
- Includes Google Search grounding for non-multimodal requests
- Direct processing with immediate response
- Saves result via `POST /api/analyses/[id]/update-ai-analysis`

**5. Analysis Display**
- Frontend refreshes analysis data when processing completes
- `SharedAnalysisPage` renders completed analysis from JSON structure
- Status updated to `'completed'`

### Key Architectural Points
- **Dual Processing Paths**: Feature flag `NEXT_PUBLIC_USE_VERTEX_AI` controls Vertex AI streaming vs direct Gemini API
- **NO Queue System**: Queue implementation has been completely removed
- **NO Supabase Functions**: All AI processing happens in Next.js API routes or frontend
- **Auto-triggering**: Analyses start automatically when page loads with pending status
- **Fallback Models**: Direct API path includes model fallbacks for reliability
- **Google Search Grounding**: Both paths use Google Search for enhanced context (when applicable)

## Tech Stack
- **Frontend**: Next.js 15, React 19, TypeScript, Tailwind CSS
- **UI Components**: ShadCN/ui (Radix UI primitives)
- **Authentication**: Clerk
- **Database**: Supabase (PostgreSQL)
- **AI/ML**: Google Vertex AI (Gemini 2.5 Pro) OR Direct Gemini API
- **Payments**: Lemon Squeezy
- **Video**: Video.js, yt-dlp-wrap

## Architecture Overview

### App Router Structure
- `src/app/(marketing)/` - Public marketing pages (landing, pricing, etc.)
- `src/app/(dashboard)/` - Authenticated dashboard pages
- `src/app/(auth)/` - Authentication pages (sign-in/up)
- `src/app/api/` - API routes for all backend functionality
- `src/app/admin/` - Admin-only pages

### Key Components Architecture
- `SharedAnalysisPage.tsx` - Main analysis display component with streaming capabilities
- `useAnalysisStream.ts` - Hook for managing SSE streaming from Vertex AI
- Analysis components in `src/components/analysis/` - Modular display components

### Database Layer
- Supabase client with service role for server-side operations
- RLS policies for user data isolation
- Migration system in `database/migrations/`
- Schema in `database/schema.sql`

### AI Processing Pipeline
1. YouTube URL validation and metadata extraction
2. Video content analysis via Vertex AI with grounding OR direct Gemini API
3. Structured JSON response parsing
4. Real-time streaming to frontend via SSE (Vertex AI path only)
5. Data persistence to PostgreSQL

## Configuration Files

### Next.js Configuration
- `next.config.js` includes admin-only build mode (`ADMIN_ONLY=true`)
- React Compiler enabled experimentally
- Optimized chunk splitting for performance
- Extensive caching headers for static assets

### TypeScript Configuration
- Strict mode enabled
- Path aliases: `@/*` maps to `./src/*`
- Excludes: `database/`, `documentation/`, `supabase/`, test files

## Key Features to Implement
1. AI sentiment/emotion analysis (PRD section 3.1)
2. Scene-by-scene breakdowns (PRD section 3.2)
3. Competitor comparison (PRD section 3.3)
4. Targeting recommendations (PRD section 3.4)
5. Actionable suggestions (PRD section 3.5)

## Code Standards
- Use TypeScript strict mode
- Follow component-based architecture from architecture.md
- Implement proper error handling
- Use Tailwind for styling (as specified in tech stack)
- Ensure mobile responsiveness
- Follow UI patterns from mockup specifications

## Current Development Phase
Track progress against task-plan.md phases:
- [ ] Phase 1: Auth & Foundation (Steps 1-15)
- [ ] Phase 2: Core AI Pipeline (Steps 16-26)  
- [ ] Phase 3: Analysis UI (Steps 27-34)
- [ ] Phase 4: Report Content Display (Steps 35-39)
- [ ] Phase 5+: Advanced Features

## Testing and Quality Assurance
- Type checking: `npm run type-check`
- Linting: `npm run lint`
- No formal test suite currently implemented

## Environment Variables Required
- `NEXT_PUBLIC_SUPABASE_URL` - Supabase project URL
- `NEXT_PUBLIC_SUPABASE_ANON_KEY` - Supabase anonymous key
- `SUPABASE_SERVICE_ROLE_KEY` - Supabase service role key for server operations
- `NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY` - Clerk authentication
- `CLERK_SECRET_KEY` - Clerk server key
- Vertex AI credentials for Google Cloud

## Feature Flags
- `NEXT_PUBLIC_USE_VERTEX_AI` - Controls whether to use Vertex AI streaming or direct Gemini API
- `ADMIN_ONLY` - When set to 'true', restricts site to admin functions only

## Prompt Expectations
When implementing features, reference specific documents:
# important-instruction-reminders
Do what has been asked; nothing more, nothing less.
NEVER create files unless they're absolutely necessary for achieving your goal.
ALWAYS prefer editing an existing file to creating a new one.
NEVER proactively create documentation files (*.md) or README files. Only create documentation files if explicitly requested by the User.