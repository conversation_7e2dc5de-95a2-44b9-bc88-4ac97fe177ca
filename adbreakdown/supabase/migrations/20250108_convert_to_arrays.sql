-- Migration: Convert celebrity and campaign_category columns from TEXT to TEXT[]
-- This replaces the original columns with array versions and migrates all data

BEGIN;

-- Step 1: Create backup columns temporarily
ALTER TABLE ad_analyses ADD COLUMN celebrity_backup TEXT;
ALTER TABLE ad_analyses ADD COLUMN campaign_category_backup TEXT;

-- Step 2: Backup existing data
UPDATE ad_analyses SET 
  celebrity_backup = celebrity,
  campaign_category_backup = campaign_category;

-- Step 3: Drop the original columns
ALTER TABLE ad_analyses DROP COLUMN celebrity;
ALTER TABLE ad_analyses DROP COLUMN campaign_category;

-- Step 4: Create new array columns with same names
ALTER TABLE ad_analyses ADD COLUMN celebrity TEXT[];
ALTER TABLE ad_analyses ADD COLUMN campaign_category TEXT[];

-- Step 5: Migrate data from backup columns to array columns
-- Handle celebrity data
UPDATE ad_analyses 
SET celebrity = (
  CASE 
    WHEN celebrity_backup IS NULL OR celebrity_backup = '' THEN NULL
    WHEN celebrity_backup LIKE '%(%' THEN 
      -- Has context in parentheses, extract just names for now
      CASE 
        WHEN celebrity_backup ~ '^[^(]+(,|&)[^(]+\(' THEN
          -- Multiple names with context: "Name1, Name2 (context)"
          ARRAY(
            SELECT TRIM(name) 
            FROM unnest(string_to_array(
              regexp_replace(celebrity_backup, '\s*\([^)]*\)', '', 'g'), 
              ','
            )) AS name
            WHERE TRIM(name) != '' AND TRIM(name) NOT IN ('Unknown', 'N/A')
          )
        ELSE
          -- Single name with context: "Name (context)"
          ARRAY[TRIM(regexp_replace(celebrity_backup, '\s*\([^)]*\)', '', 'g'))]
      END
    WHEN celebrity_backup LIKE '%,%' THEN
      -- Multiple names without context: "Name1, Name2, Name3"
      ARRAY(
        SELECT TRIM(name) 
        FROM unnest(string_to_array(celebrity_backup, ',')) AS name
        WHERE TRIM(name) != '' AND TRIM(name) NOT IN ('Unknown', 'N/A')
      )
    ELSE
      -- Single name: "Name"
      ARRAY[celebrity_backup]
  END
)
WHERE celebrity_backup IS NOT NULL;

-- Handle campaign_category data
UPDATE ad_analyses 
SET campaign_category = (
  CASE 
    WHEN campaign_category_backup IS NULL OR campaign_category_backup = '' THEN NULL
    WHEN campaign_category_backup LIKE '%/%' OR campaign_category_backup LIKE '%,%' THEN
      -- Multiple categories: "Cat1 / Cat2" or "Cat1, Cat2"
      ARRAY(
        SELECT TRIM(cat) 
        FROM unnest(string_to_array(
          regexp_replace(campaign_category_backup, '\s*[/,]\s*', ',', 'g'), 
          ','
        )) AS cat
        WHERE TRIM(cat) != '' AND TRIM(cat) NOT IN ('Unknown', 'N/A')
      )
    ELSE
      -- Single category
      ARRAY[campaign_category_backup]
  END
)
WHERE campaign_category_backup IS NOT NULL;

-- Step 6: Copy data from celebrity_names and campaign_categories if they exist and are better
UPDATE ad_analyses 
SET celebrity = celebrity_names
WHERE celebrity_names IS NOT NULL 
  AND (celebrity IS NULL OR array_length(celebrity_names, 1) > array_length(celebrity, 1));

UPDATE ad_analyses 
SET campaign_category = campaign_categories  
WHERE campaign_categories IS NOT NULL 
  AND (campaign_category IS NULL OR array_length(campaign_categories, 1) > array_length(campaign_category, 1));

-- Step 7: Update celebrity_context from backup if needed
UPDATE ad_analyses 
SET celebrity_context = celebrity_backup
WHERE celebrity_context IS NULL 
  AND celebrity_backup LIKE '%(%'
  AND celebrity_backup ~ '\([^)]+\)';

-- Step 8: Drop the temporary backup columns
ALTER TABLE ad_analyses DROP COLUMN celebrity_backup;
ALTER TABLE ad_analyses DROP COLUMN campaign_category_backup;

-- Step 9: Drop the redundant new columns since we're using original column names
ALTER TABLE ad_analyses DROP COLUMN IF EXISTS celebrity_names;
ALTER TABLE ad_analyses DROP COLUMN IF EXISTS campaign_categories;

-- Step 10: Create indexes for array columns
CREATE INDEX IF NOT EXISTS idx_ad_analyses_celebrity_gin ON ad_analyses USING GIN (celebrity);
CREATE INDEX IF NOT EXISTS idx_ad_analyses_campaign_category_gin ON ad_analyses USING GIN (campaign_category);

-- Step 11: Add comments
COMMENT ON COLUMN ad_analyses.celebrity IS 'Array of celebrity names appearing in the ad';
COMMENT ON COLUMN ad_analyses.campaign_category IS 'Array of campaign category tags';

-- Step 12: Show results
SELECT 
  'Migration Results' as info,
  COUNT(*) as total_records,
  COUNT(celebrity) as records_with_celebrities,
  COUNT(CASE WHEN array_length(celebrity, 1) > 1 THEN 1 END) as multi_celebrity_records,
  COUNT(campaign_category) as records_with_categories,
  COUNT(CASE WHEN array_length(campaign_category, 1) > 1 THEN 1 END) as multi_category_records
FROM ad_analyses;

COMMIT;