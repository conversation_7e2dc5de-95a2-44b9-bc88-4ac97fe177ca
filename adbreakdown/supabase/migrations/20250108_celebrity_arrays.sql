-- Migration: Convert celebrity and campaign_category fields to arrays + add celebrity_context
-- This migration maintains backward compatibility during transition

BEGIN;

-- Step 1: Add new array columns alongside existing ones
ALTER TABLE ad_analyses 
ADD COLUMN celebrity_names TEXT[],
ADD COLUMN celebrity_context TEXT,
ADD COLUMN campaign_categories TEXT[];

-- Step 2: Migrate existing data from string fields to arrays
-- Convert single celebrity names to single-item arrays
UPDATE ad_analyses 
SET celebrity_names = CASE 
  WHEN celebrity IS NOT NULL AND celebrity != '' THEN ARRAY[celebrity]
  ELSE NULL
END
WHERE celebrity IS NOT NULL;

-- Convert campaign_category strings to arrays (handle comma/slash separation)
UPDATE ad_analyses 
SET campaign_categories = CASE 
  WHEN campaign_category IS NOT NULL AND campaign_category != '' THEN 
    -- Split by comma or forward slash, trim whitespace
    string_to_array(
      regexp_replace(
        regexp_replace(campaign_category, '\s*[,/]\s*', ',', 'g'), -- Replace comma/slash patterns with comma
        '^\s+|\s+$', '', 'g' -- Trim leading/trailing whitespace
      ), 
      ','
    )
  ELSE NULL
END
WHERE campaign_category IS NOT NULL;

-- Step 3: Create indexes for better query performance on new array columns
CREATE INDEX IF NOT EXISTS idx_ad_analyses_celebrity_names ON ad_analyses USING GIN (celebrity_names);
CREATE INDEX IF NOT EXISTS idx_ad_analyses_campaign_categories ON ad_analyses USING GIN (campaign_categories);

-- Step 4: Add comments for documentation
COMMENT ON COLUMN ad_analyses.celebrity_names IS 'Array of celebrity names featured in the ad';
COMMENT ON COLUMN ad_analyses.celebrity_context IS 'Additional context about celebrity involvement (e.g., "Brand Founder", "Historical Footage")';
COMMENT ON COLUMN ad_analyses.campaign_categories IS 'Array of campaign category tags';

-- Step 5: Create a view for backward compatibility (optional)
CREATE OR REPLACE VIEW ad_analyses_compatible AS 
SELECT *,
  -- Provide backward-compatible string versions
  array_to_string(celebrity_names, ', ') as celebrity_legacy,
  array_to_string(campaign_categories, ', ') as campaign_category_legacy
FROM ad_analyses;

COMMIT;