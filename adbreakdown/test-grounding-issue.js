// Test if Google Search grounding is causing the hang
require('dotenv').config({ path: '.env.local' });
const { VertexAI } = require('@google-cloud/vertexai');

async function testGroundingIssue() {
  console.log('🔍 Testing Google Search Grounding Issue...\n');
  
  try {
    const projectId = process.env.GOOGLE_PROJECT_ID;
    const serviceAccountKey = process.env.GOOGLE_CLOUD_SERVICE_ACCOUNT_KEY;
    const credentials = JSON.parse(serviceAccountKey);
    
    const vertex_ai = new VertexAI({
      project: projectId,
      location: 'us-central1',
      googleAuthOptions: { credentials }
    });
    
    // Test WITH Google Search grounding (this might hang)
    console.log('1. Testing WITH Google Search grounding (production config)...');
    
    const tools = [{ googleSearch: {} }];
    const generationConfig = {
      temperature: 0.9,
      topP: 0.95,
      maxOutputTokens: 8192,
    };
    
    const modelWithGrounding = vertex_ai.getGenerativeModel({ 
      model: 'gemini-2.5-pro',
      generationConfig,
      tools
    });
    
    const videoPart = {
      fileData: {
        mimeType: 'video/mp4',
        fileUri: 'https://www.youtube.com/watch?v=SQtZo4SvBzw',
      },
    };
    
    const textPart = {
      text: 'Analyze this video briefly and respond with just "Analysis with grounding complete".'
    };
    
    // This is likely where it hangs in production
    const groundingResult = await Promise.race([
      modelWithGrounding.generateContent({
        contents: [{ role: "user", parts: [textPart, videoPart] }]
      }),
      new Promise((_, reject) => 
        setTimeout(() => reject(new Error('GROUNDING TIMEOUT after 90s')), 90000)
      )
    ]);
    
    console.log('✅ Grounding request succeeded');
    console.log('📹 Response:', groundingResult.response.candidates?.[0]?.content?.parts?.[0]?.text?.substring(0, 100));
    
    console.log('\n✅ Google Search grounding is working - issue might be elsewhere');
    
  } catch (error) {
    console.error('\n❌ Test failed:', error.message);
    
    if (error.message.includes('GROUNDING TIMEOUT')) {
      console.log('\n🎯 GROUNDING TIMEOUT DETECTED!');
      console.log('This is likely causing your production hang.');
      console.log('\nSolutions:');
      console.log('1. Remove Google Search grounding temporarily');
      console.log('2. Increase timeout in production');
      console.log('3. Use simpler grounding configuration');
      console.log('4. Check Google Search API quotas');
    }
  }
}

testGroundingIssue();