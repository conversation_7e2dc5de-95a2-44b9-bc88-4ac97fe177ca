#!/bin/bash

# Load Supabase environment variables
export SUPABASE_URL=$(grep "NEXT_PUBLIC_SUPABASE_URL" .env.local | cut -d '=' -f2)
export SUPABASE_ANON_KEY=$(grep "NEXT_PUBLIC_SUPABASE_ANON_KEY" .env.local | cut -d '=' -f2)
export SUPABASE_SERVICE_ROLE_KEY=$(grep "SUPABASE_SERVICE_ROLE_KEY" .env.local | cut -d '=' -f2)
export MCP_API_KEY="your-mcp-api-key"

echo "Supabase URL: $SUPABASE_URL"
echo "Supabase Anon Key: ${SUPABASE_ANON_KEY:0:20}..."
echo "Supabase Service Role Key: ${SUPABASE_SERVICE_ROLE_KEY:0:20}..."
echo "MCP API Key: $MCP_API_KEY"

# Start the Supabase MCP server
echo "Starting Supabase MCP server..."
supabase-mcp 