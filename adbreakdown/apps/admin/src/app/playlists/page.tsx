'use client'

import React, { useState, useEffect } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Edit, Save, X, Tag, Plus, Trash2, ExternalLink, Palette, Eye } from 'lucide-react'
import { ColorPicker } from '@/components/ui/ColorPicker'
import { useAuth } from '@/hooks/useAuth'
import Link from 'next/link'
import Image from 'next/image'

interface AdAnalysis {
  id: string
  title: string
  slug: string
  brand: string
  thumbnail_url: string
  duration_formatted: string
  collection_tags: string[]
  created_at: string
  is_public: boolean
}

interface CollectionTag {
  tag: string
  count: number
  latest_ad: string
}

interface PlaylistEditState {
  originalTag: string
  newTag: string
  isEditing: boolean
}

interface BannerConfig {
  id?: string
  playlist_tag: string
  title: string
  subtitle: string
  background_gradient: string
  background_color?: string
  text_color?: string
  features?: string[]
  is_active: boolean
  // Enhanced color options
  heading_color_type?: 'gradient' | 'solid'
  heading_solid_color?: string
  heading_gradient_start?: string
  heading_gradient_middle?: string
  heading_gradient_end?: string
  body_text_color?: string
  background_type?: 'gradient' | 'solid'
  background_gradient_start?: string
  background_gradient_middle?: string
  background_gradient_end?: string
}

interface BannerEditState {
  tag: string
  config: BannerConfig
  isEditing: boolean
}

export default function PlaylistsAdminPage() {
  const { isAuthenticated, loading } = useAuth()
  const [analyses, setAnalyses] = useState<AdAnalysis[]>([])
  const [collectionTags, setCollectionTags] = useState<CollectionTag[]>([])
  const [editingAnalysis, setEditingAnalysis] = useState<AdAnalysis | null>(null)
  const [newTags, setNewTags] = useState('')
  const [loading1, setLoading1] = useState(true)
  const [saving, setSaving] = useState(false)
  const [error, setError] = useState('')
  const [success, setSuccess] = useState('')
  const [accessDenied, setAccessDenied] = useState(false)
  const [searchQuery, setSearchQuery] = useState('')
  const [editingPlaylist, setEditingPlaylist] = useState<PlaylistEditState | null>(null)
  const [deletingPlaylist, setDeletingPlaylist] = useState<string | null>(null)
  const [bannerConfigs, setBannerConfigs] = useState<{[key: string]: BannerConfig}>({})
  const [editingBanner, setEditingBanner] = useState<BannerEditState | null>(null)

  useEffect(() => {
    // Block access if not on admin subdomain
    if (typeof window !== 'undefined' && !window.location.hostname.startsWith('admin.')) {
      window.location.href = '/'
      return
    }

    if (isAuthenticated) {
      fetchAnalyses()
      fetchCollectionTags()
    }
  }, [isAuthenticated])

  useEffect(() => {
    // Fetch banner configs for all playlists
    if (collectionTags.length > 0) {
      fetchBannerConfigs()
    }
  }, [collectionTags])

  const fetchAnalyses = async () => {
    try {
      const response = await fetch('/api/admin/playlists/analyses')
      if (response.status === 403) {
        setAccessDenied(true)
        setLoading1(false)
        return
      }
      if (!response.ok) throw new Error('Failed to fetch analyses')
      const data = await response.json()
      setAnalyses(data.analyses)
    } catch (err) {
      setError('Failed to load analyses')
    } finally {
      setLoading1(false)
    }
  }

  const fetchCollectionTags = async () => {
    try {
      const response = await fetch('/api/admin/playlists/tags')
      if (!response.ok) throw new Error('Failed to fetch tags')
      const data = await response.json()
      setCollectionTags(data.tags)
    } catch (err) {
      setError('Failed to load collection tags')
    }
  }

  const saveAnalysisTags = async () => {
    if (!editingAnalysis) return
    
    setSaving(true)
    try {
      const tags = newTags.split(',').map(tag => tag.trim().toLowerCase()).filter(Boolean)
      
      const response = await fetch('/api/admin/playlists/update-tags', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          analysisId: editingAnalysis.id,
          tags: tags
        })
      })

      if (!response.ok) throw new Error('Failed to save tags')
      
      setSuccess(`Tags updated for "${editingAnalysis.title}"`)
      setEditingAnalysis(null)
      setNewTags('')
      fetchAnalyses()
      fetchCollectionTags()
    } catch (err) {
      setError('Failed to save tags')
    } finally {
      setSaving(false)
    }
  }

  const startEditing = (analysis: AdAnalysis) => {
    setEditingAnalysis(analysis)
    setNewTags(analysis.collection_tags?.join(', ') || '')
    setSuccess('')
    setError('')
  }

  const startEditingPlaylist = (tag: string) => {
    setEditingPlaylist({
      originalTag: tag,
      newTag: tag,
      isEditing: true
    })
    setSuccess('')
    setError('')
  }

  const renamePlaylist = async () => {
    if (!editingPlaylist) return
    
    setSaving(true)
    try {
      const response = await fetch('/api/admin/playlists/rename-tag', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          oldTag: editingPlaylist.originalTag,
          newTag: editingPlaylist.newTag.trim().toLowerCase()
        })
      })

      if (!response.ok) throw new Error('Failed to rename playlist')
      
      setSuccess(`Playlist "${editingPlaylist.originalTag}" renamed to "${editingPlaylist.newTag}"`)
      setEditingPlaylist(null)
      fetchAnalyses()
      fetchCollectionTags()
    } catch (err) {
      setError('Failed to rename playlist')
    } finally {
      setSaving(false)
    }
  }

  const deletePlaylist = async (tag: string) => {
    if (!confirm(`Are you sure you want to delete the playlist "${tag}"? This will remove this tag from all ads.`)) {
      return
    }

    setDeletingPlaylist(tag)
    try {
      const response = await fetch('/api/admin/playlists/delete-tag', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ tag })
      })

      if (!response.ok) throw new Error('Failed to delete playlist')
      
      setSuccess(`Playlist "${tag}" deleted successfully`)
      fetchAnalyses()
      fetchCollectionTags()
    } catch (err) {
      setError('Failed to delete playlist')
    } finally {
      setDeletingPlaylist(null)
    }
  }

  const fetchBannerConfigs = async () => {
    const configs: {[key: string]: BannerConfig} = {}
    
    for (const tag of collectionTags) {
      try {
        const response = await fetch(`/api/admin/playlists/banner-config?playlist_tag=${encodeURIComponent(tag.tag)}`)
        if (response.ok) {
          const data = await response.json()
          if (data.banner_config) {
            configs[tag.tag] = data.banner_config
          }
        }
      } catch (error) {
        console.error(`Failed to fetch banner config for ${tag.tag}:`, error)
      }
    }
    
    setBannerConfigs(configs)
  }

  const startEditingBanner = (tag: string) => {
    const existing = bannerConfigs[tag] || {
      playlist_tag: tag,
      title: `${tag.charAt(0).toUpperCase() + tag.slice(1)} Collection`,
      subtitle: 'Discover curated ads from this collection',
      background_gradient: 'from-indigo-50 via-purple-50 to-blue-50',
      background_color: '#ffffff',
      text_color: 'text-gray-700',
      features: ['AI-Powered Analysis', 'Community Curated', 'Marketing Insights'],
      is_active: true,
      // Enhanced color options
      heading_color_type: 'gradient',
      heading_solid_color: '#1f2937',
      heading_gradient_start: '#4338ca',
      heading_gradient_middle: '#7c3aed',
      heading_gradient_end: '#2563eb',
      body_text_color: '#374151',
      background_type: 'gradient',
      background_gradient_start: '#eef2ff',
      background_gradient_middle: '#f3e8ff',
      background_gradient_end: '#eff6ff'
    }

    setEditingBanner({
      tag,
      config: { ...existing },
      isEditing: true
    })
  }

  const saveBannerConfig = async () => {
    if (!editingBanner) return
    
    setSaving(true)
    try {
      const response = await fetch('/api/admin/playlists/banner-config', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(editingBanner.config)
      })

      if (!response.ok) throw new Error('Failed to save banner config')
      
      const data = await response.json()
      setBannerConfigs(prev => ({
        ...prev,
        [editingBanner.tag]: data.banner_config
      }))
      
      setSuccess(`Banner configuration saved for "${editingBanner.tag}"`)
      setEditingBanner(null)
    } catch (err) {
      setError('Failed to save banner configuration')
    } finally {
      setSaving(false)
    }
  }

  const deleteBannerConfig = async (tag: string) => {
    if (!confirm(`Remove custom banner for "${tag}"? It will revert to the default banner.`)) {
      return
    }

    try {
      const response = await fetch(`/api/admin/playlists/banner-config?playlist_tag=${encodeURIComponent(tag)}`, {
        method: 'DELETE'
      })

      if (!response.ok) throw new Error('Failed to delete banner config')
      
      setBannerConfigs(prev => {
        const updated = { ...prev }
        delete updated[tag]
        return updated
      })
      
      setSuccess(`Banner configuration removed for "${tag}"`)
    } catch (err) {
      setError('Failed to remove banner configuration')
    }
  }

  // Helper functions for generating CSS styles
  const getHeadingStyle = (config: BannerConfig) => {
    if (config.heading_color_type === 'solid') {
      return { color: config.heading_solid_color || '#1f2937' }
    } else {
      const startColor = config.heading_gradient_start || '#4338ca'
      const middleColor = config.heading_gradient_middle || '#7c3aed'
      const endColor = config.heading_gradient_end || '#2563eb'
      return {
        background: `linear-gradient(to right, ${startColor}, ${middleColor}, ${endColor})`,
        backgroundClip: 'text',
        WebkitBackgroundClip: 'text',
        color: 'transparent'
      }
    }
  }

  const getBackgroundStyle = (config: BannerConfig) => {
    if (config.background_type === 'solid') {
      return { backgroundColor: config.background_color || '#ffffff' }
    } else {
      const startColor = config.background_gradient_start || '#eef2ff'
      const middleColor = config.background_gradient_middle || '#f3e8ff'
      const endColor = config.background_gradient_end || '#eff6ff'
      return {
        background: `linear-gradient(to bottom right, ${startColor}, ${middleColor}, ${endColor})`
      }
    }
  }

  const filteredAnalyses = analyses.filter(analysis =>
    analysis.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
    analysis.brand?.toLowerCase().includes(searchQuery.toLowerCase()) ||
    analysis.collection_tags?.some(tag => tag.toLowerCase().includes(searchQuery.toLowerCase()))
  )

  if (loading) {
    return <div className="p-8">Loading...</div>
  }

  if (!isAuthenticated) {
    return (
      <div className="p-8">
        <Card>
          <CardHeader>
            <CardTitle>Access Denied</CardTitle>
            <CardDescription>Please sign in to access the admin panel.</CardDescription>
          </CardHeader>
          <CardContent>
            <Link href="/sign-in">
              <Button>Sign In</Button>
            </Link>
          </CardContent>
        </Card>
      </div>
    )
  }
  
  if (accessDenied) {
    return (
      <div className="p-8">
        <Card>
          <CardHeader>
            <CardTitle>Admin Access Required</CardTitle>
            <CardDescription>You need administrator privileges to access this page.</CardDescription>
          </CardHeader>
          <CardContent>
            <Link href="/studio">
              <Button>Return to Dashboard</Button>
            </Link>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className="p-6 max-w-7xl mx-auto">
      <div className="mb-6">
        <h1 className="text-2xl font-bold mb-2">Playlists Management</h1>
        <p className="text-gray-600">
          Tag ads to create playlists like &ldquo;Super Bowl 2025&rdquo;, &ldquo;Diwali 2024&rdquo;, etc.
        </p>
        <div className="mt-4 flex gap-4">
          <Link href="/admin/playlists">
            <Button variant="outline" size="sm">Playlists</Button>
          </Link>
          <Link href="/admin/waitlist">
            <Button variant="outline" size="sm">Studio Waitlist</Button>
          </Link>
          <Link href="/admin/prompts">
            <Button variant="outline" size="sm">Prompts</Button>
          </Link>
        </div>
      </div>

      {error && (
        <div className="mb-4 p-4 bg-red-50 border border-red-200 rounded-md text-red-700">
          {error}
        </div>
      )}

      {success && (
        <div className="mb-4 p-4 bg-green-50 border border-green-200 rounded-md text-green-700">
          {success}
        </div>
      )}

      <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
        {/* Collection Tags Overview - 1/4 width */}
        <div className="lg:col-span-1">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Tag className="w-5 h-5" />
                Active Playlists
              </CardTitle>
              <CardDescription>
                {collectionTags.length} playlists created
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-2 max-h-96 overflow-y-auto">
                {collectionTags.map((tag) => (
                  <div
                    key={tag.tag}
                    className="p-2 bg-gray-50 rounded-md"
                  >
                    {editingPlaylist?.originalTag === tag.tag ? (
                      <div className="space-y-2">
                        <Input
                          value={editingPlaylist.newTag}
                          onChange={(e) => setEditingPlaylist({
                            ...editingPlaylist,
                            newTag: e.target.value
                          })}
                          placeholder="Enter new playlist name"
                          className="text-sm"
                        />
                        <div className="flex gap-1">
                          <Button
                            onClick={renamePlaylist}
                            disabled={saving || !editingPlaylist.newTag.trim()}
                            size="sm"
                            variant="default"
                          >
                            <Save className="w-3 h-3" />
                          </Button>
                          <Button
                            onClick={() => setEditingPlaylist(null)}
                            size="sm"
                            variant="outline"
                          >
                            <X className="w-3 h-3" />
                          </Button>
                        </div>
                      </div>
                    ) : (
                      <>
                        <div className="flex items-center justify-between">
                          <span className="text-sm font-medium">{tag.tag}</span>
                          <div className="flex items-center gap-1">
                            <Badge variant="secondary">{tag.count}</Badge>
                            <Link href={`/ad-library?collection_tags=${encodeURIComponent(tag.tag)}`}>
                              <Button size="sm" variant="ghost" title="View in Ad Library">
                                <ExternalLink className="w-3 h-3" />
                              </Button>
                            </Link>
                            <Button
                              onClick={() => startEditingPlaylist(tag.tag)}
                              size="sm"
                              variant="ghost"
                              title="Rename playlist"
                            >
                              <Edit className="w-3 h-3" />
                            </Button>
                            <Button
                              onClick={() => startEditingBanner(tag.tag)}
                              size="sm"
                              variant="ghost"
                              title="Customize banner"
                              className="text-purple-600 hover:text-purple-700 hover:bg-purple-50"
                            >
                              <Palette className="w-3 h-3" />
                            </Button>
                            {bannerConfigs[tag.tag] && (
                              <Link href={`/ad-library?playlist_tags=${encodeURIComponent(tag.tag)}`}>
                                <Button
                                  size="sm"
                                  variant="ghost"
                                  title="Preview custom banner"
                                  className="text-blue-600 hover:text-blue-700 hover:bg-blue-50"
                                >
                                  <Eye className="w-3 h-3" />
                                </Button>
                              </Link>
                            )}
                            <Button
                              onClick={() => deletePlaylist(tag.tag)}
                              disabled={deletingPlaylist === tag.tag}
                              size="sm"
                              variant="ghost"
                              title="Delete playlist"
                              className="text-red-600 hover:text-red-700 hover:bg-red-50"
                            >
                              {deletingPlaylist === tag.tag ? (
                                <div className="w-3 h-3 animate-spin rounded-full border-2 border-red-300 border-t-red-600" />
                              ) : (
                                <Trash2 className="w-3 h-3" />
                              )}
                            </Button>
                          </div>
                        </div>
                      </>
                    )}
                  </div>
                ))}
                {collectionTags.length === 0 && (
                  <p className="text-sm text-gray-500">No playlists yet</p>
                )}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Ad Analyses List - 3/4 width */}
        <div className="lg:col-span-3">
          <Card>
            <CardHeader>
              <CardTitle>Ad Analyses</CardTitle>
              <CardDescription>
                Click on any ad to add playlist tags
              </CardDescription>
              <div className="mt-4">
                <Input
                  placeholder="Search by title, brand, or existing tags..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                />
              </div>
            </CardHeader>
            <CardContent>
              {loading1 ? (
                <p>Loading analyses...</p>
              ) : (
                <div className="space-y-3 max-h-[600px] overflow-y-auto">
                  {filteredAnalyses.map((analysis) => (
                    <div
                      key={analysis.id}
                      className={`p-4 border rounded-lg hover:bg-gray-50 cursor-pointer ${
                        editingAnalysis?.id === analysis.id ? 'border-blue-500 bg-blue-50' : ''
                      }`}
                      onClick={() => startEditing(analysis)}
                    >
                      <div className="flex items-start gap-4">
                        {analysis.thumbnail_url && (
                          <Image
                            src={analysis.thumbnail_url}
                            alt={analysis.title}
                            width={80}
                            height={48}
                            className="w-20 h-12 object-cover rounded"
                          />
                        )}
                        <div className="flex-1">
                          <div className="flex items-center justify-between mb-2">
                            <h3 className="font-medium line-clamp-1">{analysis.title}</h3>
                            <div className="flex gap-2">
                              {!analysis.is_public && (
                                <Badge variant="secondary">Private</Badge>
                              )}
                              <Badge variant="outline">{analysis.duration_formatted}</Badge>
                            </div>
                          </div>
                          <p className="text-sm text-gray-600 mb-2">{analysis.brand}</p>
                          <div className="flex flex-wrap gap-1 mb-2">
                            {analysis.collection_tags?.map((tag) => (
                              <Badge key={tag} variant="default" className="text-xs">
                                {tag}
                              </Badge>
                            ))}
                          </div>
                          <div className="text-xs text-gray-500">
                            Created: {new Date(analysis.created_at).toLocaleDateString()}
                          </div>
                        </div>
                      </div>

                      {editingAnalysis?.id === analysis.id && (
                        <div className="mt-4 p-4 bg-white border rounded-md">
                          <div className="space-y-4">
                            <div>
                              <Label htmlFor="tags">Playlist Tags (comma-separated)</Label>
                              <Input
                                id="tags"
                                value={newTags}
                                onChange={(e) => setNewTags(e.target.value)}
                                placeholder="e.g. super-bowl-2025, sports-events, high-budget"
                                className="mt-1"
                              />
                              <p className="text-xs text-gray-500 mt-1">
                                Use lowercase, hyphen-separated format (e.g. &ldquo;super-bowl-2025&rdquo;)
                              </p>
                            </div>
                            <div className="flex gap-2">
                              <Button
                                onClick={saveAnalysisTags}
                                disabled={saving}
                                size="sm"
                              >
                                <Save className="w-4 h-4 mr-2" />
                                {saving ? 'Saving...' : 'Save Tags'}
                              </Button>
                              <Button
                                onClick={() => {
                                  setEditingAnalysis(null)
                                  setNewTags('')
                                }}
                                variant="outline"
                                size="sm"
                              >
                                <X className="w-4 h-4" />
                                Cancel
                              </Button>
                            </div>
                          </div>
                        </div>
                      )}
                    </div>
                  ))}
                  
                  {filteredAnalyses.length === 0 && (
                    <p className="text-center text-gray-500 py-8">
                      {searchQuery ? 'No analyses match your search' : 'No analyses found'}
                    </p>
                  )}
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Banner Configuration Modal */}
      {editingBanner && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <Card className="w-full max-w-2xl max-h-[90vh] overflow-y-auto">
            <CardHeader>
              <CardTitle>Customize Banner for &ldquo;{editingBanner.tag}&rdquo;</CardTitle>
              <CardDescription>
                Configure how this playlist appears in the ad library
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="banner-title">Banner Title</Label>
                <Input
                  id="banner-title"
                  value={editingBanner.config.title}
                  onChange={(e) => setEditingBanner({
                    ...editingBanner,
                    config: { ...editingBanner.config, title: e.target.value }
                  })}
                  placeholder="e.g., Super Bowl 2025 Collection"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="banner-subtitle">Banner Subtitle</Label>
                <Input
                  id="banner-subtitle"
                  value={editingBanner.config.subtitle}
                  onChange={(e) => setEditingBanner({
                    ...editingBanner,
                    config: { ...editingBanner.config, subtitle: e.target.value }
                  })}
                  placeholder="e.g., The biggest game, the biggest ads"
                />
              </div>

              {/* Enhanced Color Pickers */}
              <div className="grid grid-cols-1 gap-6">
                {/* Heading Color Picker */}
                <ColorPicker
                  label="Heading Color"
                  type={editingBanner.config.heading_color_type || 'gradient'}
                  onTypeChange={(type) => setEditingBanner({
                    ...editingBanner,
                    config: { ...editingBanner.config, heading_color_type: type }
                  })}
                  solidColor={editingBanner.config.heading_solid_color || '#1f2937'}
                  onSolidColorChange={(color) => setEditingBanner({
                    ...editingBanner,
                    config: { ...editingBanner.config, heading_solid_color: color }
                  })}
                  gradientStart={editingBanner.config.heading_gradient_start || '#4338ca'}
                  gradientMiddle={editingBanner.config.heading_gradient_middle || '#7c3aed'}
                  gradientEnd={editingBanner.config.heading_gradient_end || '#2563eb'}
                  onGradientStartChange={(color) => setEditingBanner({
                    ...editingBanner,
                    config: { ...editingBanner.config, heading_gradient_start: color }
                  })}
                  onGradientMiddleChange={(color) => setEditingBanner({
                    ...editingBanner,
                    config: { ...editingBanner.config, heading_gradient_middle: color }
                  })}
                  onGradientEndChange={(color) => setEditingBanner({
                    ...editingBanner,
                    config: { ...editingBanner.config, heading_gradient_end: color }
                  })}
                  showMiddleColor={true}
                />

                {/* Body Text Color Picker */}
                <div className="space-y-3">
                  <Label className="text-sm font-medium">Body Text Color</Label>
                  <div className="flex gap-2 items-center">
                    <Input
                      type="color"
                      value={editingBanner.config.body_text_color || '#374151'}
                      onChange={(e) => setEditingBanner({
                        ...editingBanner,
                        config: { ...editingBanner.config, body_text_color: e.target.value }
                      })}
                      className="w-16 h-10 p-1 border rounded cursor-pointer"
                    />
                    <Input
                      value={editingBanner.config.body_text_color || '#374151'}
                      onChange={(e) => setEditingBanner({
                        ...editingBanner,
                        config: { ...editingBanner.config, body_text_color: e.target.value }
                      })}
                      placeholder="#374151"
                      className="flex-1 font-mono text-sm"
                    />
                  </div>
                </div>

                {/* Background Color Picker */}
                <ColorPicker
                  label="Background"
                  type={editingBanner.config.background_type || 'gradient'}
                  onTypeChange={(type) => setEditingBanner({
                    ...editingBanner,
                    config: { ...editingBanner.config, background_type: type }
                  })}
                  solidColor={editingBanner.config.background_color || '#ffffff'}
                  onSolidColorChange={(color) => setEditingBanner({
                    ...editingBanner,
                    config: { ...editingBanner.config, background_color: color }
                  })}
                  gradientStart={editingBanner.config.background_gradient_start || '#eef2ff'}
                  gradientMiddle={editingBanner.config.background_gradient_middle || '#f3e8ff'}
                  gradientEnd={editingBanner.config.background_gradient_end || '#eff6ff'}
                  onGradientStartChange={(color) => setEditingBanner({
                    ...editingBanner,
                    config: { ...editingBanner.config, background_gradient_start: color }
                  })}
                  onGradientMiddleChange={(color) => setEditingBanner({
                    ...editingBanner,
                    config: { ...editingBanner.config, background_gradient_middle: color }
                  })}
                  onGradientEndChange={(color) => setEditingBanner({
                    ...editingBanner,
                    config: { ...editingBanner.config, background_gradient_end: color }
                  })}
                  showMiddleColor={true}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="banner-features">Features (comma-separated)</Label>
                <Input
                  id="banner-features"
                  value={editingBanner.config.features?.join(', ') || ''}
                  onChange={(e) => setEditingBanner({
                    ...editingBanner,
                    config: { 
                      ...editingBanner.config, 
                      features: e.target.value.split(',').map(f => f.trim()).filter(Boolean)
                    }
                  })}
                  placeholder="e.g., Premium Content, Expert Analysis, Trending Now"
                />
              </div>

              {/* Live Preview */}
              <div className="space-y-2">
                <Label className="text-sm font-medium">Live Preview</Label>
                <div 
                  className="border border-gray-200 rounded-xl p-6 text-center"
                  style={getBackgroundStyle(editingBanner.config)}
                >
                  <h2 
                    className="text-2xl font-bold mb-3"
                    style={getHeadingStyle(editingBanner.config)}
                  >
                    {editingBanner.config.title || 'Banner Title'}
                  </h2>
                  <p 
                    className="mb-4 text-base leading-relaxed"
                    style={{ color: editingBanner.config.body_text_color || '#374151' }}
                  >
                    {editingBanner.config.subtitle || 'Banner subtitle goes here'}
                  </p>
                  <div className="flex flex-wrap gap-3 justify-center text-sm">
                    {editingBanner.config.features?.map((feature, index) => (
                      <div key={index} className="flex items-center gap-2">
                        <div 
                          className={`w-2 h-2 rounded-full`}
                          style={{ 
                            backgroundColor: index === 0 ? '#10b981' : index === 1 ? '#3b82f6' : '#8b5cf6' 
                          }}
                        ></div>
                        <span style={{ color: editingBanner.config.body_text_color || '#374151' }}>
                          {feature}
                        </span>
                      </div>
                    ))}
                  </div>
                </div>
                <p className="text-xs text-gray-500 text-center">
                  This is how your banner will appear in the ad library
                </p>
              </div>

              <div className="flex gap-2 pt-4">
                <Button
                  onClick={saveBannerConfig}
                  disabled={saving}
                >
                  <Save className="w-4 h-4 mr-2" />
                  {saving ? 'Saving...' : 'Save Banner'}
                </Button>
                <Button
                  onClick={() => setEditingBanner(null)}
                  variant="outline"
                >
                  <X className="w-4 h-4 mr-2" />
                  Cancel
                </Button>
                {bannerConfigs[editingBanner.tag] && (
                  <Button
                    onClick={() => deleteBannerConfig(editingBanner.tag)}
                    variant="outline"
                    className="text-red-600 hover:text-red-700"
                  >
                    <Trash2 className="w-4 h-4 mr-2" />
                    Remove Custom Banner
                  </Button>
                )}
              </div>
            </CardContent>
          </Card>
        </div>
      )}
    </div>
  )
}