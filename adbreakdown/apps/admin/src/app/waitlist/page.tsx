'use client'

import { useState, useEffect } from 'react'
import { useAuth } from '@/hooks/useAuth'
import { useRouter } from 'next/navigation'

interface WaitlistEntry {
  id: string
  user_id: string
  clerk_id: string
  email: string | null
  requested_at: string
  status: 'pending' | 'approved' | 'rejected'
  notes: string | null
  updated_at: string
}

export default function AdminWaitlistPage() {
  const { isAdmin, loading: authLoading } = useAuth()
  const router = useRouter()
  const [entries, setEntries] = useState<WaitlistEntry[]>([])
  const [loading, setLoading] = useState(true)
  const [updating, setUpdating] = useState<string | null>(null)

  useEffect(() => {
    // Block access if not on admin subdomain
    if (typeof window !== 'undefined' && !window.location.hostname.startsWith('admin.')) {
      router.push('/')
      return
    }

    if (!authLoading && !isAdmin) {
      router.push('/') // Redirect non-admin users
      return
    }
    
    if (isAdmin) {
      fetchWaitlistEntries()
    }
  }, [isAdmin, authLoading, router])

  const fetchWaitlistEntries = async () => {
    try {
      const response = await fetch('/api/admin/waitlist')
      if (response.ok) {
        const { data } = await response.json()
        setEntries(data)
      } else {
        console.error('Failed to fetch waitlist entries')
      }
    } catch (error) {
      console.error('Error fetching waitlist entries:', error)
    } finally {
      setLoading(false)
    }
  }

  const updateStatus = async (entryId: string, status: 'approved' | 'rejected' | 'pending', notes?: string) => {
    setUpdating(entryId)
    try {
      const response = await fetch('/api/admin/waitlist/update', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ entryId, status, notes }),
      })

      if (response.ok) {
        // Refresh the list
        await fetchWaitlistEntries()
      } else {
        console.error('Failed to update waitlist entry')
      }
    } catch (error) {
      console.error('Error updating waitlist entry:', error)
    } finally {
      setUpdating(null)
    }
  }

  const getStatusBadge = (status: string) => {
    const baseClasses = "px-2 py-1 rounded-full text-xs font-medium"
    switch (status) {
      case 'pending':
        return `${baseClasses} bg-yellow-100 text-yellow-800`
      case 'approved':
        return `${baseClasses} bg-green-100 text-green-800`
      case 'rejected':
        return `${baseClasses} bg-red-100 text-red-800`
      default:
        return `${baseClasses} bg-gray-100 text-gray-800`
    }
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  if (authLoading || loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  if (!isAdmin) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-xl font-semibold mb-2">Access Denied</h2>
          <p className="text-gray-600">You don&apos;t have admin permissions.</p>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="bg-white shadow rounded-lg">
          <div className="px-4 py-5 sm:p-6">
            <div className="flex items-center justify-between mb-6">
              <h1 className="text-2xl font-bold text-gray-900">Studio Waitlist Management</h1>
              <button
                onClick={fetchWaitlistEntries}
                className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm font-medium"
                disabled={loading}
              >
                Refresh
              </button>
            </div>

            <div className="mb-4 grid grid-cols-4 gap-4">
              <div className="bg-blue-50 p-4 rounded-lg">
                <div className="text-2xl font-bold text-blue-600">
                  {entries.filter(e => e.status === 'pending').length}
                </div>
                <div className="text-sm text-blue-800">Pending</div>
              </div>
              <div className="bg-green-50 p-4 rounded-lg">
                <div className="text-2xl font-bold text-green-600">
                  {entries.filter(e => e.status === 'approved').length}
                </div>
                <div className="text-sm text-green-800">Approved</div>
              </div>
              <div className="bg-red-50 p-4 rounded-lg">
                <div className="text-2xl font-bold text-red-600">
                  {entries.filter(e => e.status === 'rejected').length}
                </div>
                <div className="text-sm text-red-800">Rejected</div>
              </div>
              <div className="bg-gray-50 p-4 rounded-lg">
                <div className="text-2xl font-bold text-gray-600">{entries.length}</div>
                <div className="text-sm text-gray-800">Total</div>
              </div>
            </div>

            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      User
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Email
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Requested
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Status
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Notes
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Actions
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {entries.map((entry) => (
                    <tr key={entry.id} className="hover:bg-gray-50">
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm font-medium text-gray-900">
                          {entry.email || 'Unknown'}
                        </div>
                        <div className="text-sm text-gray-500">{entry.clerk_id}</div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {entry.email || 'N/A'}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {formatDate(entry.requested_at)}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={getStatusBadge(entry.status)}>
                          {entry.status}
                        </span>
                      </td>
                      <td className="px-6 py-4 text-sm text-gray-900 max-w-xs truncate">
                        {entry.notes || '-'}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                        {entry.status !== 'approved' && (
                          <button
                            onClick={() => updateStatus(entry.id, 'approved', 'Approved by admin')}
                            disabled={updating === entry.id}
                            className="text-green-600 hover:text-green-900 disabled:opacity-50"
                          >
                            {updating === entry.id ? '...' : 'Approve'}
                          </button>
                        )}
                        {entry.status !== 'rejected' && (
                          <button
                            onClick={() => updateStatus(entry.id, 'rejected', 'Rejected by admin')}
                            disabled={updating === entry.id}
                            className="text-red-600 hover:text-red-900 disabled:opacity-50"
                          >
                            {updating === entry.id ? '...' : 'Reject'}
                          </button>
                        )}
                        {entry.status !== 'pending' && (
                          <button
                            onClick={() => updateStatus(entry.id, 'pending', 'Reset to pending')}
                            disabled={updating === entry.id}
                            className="text-yellow-600 hover:text-yellow-900 disabled:opacity-50"
                          >
                            {updating === entry.id ? '...' : 'Reset'}
                          </button>
                        )}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>

            {entries.length === 0 && (
              <div className="text-center py-8">
                <p className="text-gray-500">No waitlist entries found.</p>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}