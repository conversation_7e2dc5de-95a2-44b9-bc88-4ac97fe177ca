import { NextRequest, NextResponse } from 'next/server'
import { auth } from '@clerk/nextjs/server'
import { createServerSupabaseClient } from '@/lib/supabase'
import PromptManager from '@/lib/prompts/promptManager'

// Admin user check (consistent with other admin APIs)
const isAdmin = (userId: string | null) => {
  if (process.env.NODE_ENV === 'development') {
    return true // Allow all users in development
  }
  
  const adminIds = process.env.ADMIN_USER_IDS?.split(',') || []
  return userId ? adminIds.includes(userId) : false
}

// GET /api/admin/prompts - List all prompts
export async function GET() {
  try {
    const { userId } = await auth()
    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }
    
    // Check admin access
    if (!isAdmin(userId)) {
      return NextResponse.json({ error: 'Admin access required' }, { status: 403 })
    }

    const supabase = createServerSupabaseClient()
    const { data: prompts, error } = await supabase
      .from('prompts')
      .select('*')
      .order('updated_at', { ascending: false })

    if (error) {
      return NextResponse.json({ error: error.message }, { status: 500 })
    }

    // Format prompts for better readability
    const formattedPrompts = prompts.map(prompt => ({
      ...prompt,
      content_preview: prompt.content.substring(0, 200) + '...',
      content_lines: prompt.content.split('\n').length,
      last_updated: new Date(prompt.updated_at).toLocaleString()
    }))

    return NextResponse.json({ prompts: formattedPrompts })
  } catch (error) {
    console.error('Admin prompts GET error:', error)
    return NextResponse.json({ error: 'Internal server error', details: error instanceof Error ? error.message : 'Unknown error' }, { status: 500 })
  }
}

// POST /api/admin/prompts - Create or update prompt
export async function POST(req: NextRequest) {
  try {
    console.log('🔍 Admin prompts POST: Starting request processing')
    
    const { userId } = await auth()
    console.log('🔍 Admin prompts POST: Auth result:', { userId: userId ? 'present' : 'missing' })
    
    if (!userId) {
      console.log('❌ Admin prompts POST: No user ID')
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }
    
    // Check admin access
    if (!isAdmin(userId)) {
      console.log('❌ Admin prompts POST: Not admin user:', userId)
      return NextResponse.json({ error: 'Admin access required' }, { status: 403 })
    }

    console.log('🔍 Admin prompts POST: Parsing request body')
    const requestBody = await req.json()
    console.log('🔍 Admin prompts POST: Request body keys:', Object.keys(requestBody))
    
    const { name, content, description } = requestBody

    if (!name || !content) {
      console.log('❌ Admin prompts POST: Missing required fields:', { name: !!name, content: !!content })
      return NextResponse.json({ error: 'Name and content are required' }, { status: 400 })
    }

    console.log('🔍 Admin prompts POST: Calling PromptManager.updatePrompt with:', { name, contentLength: content.length, description })
    const success = await PromptManager.updatePrompt(name, content, description)
    console.log('🔍 Admin prompts POST: PromptManager result:', { success })
    
    if (!success) {
      console.log('❌ Admin prompts POST: PromptManager returned false')
      return NextResponse.json({ error: 'Failed to update prompt' }, { status: 500 })
    }

    console.log('✅ Admin prompts POST: Success!')
    return NextResponse.json({ message: 'Prompt updated successfully' })
  } catch (error) {
    console.error('Admin prompts POST error:', error)
    return NextResponse.json({ error: 'Internal server error', details: error instanceof Error ? error.message : 'Unknown error' }, { status: 500 })
  }
}