import { NextRequest, NextResponse } from 'next/server'
import { auth } from '@clerk/nextjs/server'
import { createServerSupabaseClient } from '@/lib/supabase'

// Admin user check (consistent with other admin APIs)
const isAdmin = (userId: string | null) => {
  if (process.env.NODE_ENV === 'development') {
    return true // Allow all users in development
  }
  
  const adminIds = process.env.ADMIN_USER_IDS?.split(',') || []
  return userId ? adminIds.includes(userId) : false
}

interface RouteParams {
  params: Promise<{
    name: string
  }>
}

// GET /api/admin/prompts/[name] - Get specific prompt with full content
export async function GET(req: NextRequest, { params }: RouteParams) {
  try {
    const { userId } = await auth()
    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }
    
    // Check admin access
    if (!isAdmin(userId)) {
      return NextResponse.json({ error: 'Admin access required' }, { status: 403 })
    }

    const { name } = await params
    const supabase = createServerSupabaseClient()
    
    const { data: prompt, error } = await supabase
      .from('prompts')
      .select('*')
      .eq('name', name)
      .single()

    if (error || !prompt) {
      return NextResponse.json({ error: 'Prompt not found' }, { status: 404 })
    }

    // Format for better display
    const formattedPrompt = {
      ...prompt,
      content_formatted: formatPromptContent(prompt.content),
      content_stats: {
        lines: prompt.content.split('\n').length,
        characters: prompt.content.length,
        words: prompt.content.split(/\s+/).length,
        sections: (prompt.content.match(/###/g) || []).length
      },
      last_updated: new Date(prompt.updated_at).toLocaleString(),
      created: new Date(prompt.created_at).toLocaleString()
    }

    return NextResponse.json({ prompt: formattedPrompt })
  } catch (error) {
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

function formatPromptContent(content: string): string {
  // Add proper indentation and formatting for better readability
  return content
    .split('\n')
    .map(line => {
      // Add proper spacing for headers
      if (line.startsWith('###')) {
        return `\n${line}\n`
      }
      if (line.startsWith('**') && line.endsWith('**')) {
        return `\n${line}`
      }
      return line
    })
    .join('\n')
    .replace(/\n{3,}/g, '\n\n') // Remove excessive line breaks
    .trim()
}