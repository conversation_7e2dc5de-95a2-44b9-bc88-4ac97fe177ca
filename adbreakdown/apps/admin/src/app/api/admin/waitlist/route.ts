import { NextRequest, NextResponse } from 'next/server'
import { auth } from '@clerk/nextjs/server'
import { usageService } from '@/lib/services/usageService'

// Admin user check
const isAdmin = (userId: string | null) => {
  if (process.env.NODE_ENV === 'development') {
    return true // Allow all users in development
  }
  
  const adminIds = process.env.ADMIN_USER_IDS?.split(',') || []
  return userId ? adminIds.includes(userId) : false
}

export async function GET(req: NextRequest) {
  try {
    const { userId } = await auth()
    
    if (!userId || !isAdmin(userId)) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 403 })
    }

    const waitlistEntries = await usageService.getAllWaitlistEntries()

    return NextResponse.json({
      success: true,
      data: waitlistEntries
    })

  } catch (error) {
    console.error('Error fetching waitlist entries:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}