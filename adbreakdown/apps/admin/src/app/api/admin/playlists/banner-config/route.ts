import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@supabase/supabase-js'
import { auth } from '@clerk/nextjs/server'

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!
const supabase = createClient(supabaseUrl, supabaseServiceKey)

// Admin email check for development/production
const isAdmin = (userId: string | null) => {
  if (process.env.NODE_ENV === 'development') {
    return true // Allow all users in development
  }
  
  // In production, check against admin user IDs or implement proper admin check
  const adminIds = process.env.ADMIN_USER_IDS?.split(',') || []
  return userId ? adminIds.includes(userId) : false
}

export interface BannerConfig {
  id?: string
  playlist_tag: string
  title: string
  subtitle: string
  background_gradient: string
  background_color?: string
  text_color?: string
  features?: string[]
  is_active: boolean
  created_at?: string
  updated_at?: string
  // Enhanced color options
  heading_color_type?: 'gradient' | 'solid'
  heading_solid_color?: string
  heading_gradient_start?: string
  heading_gradient_middle?: string
  heading_gradient_end?: string
  body_text_color?: string
  background_type?: 'gradient' | 'solid'
  background_gradient_start?: string
  background_gradient_middle?: string
  background_gradient_end?: string
}

// GET - Retrieve banner configuration for a specific playlist tag
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const playlistTag = searchParams.get('playlist_tag')
    
    if (!playlistTag) {
      return NextResponse.json({ error: 'playlist_tag parameter is required' }, { status: 400 })
    }

    const { data, error } = await supabase
      .from('playlist_banner_configs')
      .select('*')
      .eq('playlist_tag', playlistTag)
      .eq('is_active', true)
      .single()

    if (error && error.code !== 'PGRST116') { // PGRST116 is "not found" error
      throw new Error(error.message)
    }

    return NextResponse.json({ banner_config: data })

  } catch (error) {
    console.error('Error fetching banner config:', error)
    return NextResponse.json(
      { error: 'Failed to fetch banner config' },
      { status: 500 }
    )
  }
}

// POST - Create or update banner configuration
export async function POST(request: NextRequest) {
  try {
    const { userId } = await auth()
    
    if (!isAdmin(userId)) {
      return NextResponse.json({ error: 'Admin access required' }, { status: 403 })
    }

    const bannerConfig: BannerConfig = await request.json()

    if (!bannerConfig.playlist_tag || !bannerConfig.title) {
      return NextResponse.json({ 
        error: 'playlist_tag and title are required' 
      }, { status: 400 })
    }

    // Check if banner config already exists
    const { data: existing } = await supabase
      .from('playlist_banner_configs')
      .select('id')
      .eq('playlist_tag', bannerConfig.playlist_tag)
      .single()

    let result
    if (existing) {
      // Update existing banner config
      result = await supabase
        .from('playlist_banner_configs')
        .update({
          title: bannerConfig.title,
          subtitle: bannerConfig.subtitle || '',
          background_gradient: bannerConfig.background_gradient || 'from-indigo-50 via-purple-50 to-blue-50',
          background_color: bannerConfig.background_color || '#ffffff',
          text_color: bannerConfig.text_color || 'text-gray-700',
          features: bannerConfig.features || [],
          is_active: bannerConfig.is_active,
          // Enhanced color options
          heading_color_type: bannerConfig.heading_color_type || 'gradient',
          heading_solid_color: bannerConfig.heading_solid_color || '#1f2937',
          heading_gradient_start: bannerConfig.heading_gradient_start || '#4338ca',
          heading_gradient_middle: bannerConfig.heading_gradient_middle || '#7c3aed',
          heading_gradient_end: bannerConfig.heading_gradient_end || '#2563eb',
          body_text_color: bannerConfig.body_text_color || '#374151',
          background_type: bannerConfig.background_type || 'gradient',
          background_gradient_start: bannerConfig.background_gradient_start || '#eef2ff',
          background_gradient_middle: bannerConfig.background_gradient_middle || '#f3e8ff',
          background_gradient_end: bannerConfig.background_gradient_end || '#eff6ff',
          updated_at: new Date().toISOString()
        })
        .eq('playlist_tag', bannerConfig.playlist_tag)
        .select()
        .single()
    } else {
      // Create new banner config
      result = await supabase
        .from('playlist_banner_configs')
        .insert({
          playlist_tag: bannerConfig.playlist_tag,
          title: bannerConfig.title,
          subtitle: bannerConfig.subtitle || '',
          background_gradient: bannerConfig.background_gradient || 'from-indigo-50 via-purple-50 to-blue-50',
          background_color: bannerConfig.background_color || '#ffffff',
          text_color: bannerConfig.text_color || 'text-gray-700',
          features: bannerConfig.features || [],
          is_active: bannerConfig.is_active,
          // Enhanced color options
          heading_color_type: bannerConfig.heading_color_type || 'gradient',
          heading_solid_color: bannerConfig.heading_solid_color || '#1f2937',
          heading_gradient_start: bannerConfig.heading_gradient_start || '#4338ca',
          heading_gradient_middle: bannerConfig.heading_gradient_middle || '#7c3aed',
          heading_gradient_end: bannerConfig.heading_gradient_end || '#2563eb',
          body_text_color: bannerConfig.body_text_color || '#374151',
          background_type: bannerConfig.background_type || 'gradient',
          background_gradient_start: bannerConfig.background_gradient_start || '#eef2ff',
          background_gradient_middle: bannerConfig.background_gradient_middle || '#f3e8ff',
          background_gradient_end: bannerConfig.background_gradient_end || '#eff6ff',
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        })
        .select()
        .single()
    }

    if (result.error) {
      throw new Error(result.error.message)
    }

    return NextResponse.json({
      success: true,
      message: existing ? 'Banner configuration updated' : 'Banner configuration created',
      banner_config: result.data
    })

  } catch (error) {
    console.error('Error saving banner config:', error)
    return NextResponse.json(
      { error: 'Failed to save banner configuration' },
      { status: 500 }
    )
  }
}

// DELETE - Remove banner configuration
export async function DELETE(request: NextRequest) {
  try {
    const { userId } = await auth()
    
    if (!isAdmin(userId)) {
      return NextResponse.json({ error: 'Admin access required' }, { status: 403 })
    }

    const { searchParams } = new URL(request.url)
    const playlistTag = searchParams.get('playlist_tag')

    if (!playlistTag) {
      return NextResponse.json({ error: 'playlist_tag parameter is required' }, { status: 400 })
    }

    const { error } = await supabase
      .from('playlist_banner_configs')
      .delete()
      .eq('playlist_tag', playlistTag)

    if (error) {
      throw new Error(error.message)
    }

    return NextResponse.json({
      success: true,
      message: 'Banner configuration deleted'
    })

  } catch (error) {
    console.error('Error deleting banner config:', error)
    return NextResponse.json(
      { error: 'Failed to delete banner configuration' },
      { status: 500 }
    )
  }
}