import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@supabase/supabase-js'
import { auth } from '@clerk/nextjs/server'

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!
const supabase = createClient(supabaseUrl, supabaseServiceKey)

// Admin email check for development/production
const isAdmin = (userId: string | null) => {
  if (process.env.NODE_ENV === 'development') {
    return true // Allow all users in development
  }
  
  // In production, check against admin user IDs or implement proper admin check
  const adminIds = process.env.ADMIN_USER_IDS?.split(',') || []
  return userId ? adminIds.includes(userId) : false
}

export async function POST(request: NextRequest) {
  try {
    const { userId } = await auth()
    
    if (!isAdmin(userId)) {
      return NextResponse.json({ error: 'Admin access required' }, { status: 403 })
    }

    const { oldTag, newTag } = await request.json()

    if (!oldTag || !newTag) {
      return NextResponse.json({ error: 'Both oldTag and newTag are required' }, { status: 400 })
    }

    // Clean the new tag
    const cleanNewTag = newTag.trim().toLowerCase()
    
    if (cleanNewTag === oldTag) {
      return NextResponse.json({ error: 'New tag must be different from old tag' }, { status: 400 })
    }

    // Get all analyses with the old tag
    const { data: analyses, error: fetchError } = await supabase
      .from('ad_analyses')
      .select('id, collection_tags')
      .contains('collection_tags', [oldTag])

    if (fetchError) {
      throw new Error(fetchError.message)
    }

    if (!analyses || analyses.length === 0) {
      return NextResponse.json({ error: 'No analyses found with this tag' }, { status: 404 })
    }

    // Update each analysis to replace the old tag with the new tag
    const updatePromises = analyses.map(analysis => {
      const updatedTags = analysis.collection_tags.map((tag: string) => 
        tag === oldTag ? cleanNewTag : tag
      )

      return supabase
        .from('ad_analyses')
        .update({ 
          collection_tags: updatedTags,
          updated_at: new Date().toISOString()
        })
        .eq('id', analysis.id)
    })

    const results = await Promise.all(updatePromises)
    
    // Check for any errors
    const errors = results.filter(result => result.error)
    if (errors.length > 0) {
      throw new Error(`Failed to update ${errors.length} analyses`)
    }

    return NextResponse.json({
      success: true,
      message: `Playlist tag "${oldTag}" renamed to "${cleanNewTag}" in ${analyses.length} analyses`,
      updatedCount: analyses.length
    })

  } catch (error) {
    console.error('Error renaming playlist tag:', error)
    return NextResponse.json(
      { error: 'Failed to rename playlist tag' },
      { status: 500 }
    )
  }
}