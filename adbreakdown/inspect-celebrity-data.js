#!/usr/bin/env node

const { createClient } = require('@supabase/supabase-js')
require('dotenv').config()

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing required environment variables')
  process.exit(1)
}

const supabase = createClient(supabaseUrl, supabaseServiceKey)

async function inspectCelebrityData() {
  console.log('🔍 Inspecting celebrity data in database...')
  
  try {
    // Get some sample celebrity data to see the actual format
    const { data: analyses, error } = await supabase
      .from('ad_analyses')
      .select('id, celebrity, celebrity_names, celebrity_context')
      .not('celebrity', 'is', null)
      .limit(5)
    
    if (error) {
      throw new Error(`Failed to fetch data: ${error.message}`)
    }
    
    console.log(`\n📊 Found ${analyses.length} analyses with celebrity data`)
    console.log('=' .repeat(80))
    
    analyses.forEach((analysis, i) => {
      console.log(`\n${i + 1}. Analysis ID: ${analysis.id}`)
      console.log(`   celebrity: ${JSON.stringify(analysis.celebrity)}`)
      console.log(`   celebrity (type): ${typeof analysis.celebrity}`)
      console.log(`   celebrity_names: ${JSON.stringify(analysis.celebrity_names)}`)
      console.log(`   celebrity_names (type): ${typeof analysis.celebrity_names}`)
      console.log(`   celebrity_context: ${JSON.stringify(analysis.celebrity_context)}`)
      
      if (analysis.celebrity) {
        console.log(`   Raw celebrity data: "${analysis.celebrity}"`)
        console.log(`   Length: ${analysis.celebrity.length}`)
        console.log(`   Starts with [": ${analysis.celebrity.startsWith('["')}`)
        console.log(`   Ends with "]: ${analysis.celebrity.endsWith('"]')}`)
        
        // Test our regex against this actual data
        const arrayLikeMatch = analysis.celebrity.trim().match(/^\["([^"]+)"\]$/)
        console.log(`   Matches array pattern: ${!!arrayLikeMatch}`)
        if (arrayLikeMatch) {
          console.log(`   Would extract: "${arrayLikeMatch[1]}"`)
          const names = arrayLikeMatch[1].split(',').map(n => n.trim())
          console.log(`   Would become: ${JSON.stringify(names)}`)
        }
      }
    })
    
    // Also check for specific patterns
    console.log('\n' + '='.repeat(80))
    console.log('🔍 Searching for specific malformed patterns...')
    
    const { data: malformed, error: malformedError } = await supabase
      .from('ad_analyses')
      .select('id, celebrity')
      .ilike('celebrity', '["*"]')
      .limit(10)
    
    if (malformedError) {
      console.log('Could not search for malformed patterns:', malformedError.message)
    } else {
      console.log(`Found ${malformed.length} records with ["..."] pattern:`)
      malformed.forEach((record, i) => {
        console.log(`${i + 1}. ${record.id}: ${record.celebrity}`)
      })
    }
    
  } catch (error) {
    console.error('❌ Inspection failed:', error.message)
  }
}

inspectCelebrityData()