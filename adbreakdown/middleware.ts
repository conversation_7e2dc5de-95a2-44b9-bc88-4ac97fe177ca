import { NextResponse } from 'next/server'
import type { NextRequest } from 'next/server'

export function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl
  const hostname = request.headers.get('host') || ''

  // If on admin subdomain
  if (hostname.startsWith('admin.')) {
    // Allow admin routes and their API endpoints
    if (
      pathname.startsWith('/admin') ||
      pathname.startsWith('/api/admin') ||
      pathname.startsWith('/_next') ||
      pathname.startsWith('/favicon') ||
      pathname === '/sitemap.xml' ||
      pathname === '/robots.txt'
    ) {
      return NextResponse.next()
    }

    // Redirect root to admin waitlist
    if (pathname === '/') {
      return NextResponse.redirect(new URL('/admin/waitlist', request.url))
    }

    // Redirect all other routes to main site
    const mainUrl = new URL(pathname, request.url)
    mainUrl.hostname = hostname.replace('admin.', '')
    return NextResponse.redirect(mainUrl)
  }

  // On main site - completely block admin routes (security measure)
  if (pathname.startsWith('/admin') && !hostname.startsWith('admin.')) {
    return new NextResponse('Not Found', { status: 404 })
  }

  // Block admin API routes on main site
  if (pathname.startsWith('/api/admin') && !hostname.startsWith('admin.')) {
    return new NextResponse('Not Found', { status: 404 })
  }

  return NextResponse.next()
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - api (API routes)
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     */
    '/((?!_next/static|_next/image|favicon.ico).*)',
  ],
}