const { createClient } = require('@supabase/supabase-js')
const fs = require('fs')

const supabaseUrl = 'https://elossghirdivbobfycob.supabase.co'
const supabaseServiceKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImVsb3NzZ2hpcmRpdmJvYmZ5Y29iIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1MDkxOTM1MCwiZXhwIjoyMDY2NDk1MzUwfQ.xbxYhEBpCLSdihJ98jFrsLDHR7wHR5ZtGXqaGWXJFn8'

const supabase = createClient(supabaseUrl, supabaseServiceKey)

async function applyTriggerFix() {
  try {
    console.log('📦 Applying trigger fix...')
    
    // Read the SQL fix
    const sql = fs.readFileSync('./database/migrations/V24_fix_celebrity_context_variable.sql', 'utf8')
    
    // Apply the fix
    const { data, error } = await supabase.rpc('exec_sql', { sql_query: sql })
    
    if (error) {
      console.error('❌ Error applying trigger fix:', error)
      return
    }
    
    console.log('✅ Trigger fix applied successfully!')
    console.log('📋 Result:', data)
    
  } catch (error) {
    console.error('❌ Script error:', error)
  }
}

applyTriggerFix()