const { createClient } = require('@supabase/supabase-js')

const supabaseUrl = 'https://elossghirdivbobfycob.supabase.co'
const supabaseServiceKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImVsb3NzZ2hpcmRpdmJvYmZ5Y29iIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1MDkxOTM1MCwiZXhwIjoyMDY2NDk1MzUwfQ.xbxYhEBpCLSdihJ98jFrsLDHR7wHR5ZtGXqaGWXJFn8'

const supabase = createClient(supabaseUrl, supabaseServiceKey)

async function fixTrigger() {
  try {
    console.log('🔧 Fixing celebrity_context_val variable scope...')
    
    const { data, error } = await supabase
      .from('ad_analyses')
      .select('id')
      .limit(1)
    
    if (error) {
      console.error('❌ Connection test failed:', error)
      return
    }
    
    console.log('✅ Connection test passed')
    
    // The function fix will be applied via the SQL file later
    // For now, just test the issue by checking if any analysis is failing
    const { data: failedAnalyses, error: queryError } = await supabase
      .from('ad_analyses')
      .select('id, status, created_at')
      .eq('status', 'pending')
      .order('created_at', { ascending: false })
      .limit(5)
    
    if (queryError) {
      console.error('❌ Query error:', queryError)
      return
    }
    
    console.log('📊 Recent pending analyses:', failedAnalyses)
    
  } catch (error) {
    console.error('❌ Script error:', error)
  }
}

fixTrigger()