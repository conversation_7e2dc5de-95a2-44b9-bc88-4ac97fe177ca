-- SIMPLIFIED MIGRATION: Usage Tracking and Studio Waitlist
-- Run this in your Supabase SQL Editor

-- 1. Add tracking columns (for reference/analytics, not restrictions)
ALTER TABLE profiles 
ADD COLUMN IF NOT EXISTS analysis_count INTEGER DEFAULT 0,
ADD COLUMN IF NOT EXISTS library_views_count INTEGER DEFAULT 0,
ADD COLUMN IF NOT EXISTS prelaunch_analysis_count INTEGER DEFAULT 0,
ADD COLUMN IF NOT EXISTS usage_reset_date TIMESTAMP WITH TIME ZONE DEFAULT (NOW() + INTERVAL '1 month');

-- 2. Create studio waitlist table
CREATE TABLE IF NOT EXISTS studio_waitlist (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
  clerk_id TEXT NOT NULL,
  email TEXT,
  requested_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'approved', 'rejected')),
  notes TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(user_id)
);

-- 3. Create indexes for faster lookups
CREATE INDEX IF NOT EXISTS idx_studio_waitlist_user_id ON studio_waitlist(user_id);
CREATE INDEX IF NOT EXISTS idx_studio_waitlist_clerk_id ON studio_waitlist(clerk_id);
CREATE INDEX IF NOT EXISTS idx_studio_waitlist_status ON studio_waitlist(status);

-- 4. Create function to reset usage counts monthly (for analytics/reference)
CREATE OR REPLACE FUNCTION reset_monthly_usage() 
RETURNS void AS $$
BEGIN
  UPDATE profiles 
  SET 
    analysis_count = 0,
    library_views_count = 0,
    prelaunch_analysis_count = 0,
    usage_reset_date = NOW() + INTERVAL '1 month'
  WHERE usage_reset_date <= NOW();
END;
$$ LANGUAGE plpgsql;

-- 5. Add helpful comments
COMMENT ON COLUMN profiles.analysis_count IS 'Monthly count of analyses created by user (for reference only)';
COMMENT ON COLUMN profiles.library_views_count IS 'Monthly count of ad library individual views (for testing/analytics)';
COMMENT ON COLUMN profiles.prelaunch_analysis_count IS 'Monthly count of pre-launch analyses created (for reference only)';
COMMENT ON COLUMN profiles.usage_reset_date IS 'Date when usage counts will reset';
COMMENT ON TABLE studio_waitlist IS 'Waitlist for studio access requests';
COMMENT ON FUNCTION reset_monthly_usage() IS 'Resets monthly usage counts for analytics';

-- 6. Create functions for atomic usage count increments (for tracking only)
CREATE OR REPLACE FUNCTION increment_analysis_count(p_user_id UUID)
RETURNS integer LANGUAGE sql AS $$
  WITH updated AS (
    UPDATE profiles
    SET analysis_count = analysis_count + 1, updated_at = NOW()
    WHERE user_id = p_user_id
    RETURNING analysis_count
  )
  SELECT analysis_count FROM updated;
$$;

CREATE OR REPLACE FUNCTION increment_library_views_count(p_user_id UUID)
RETURNS integer LANGUAGE sql AS $$
  WITH updated AS (
    UPDATE profiles
    SET library_views_count = library_views_count + 1, updated_at = NOW()
    WHERE user_id = p_user_id
    RETURNING library_views_count
  )
  SELECT library_views_count FROM updated;
$$;

CREATE OR REPLACE FUNCTION increment_prelaunch_analysis_count(p_user_id UUID)
RETURNS integer LANGUAGE sql AS $$
  WITH updated AS (
    UPDATE profiles
    SET prelaunch_analysis_count = prelaunch_analysis_count + 1, updated_at = NOW()
    WHERE user_id = p_user_id
    RETURNING prelaunch_analysis_count
  )
  SELECT prelaunch_analysis_count FROM updated;
$$;