{"rewrites": [{"source": "/", "destination": "/admin/waitlist"}, {"source": "/waitlist", "destination": "/admin/waitlist"}, {"source": "/playlists", "destination": "/admin/playlists"}, {"source": "/prompts", "destination": "/admin/prompts"}], "redirects": [{"source": "/studio/:path*", "destination": "https://breakdown.ad/studio/:path*", "permanent": false}, {"source": "/ad-library/:path*", "destination": "https://breakdown.ad/ad-library/:path*", "permanent": false}, {"source": "/ad/:path*", "destination": "https://breakdown.ad/ad/:path*", "permanent": false}], "headers": [{"source": "/(.*)", "headers": [{"key": "X-Frame-Options", "value": "DENY"}, {"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "Strict-Transport-Security", "value": "max-age=31536000; includeSubDomains"}, {"key": "Referrer-Policy", "value": "strict-origin-when-cross-origin"}]}]}