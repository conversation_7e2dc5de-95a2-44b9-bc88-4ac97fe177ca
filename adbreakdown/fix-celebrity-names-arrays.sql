-- Fix Celebrity Names Arrays in PostgreSQL
-- This fixes the celebrity_names column that was created from celebrity text column
-- and ended up with malformed arrays like ["Name1, Name2, Name3"] instead of ["Name1", "Name2", "Name3"]

BEGIN;

-- Step 1: Check current state
SELECT 
  id, 
  celebrity, 
  celebrity_names, 
  array_length(celebrity_names, 1) as array_length
FROM ad_analyses 
WHERE celebrity_names IS NOT NULL 
LIMIT 5;

-- Step 2: Fix malformed celebrity_names arrays
-- Handle cases where celebrity_names contains single elements with comma-separated names
UPDATE ad_analyses 
SET celebrity_names = (
  SELECT ARRAY(
    SELECT TRIM(name) 
    FROM unnest(
      string_to_array(
        -- If the first element contains commas, split it
        CASE 
          WHEN array_length(celebrity_names, 1) = 1 AND celebrity_names[1] LIKE '%,%' 
          THEN celebrity_names[1]
          ELSE array_to_string(celebrity_names, ', ')
        END,
        ','
      )
    ) AS name
    WHERE TRIM(name) != '' 
    AND TRIM(name) NOT IN ('Unknown', 'N/A', 'null', 'NULL')
  )
)
WHERE celebrity_names IS NOT NULL 
AND (
  -- Fix cases where we have a single array element containing comma-separated names
  array_length(celebrity_names, 1) = 1 
  AND celebrity_names[1] LIKE '%,%'
  -- Also fix cases where multiple elements were incorrectly combined
  OR array_length(celebrity_names, 1) > 1 
  AND EXISTS (
    SELECT 1 FROM unnest(celebrity_names) AS elem 
    WHERE elem LIKE '%,%'
  )
);

-- Step 3: Also parse celebrity strings that contain multiple names and populate celebrity_names
-- For records that don't have celebrity_names but have celebrity with multiple names
UPDATE ad_analyses 
SET celebrity_names = (
  SELECT ARRAY(
    SELECT TRIM(name) 
    FROM unnest(string_to_array(celebrity, ',')) AS name
    WHERE TRIM(name) != '' 
    AND TRIM(name) NOT IN ('Unknown', 'N/A', 'null', 'NULL')
    AND TRIM(name) NOT LIKE '%(%'  -- Skip names with context in parentheses for now
  )
),
celebrity_context = (
  CASE 
    WHEN celebrity LIKE '%(%' THEN celebrity
    ELSE NULL
  END
)
WHERE celebrity_names IS NULL 
AND celebrity IS NOT NULL
AND celebrity LIKE '%,%'  -- Only process multi-name celebrity strings
AND celebrity NOT LIKE '%(%';  -- Skip context cases for this simple update

-- Step 4: Verify the changes
SELECT 
  'After Fix' as status,
  id, 
  celebrity, 
  celebrity_names, 
  celebrity_context,
  array_length(celebrity_names, 1) as array_length
FROM ad_analyses 
WHERE celebrity_names IS NOT NULL 
ORDER BY array_length(celebrity_names, 1) DESC
LIMIT 10;

-- Step 5: Show statistics
SELECT 
  'Statistics' as info,
  COUNT(*) as total_records,
  COUNT(celebrity_names) as records_with_celebrity_names,
  COUNT(CASE WHEN array_length(celebrity_names, 1) > 1 THEN 1 END) as records_with_multiple_celebrities,
  MAX(array_length(celebrity_names, 1)) as max_celebrities_in_one_record
FROM ad_analyses;

COMMIT;