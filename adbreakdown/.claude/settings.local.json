{"permissions": {"allow": ["Bash(npm run build:*)", "Bash(find:*)", "Bash(rm:*)", "Bash(ls:*)", "<PERSON><PERSON>(mv:*)", "Bash(rg:*)", "Bash(grep:*)", "Bash(npm install:*)", "Bash(npm run type-check:*)", "Bash(npm run dev:*)", "Bash(node:*)", "<PERSON><PERSON>(npm show:*)", "Bash(npm view:*)", "Bash(git add:*)", "Bash(git commit:*)", "Bash(git push:*)", "WebFetch(domain:crayo.ai)", "<PERSON><PERSON>(mkdir:*)", "mcp__ide__getDiagnostics", "<PERSON><PERSON>(curl:*)", "<PERSON><PERSON>(pkill:*)", "<PERSON><PERSON>(true)", "Bash(npx shadcn@latest add:*)", "Bash(psql:*)", "Bash(brew services:*)", "<PERSON>sh(redis-server:*)", "Bash(redis-cli:*)", "WebFetch(domain:www.ideabrowser.com)", "Bash(npm run lint:*)", "<PERSON><PERSON>(timeout:*)", "<PERSON><PERSON>(sed:*)", "Bash(git checkout:*)", "Bash(git restore:*)", "Bash(supabase db pull:*)", "Bash(supabase db dump:*)", "Bash(cp:*)", "<PERSON><PERSON>(chmod:*)", "<PERSON><PERSON>(diff:*)", "Bash(npx supabase functions deploy:*)", "<PERSON><PERSON>(python3:*)", "WebFetch(domain:cloud.google.com)", "Bash(npx tsc:*)", "Bash(NEXT_PUBLIC_USE_VERTEX_AI=false node -e \"\nconst featureFlags = {\n  useVertexAi: process.env.NEXT_PUBLIC_USE_VERTEX_AI === ''false'',\n};\nconsole.log(''🔧 With env var set to \"\"false\"\":'', featureFlags.useVertexAi);\n\")", "Bash(NEXT_PUBLIC_USE_VERTEX_AI=true node -e \"\nconst featureFlags = {\n  useVertexAi: process.env.NEXT_PUBLIC_USE_VERTEX_AI === ''false'',\n};\nconsole.log(''🔧 With env var set to \"\"true\"\":'', featureFlags.useVertexAi);\n\")", "Bash(kill:*)", "WebFetch(domain:developers.google.com)", "<PERSON>sh(gsutil cors set:*)", "WebFetch(domain:ai-sdk.dev)", "WebFetch(domain:ai.google.dev)", "WebFetch(domain:www.breakdown.ad)", "WebFetch(domain:docs.copilotkit.ai)", "WebFetch(domain:github.com)", "WebFetch(domain:www.livemint.com)", "Bash(npm ls:*)", "Bash(npx next lint:*)", "Bash(npx next build:*)", "WebFetch(domain:www.imdb.com)", "Bash(npx supabase db dump:*)", "Bash(npx supabase migration:*)", "Bash(NEXT_PUBLIC_SUPABASE_URL=https://elossghirdivbobfycob.supabase.co SUPABASE_SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImVsb3NzZ2hpcmRpdmJvYmZ5Y29iIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1MDkxOTM1MCwiZXhwIjoyMDY2NDk1MzUwfQ.xbxYhEBpCLSdihJ98jFrsLDHR7wHR5ZtGXqaGWXJFn8 node verify-columns.js)", "Bash(NEXT_PUBLIC_SUPABASE_URL=https://elossghirdivbobfycob.supabase.co SUPABASE_SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImVsb3NzZ2hpcmRpdmJvYmZ5Y29iIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1MDkxOTM1MCwiZXhwIjoyMDY2NDk1MzUwfQ.xbxYhEBpCLSdihJ98jFrsLDHR7wHR5ZtGXqaGWXJFn8 node test-trigger.js)", "Bash(npx supabase:*)", "Bash(NEXT_PUBLIC_SUPABASE_URL=https://elossghirdivbobfycob.supabase.co SUPABASE_SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImVsb3NzZ2hpcmRpdmJvYmZ5Y29iIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1MDkxOTM1MCwiZXhwIjoyMDY2NDk1MzUwfQ.xbxYhEBpCLSdihJ98jFrsLDHR7wHR5ZtGXqaGWXJFn8 psql \"postgresql://postgres:<EMAIL>:5432/postgres\" -f database/migrations/V24_fix_celebrity_context_variable.sql)", "Bash(NEXT_PUBLIC_SUPABASE_URL=https://elossghirdivbobfycob.supabase.co SUPABASE_SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImVsb3NzZ2hpcmRpdmJvYmZ5Y29iIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1MDkxOTM1MCwiZXhwIjoyMDY2NDk1MzUwfQ.xbxYhEBpCLSdihJ98jFrsLDHR7wHR5ZtGXqaGWXJFn8 psql \"postgresql://postgres:<EMAIL>:5432/postgres\" -f database/migrations/V26_add_gut_reaction_column.sql)", "mcp__shadcn-ui-server__get_component_details", "Bash(NEXT_PUBLIC_SUPABASE_URL=https://elossghirdivbobfycob.supabase.co SUPABASE_SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImVsb3NzZ2hpcmRpdmJvYmZ5Y29iIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1MDkxOTM1MCwiZXhwIjoyMDY2NDk1MzUwfQ.xbxYhEBpCLSdihJ98jFrsLDHR7wHR5ZtGXqaGWXJFn8 psql \"postgresql://postgres:<EMAIL>:5432/postgres\" -f database/migrations/V28_add_playlist_banner_configs.sql)", "Bash(NEXT_PUBLIC_SUPABASE_URL=https://elossghirdivbobfycob.supabase.co SUPABASE_SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImVsb3NzZ2hpcmRpdmJvYmZ5Y29iIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1MDkxOTM1MCwiZXhwIjoyMDY2NDk1MzUwfQ.xbxYhEBpCLSdihJ98jFrsLDHR7wHR5ZtGXqaGWXJFn8 psql \"postgresql://postgres:<EMAIL>:5432/postgres\" -f database/migrations/V29_add_background_color_to_banner_configs.sql)", "Bash(NEXT_PUBLIC_SUPABASE_URL=https://elossghirdivbobfycob.supabase.co SUPABASE_SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImVsb3NzZ2hpcmRpdmJvYmZ5Y29iIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1MDkxOTM1MCwiZXhwIjoyMDY2NDk1MzUwfQ.xbxYhEBpCLSdihJ98jFrsLDHR7wHR5ZtGXqaGWXJFn8 psql \"postgresql://postgres:<EMAIL>:5432/postgres\" -f database/migrations/V31_create_analysis_queue_system.sql)"], "deny": []}}