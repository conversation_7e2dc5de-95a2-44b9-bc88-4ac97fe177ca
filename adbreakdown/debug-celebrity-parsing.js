// Debug script to see exactly what's happening with celebrity parsing

function fixMalformedCelebrityArray(celebrityData) {
  console.log('Input:', celebrityData)
  console.log('Type:', typeof celebrityData)
  
  if (!celebrityData || typeof celebrityData !== 'string') {
    console.log('→ Returning null (not string or empty)')
    return null
  }

  // Check for malformed array-like string: ["Name1, Name2, Name3"]
  const arrayLikeMatch = celebrityData.trim().match(/^\["([^"]+)"\]$/)
  console.log('Array-like match:', arrayLikeMatch)
  
  if (arrayLikeMatch) {
    const namesString = arrayLikeMatch[1]
    console.log('Names string extracted:', namesString)
    
    const names = namesString
      .split(',')
      .map(name => name.trim())
      .filter(name => name && name !== 'Unknown' && name !== 'N/A' && name !== '')
    
    console.log('Final names array:', names)
    console.log('Array length:', names.length)
    
    return names.length > 0 ? names : null
  }

  console.log('→ No array-like match found')
  return null
}

// Test with your exact data
const testInput = '["<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>oin <PERSON>"]'

console.log('='.repeat(80))
console.log('Testing malformed celebrity array parsing:')
console.log('='.repeat(80))

const result = fixMalformedCelebrityArray(testInput)

console.log('\n' + '='.repeat(80))
console.log('Final result:', JSON.stringify(result, null, 2))
console.log('Is this an array?', Array.isArray(result))
if (Array.isArray(result)) {
  console.log('Number of elements:', result.length)
  result.forEach((name, i) => {
    console.log(`  ${i + 1}. "${name}"`)
  })
}