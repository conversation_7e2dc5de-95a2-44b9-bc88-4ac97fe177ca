-- Add enhanced color options to playlist_banner_configs table

-- Heading colors (gradient or solid)
ALTER TABLE playlist_banner_configs 
ADD COLUMN heading_color_type TEXT DEFAULT 'gradient' CHECK (heading_color_type IN ('gradient', 'solid'));

ALTER TABLE playlist_banner_configs 
ADD COLUMN heading_solid_color TEXT DEFAULT '#1f2937';

ALTER TABLE playlist_banner_configs 
ADD COLUMN heading_gradient_start TEXT DEFAULT '#4338ca';

ALTER TABLE playlist_banner_configs 
ADD COLUMN heading_gradient_middle TEXT DEFAULT '#7c3aed';

ALTER TABLE playlist_banner_configs 
ADD COLUMN heading_gradient_end TEXT DEFAULT '#2563eb';

-- Body text color
ALTER TABLE playlist_banner_configs 
ADD COLUMN body_text_color TEXT DEFAULT '#374151';

-- Background options (gradient or solid)
ALTER TABLE playlist_banner_configs 
ADD COLUMN background_type TEXT DEFAULT 'gradient' CHECK (background_type IN ('gradient', 'solid'));

ALTER TABLE playlist_banner_configs 
ADD COLUMN background_gradient_start TEXT DEFAULT '#eef2ff';

ALTER TABLE playlist_banner_configs 
ADD COLUMN background_gradient_middle TEXT DEFAULT '#f3e8ff';

ALTER TABLE playlist_banner_configs 
ADD COLUMN background_gradient_end TEXT DEFAULT '#eff6ff';

-- Add comments for the new columns
COMMENT ON COLUMN playlist_banner_configs.heading_color_type IS 'Type of heading color: gradient or solid';
COMMENT ON COLUMN playlist_banner_configs.heading_solid_color IS 'Solid color for heading text';
COMMENT ON COLUMN playlist_banner_configs.heading_gradient_start IS 'Start color for heading gradient';
COMMENT ON COLUMN playlist_banner_configs.heading_gradient_middle IS 'Middle color for heading gradient';
COMMENT ON COLUMN playlist_banner_configs.heading_gradient_end IS 'End color for heading gradient';
COMMENT ON COLUMN playlist_banner_configs.body_text_color IS 'Color for body text';
COMMENT ON COLUMN playlist_banner_configs.background_type IS 'Type of background: gradient or solid';
COMMENT ON COLUMN playlist_banner_configs.background_gradient_start IS 'Start color for background gradient';
COMMENT ON COLUMN playlist_banner_configs.background_gradient_middle IS 'Middle color for background gradient';
COMMENT ON COLUMN playlist_banner_configs.background_gradient_end IS 'End color for background gradient';