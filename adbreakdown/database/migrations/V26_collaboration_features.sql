-- V26: Real-time Collaboration Features
-- Enhanced collaboration features for campaign management

-- Real-time editing sessions
CREATE TABLE IF NOT EXISTS public.campaign_editing_sessions (
  id uuid NOT NULL DEFAULT uuid_generate_v4(),
  campaign_id uuid NOT NULL REFERENCES public.campaigns(id) ON DELETE CASCADE,
  asset_id uuid REFERENCES public.campaign_assets(id) ON DELETE CASCADE,
  user_id uuid NOT NULL REFERENCES public.users(id) ON DELETE CASCADE,
  
  -- Session Details
  session_type text NOT NULL CHECK (session_type IN ('script', 'brief', 'storyboard', 'general')),
  is_active boolean DEFAULT true,
  cursor_position integer,
  selection_start integer,
  selection_end integer,
  
  -- Real-time Data
  last_activity timestamp with time zone DEFAULT now(),
  heartbeat timestamp with time zone DEFAULT now(),
  
  -- Metadata
  metadata jsonb,
  
  -- Timestamps
  created_at timestamp with time zone DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now(),
  
  CONSTRAINT campaign_editing_sessions_pkey PRIMARY KEY (id)
);

-- Campaign activity feed
CREATE TABLE IF NOT EXISTS public.campaign_activities (
  id uuid NOT NULL DEFAULT uuid_generate_v4(),
  campaign_id uuid NOT NULL REFERENCES public.campaigns(id) ON DELETE CASCADE,
  user_id uuid NOT NULL REFERENCES public.users(id) ON DELETE CASCADE,
  
  -- Activity Details
  activity_type text NOT NULL CHECK (activity_type IN (
    'campaign_created', 'campaign_updated', 'status_changed', 'stage_changed',
    'asset_uploaded', 'asset_updated', 'asset_approved', 'asset_rejected',
    'comment_added', 'team_member_added', 'team_member_removed',
    'workflow_updated', 'ai_suggestion', 'deadline_approaching'
  )),
  title text NOT NULL,
  description text,
  
  -- Context
  asset_id uuid REFERENCES public.campaign_assets(id),
  stage_id uuid REFERENCES public.campaign_workflow_stages(id),
  comment_id uuid REFERENCES public.campaign_comments(id),
  
  -- Metadata
  metadata jsonb,
  
  -- Timestamps
  created_at timestamp with time zone DEFAULT now(),
  
  CONSTRAINT campaign_activities_pkey PRIMARY KEY (id)
);

-- Campaign notifications
CREATE TABLE IF NOT EXISTS public.campaign_notifications (
  id uuid NOT NULL DEFAULT uuid_generate_v4(),
  campaign_id uuid NOT NULL REFERENCES public.campaigns(id) ON DELETE CASCADE,
  user_id uuid NOT NULL REFERENCES public.users(id) ON DELETE CASCADE,
  activity_id uuid REFERENCES public.campaign_activities(id) ON DELETE CASCADE,
  
  -- Notification Details
  notification_type text NOT NULL CHECK (notification_type IN (
    'mention', 'assignment', 'approval_request', 'deadline', 'comment', 'update'
  )),
  title text NOT NULL,
  message text,
  
  -- Status
  is_read boolean DEFAULT false,
  read_at timestamp with time zone,
  
  -- Delivery
  delivery_method text[] DEFAULT '{"in_app"}' CHECK (
    delivery_method <@ '{"in_app", "email", "slack", "webhook"}'
  ),
  delivered_at timestamp with time zone,
  
  -- Metadata
  metadata jsonb,
  
  -- Timestamps
  created_at timestamp with time zone DEFAULT now(),
  
  CONSTRAINT campaign_notifications_pkey PRIMARY KEY (id)
);

-- Campaign templates for quick setup
CREATE TABLE IF NOT EXISTS public.campaign_templates (
  id uuid NOT NULL DEFAULT uuid_generate_v4(),
  user_id uuid REFERENCES public.users(id) ON DELETE SET NULL,
  
  -- Template Details
  name text NOT NULL,
  description text,
  category text NOT NULL CHECK (category IN (
    'product_launch', 'brand_awareness', 'seasonal', 'social_media', 'video_ad', 'custom'
  )),
  
  -- Template Data
  template_data jsonb NOT NULL,
  default_workflow jsonb,
  suggested_budget_range text,
  estimated_duration_days integer,
  
  -- Visibility
  is_public boolean DEFAULT false,
  is_featured boolean DEFAULT false,
  
  -- Usage Stats
  usage_count integer DEFAULT 0,
  
  -- Metadata
  tags text[],
  metadata jsonb,
  
  -- Timestamps
  created_at timestamp with time zone DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now(),
  
  CONSTRAINT campaign_templates_pkey PRIMARY KEY (id)
);

-- Campaign integrations (for external tools)
CREATE TABLE IF NOT EXISTS public.campaign_integrations (
  id uuid NOT NULL DEFAULT uuid_generate_v4(),
  campaign_id uuid NOT NULL REFERENCES public.campaigns(id) ON DELETE CASCADE,
  user_id uuid NOT NULL REFERENCES public.users(id) ON DELETE CASCADE,
  
  -- Integration Details
  integration_type text NOT NULL CHECK (integration_type IN (
    'slack', 'discord', 'teams', 'asana', 'trello', 'notion', 'figma', 'google_drive', 'dropbox'
  )),
  integration_name text NOT NULL,
  
  -- Configuration
  config jsonb NOT NULL,
  webhook_url text,
  api_key_encrypted text,
  
  -- Status
  is_active boolean DEFAULT true,
  last_sync timestamp with time zone,
  sync_status text DEFAULT 'pending' CHECK (sync_status IN ('pending', 'synced', 'error', 'disabled')),
  
  -- Metadata
  metadata jsonb,
  
  -- Timestamps
  created_at timestamp with time zone DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now(),
  
  CONSTRAINT campaign_integrations_pkey PRIMARY KEY (id),
  CONSTRAINT unique_campaign_integration UNIQUE (campaign_id, integration_type)
);

-- Indexes for performance
CREATE INDEX IF NOT EXISTS idx_campaign_editing_sessions_campaign_id ON public.campaign_editing_sessions(campaign_id);
CREATE INDEX IF NOT EXISTS idx_campaign_editing_sessions_user_id ON public.campaign_editing_sessions(user_id);
CREATE INDEX IF NOT EXISTS idx_campaign_editing_sessions_active ON public.campaign_editing_sessions(is_active, last_activity);

CREATE INDEX IF NOT EXISTS idx_campaign_activities_campaign_id ON public.campaign_activities(campaign_id);
CREATE INDEX IF NOT EXISTS idx_campaign_activities_user_id ON public.campaign_activities(user_id);
CREATE INDEX IF NOT EXISTS idx_campaign_activities_type ON public.campaign_activities(activity_type);
CREATE INDEX IF NOT EXISTS idx_campaign_activities_created_at ON public.campaign_activities(created_at DESC);

CREATE INDEX IF NOT EXISTS idx_campaign_notifications_user_id ON public.campaign_notifications(user_id);
CREATE INDEX IF NOT EXISTS idx_campaign_notifications_campaign_id ON public.campaign_notifications(campaign_id);
CREATE INDEX IF NOT EXISTS idx_campaign_notifications_unread ON public.campaign_notifications(user_id, is_read, created_at DESC);

CREATE INDEX IF NOT EXISTS idx_campaign_templates_category ON public.campaign_templates(category);
CREATE INDEX IF NOT EXISTS idx_campaign_templates_public ON public.campaign_templates(is_public, is_featured);

CREATE INDEX IF NOT EXISTS idx_campaign_integrations_campaign_id ON public.campaign_integrations(campaign_id);
CREATE INDEX IF NOT EXISTS idx_campaign_integrations_type ON public.campaign_integrations(integration_type);

-- Row Level Security (RLS) Policies
ALTER TABLE public.campaign_editing_sessions ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.campaign_activities ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.campaign_notifications ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.campaign_templates ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.campaign_integrations ENABLE ROW LEVEL SECURITY;

-- Editing sessions: Users can access sessions for campaigns they have access to
CREATE POLICY "Users can access editing sessions of accessible campaigns" 
ON public.campaign_editing_sessions FOR ALL USING (
  campaign_id IN (
    SELECT id FROM public.campaigns 
    WHERE user_id = auth.uid() OR 
    id IN (
      SELECT campaign_id FROM public.campaign_team_members 
      WHERE user_id = auth.uid() AND status = 'active'
    )
  )
);

-- Activities: Users can see activities for campaigns they have access to
CREATE POLICY "Users can see activities of accessible campaigns" 
ON public.campaign_activities FOR ALL USING (
  campaign_id IN (
    SELECT id FROM public.campaigns 
    WHERE user_id = auth.uid() OR 
    id IN (
      SELECT campaign_id FROM public.campaign_team_members 
      WHERE user_id = auth.uid() AND status = 'active'
    )
  )
);

-- Notifications: Users can only see their own notifications
CREATE POLICY "Users can only see their own notifications" 
ON public.campaign_notifications FOR ALL USING (user_id = auth.uid());

-- Templates: Users can see public templates and their own templates
CREATE POLICY "Users can see public templates and their own templates" 
ON public.campaign_templates FOR SELECT USING (
  is_public = true OR user_id = auth.uid()
);

CREATE POLICY "Users can manage their own templates" 
ON public.campaign_templates FOR INSERT, UPDATE, DELETE USING (user_id = auth.uid());

-- Integrations: Users can access integrations for campaigns they have access to
CREATE POLICY "Users can access integrations of accessible campaigns" 
ON public.campaign_integrations FOR ALL USING (
  campaign_id IN (
    SELECT id FROM public.campaigns 
    WHERE user_id = auth.uid() OR 
    id IN (
      SELECT campaign_id FROM public.campaign_team_members 
      WHERE user_id = auth.uid() AND status = 'active'
    )
  )
);
