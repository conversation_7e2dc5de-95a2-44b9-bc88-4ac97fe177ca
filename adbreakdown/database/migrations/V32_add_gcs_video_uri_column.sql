-- V32: Add GCS Video URI Column for YouTube Downloaded Videos
-- This migration adds support for storing both YouTube URL and GCS URI for unlisted/private videos

-- Add gcs_video_uri column to private_analyses table
ALTER TABLE public.private_analyses 
ADD COLUMN gcs_video_uri text;

-- Add index for performance when querying by GCS URI
CREATE INDEX IF NOT EXISTS idx_private_analyses_gcs_video_uri 
ON public.private_analyses(gcs_video_uri) 
WHERE gcs_video_uri IS NOT NULL;

-- Add comment for documentation
COMMENT ON COLUMN public.private_analyses.gcs_video_uri 
IS 'GCS URI for downloaded YouTube videos (unlisted/private). NULL for public YouTube videos and file uploads.';

-- Update analysis_type constraint to include new types
ALTER TABLE public.private_analyses 
DROP CONSTRAINT IF EXISTS private_analyses_analysis_type_check;

ALTER TABLE public.private_analyses 
ADD CONSTRAINT private_analyses_analysis_type_check 
CHECK (analysis_type IN ('youtube_url', 'file_upload', 'youtube_downloaded', 'private', 'pre-launch', 'beta'));

-- Add constraint to ensure data integrity:
-- If gcs_video_uri is set, analysis_type should be 'youtube_downloaded' or 'file_upload'
-- This is a soft constraint via a function to avoid breaking existing data
CREATE OR REPLACE FUNCTION validate_gcs_video_uri_consistency()
RETURNS trigger AS $$
BEGIN
    -- If gcs_video_uri is being set for a YouTube analysis
    IF NEW.gcs_video_uri IS NOT NULL 
       AND NEW.analysis_type NOT IN ('file_upload', 'youtube_downloaded') 
       AND OLD.analysis_type IS DISTINCT FROM NEW.analysis_type THEN
        
        RAISE WARNING 'Setting gcs_video_uri for analysis_type %', NEW.analysis_type;
    END IF;
    
    RETURN NEW;
END;
$$ language plpgsql;

-- Create trigger to validate consistency (warning only, not blocking)
DROP TRIGGER IF EXISTS validate_gcs_video_uri_trigger ON private_analyses;
CREATE TRIGGER validate_gcs_video_uri_trigger
    BEFORE UPDATE ON private_analyses
    FOR EACH ROW
    EXECUTE FUNCTION validate_gcs_video_uri_consistency();