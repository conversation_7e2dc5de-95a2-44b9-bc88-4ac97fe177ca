-- V25: Add usage tracking and studio waitlist
-- This migration adds usage tracking columns and creates the studio waitlist table

-- Add usage tracking columns to profiles table
ALTER TABLE profiles 
ADD COLUMN IF NOT EXISTS analysis_count INTEGER DEFAULT 0,
ADD COLUMN IF NOT EXISTS library_views_count INTEGER DEFAULT 0,
ADD COLUMN IF NOT EXISTS prelaunch_analysis_count INTEGER DEFAULT 0,
ADD COLUMN IF NOT EXISTS usage_reset_date TIMESTAMP WITH TIME ZONE DEFAULT (NOW() + INTERVAL '1 month');

-- Create studio waitlist table
CREATE TABLE IF NOT EXISTS studio_waitlist (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id TEXT NOT NULL REFERENCES users(id) ON DELETE CASCADE,
  clerk_id TEXT NOT NULL,
  email TEXT,
  requested_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'approved', 'rejected')),
  notes TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(user_id)
);

-- Create index for faster lookups
CREATE INDEX IF NOT EXISTS idx_studio_waitlist_user_id ON studio_waitlist(user_id);
CREATE INDEX IF NOT EXISTS idx_studio_waitlist_clerk_id ON studio_waitlist(clerk_id);
CREATE INDEX IF NOT EXISTS idx_studio_waitlist_status ON studio_waitlist(status);

-- Create function to reset usage counts monthly
CREATE OR REPLACE FUNCTION reset_monthly_usage() 
RETURNS void AS $$
BEGIN
  UPDATE profiles 
  SET 
    analysis_count = 0,
    library_views_count = 0,
    prelaunch_analysis_count = 0,
    usage_reset_date = NOW() + INTERVAL '1 month'
  WHERE usage_reset_date <= NOW();
END;
$$ LANGUAGE plpgsql;

-- Add comments for tracking
COMMENT ON COLUMN profiles.analysis_count IS 'Monthly count of analyses created by user';
COMMENT ON COLUMN profiles.library_views_count IS 'Monthly count of ad library individual views';
COMMENT ON COLUMN profiles.prelaunch_analysis_count IS 'Monthly count of pre-launch analyses created';
COMMENT ON COLUMN profiles.usage_reset_date IS 'Date when usage counts will reset';

COMMENT ON TABLE studio_waitlist IS 'V25 migration: Waitlist for studio access requests';
COMMENT ON FUNCTION reset_monthly_usage() IS 'V25 migration: Resets monthly usage limits';