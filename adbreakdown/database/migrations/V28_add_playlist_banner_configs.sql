-- Create table for playlist banner configurations
CREATE TABLE IF NOT EXISTS playlist_banner_configs (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  playlist_tag TEXT NOT NULL UNIQUE,
  title TEXT NOT NULL,
  subtitle TEXT DEFAULT '',
  background_gradient TEXT DEFAULT 'from-indigo-50 via-purple-50 to-blue-50',
  text_color TEXT DEFAULT 'text-gray-700',
  features JSONB DEFAULT '[]'::jsonb,
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_playlist_banner_configs_tag ON playlist_banner_configs(playlist_tag);
CREATE INDEX IF NOT EXISTS idx_playlist_banner_configs_active ON playlist_banner_configs(is_active);

-- Add RLS (Row Level Security) policies if needed
ALTER TABLE playlist_banner_configs ENABLE ROW LEVEL SECURITY;

-- Policy to allow public read access to active banner configs
CREATE POLICY "Allow public read access to active banner configs" 
ON playlist_banner_configs FOR SELECT 
USING (is_active = true);

-- Policy to allow admin access for all operations
CREATE POLICY "Allow admin full access to banner configs" 
ON playlist_banner_configs FOR ALL 
USING (true);

-- Comment on table
COMMENT ON TABLE playlist_banner_configs IS 'Stores customizable banner configurations for playlist pages in the ad library';