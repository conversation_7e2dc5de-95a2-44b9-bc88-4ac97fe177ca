-- Migration to add Instagram and Facebook analysis types to private_analyses table
-- This extends the check constraint to allow instagram_url and facebook_url values

-- First, let's see what analysis_type values exist in the table
-- SELECT DISTINCT analysis_type FROM private_analyses;

-- Update any NULL or invalid analysis_type values to 'youtube_url' (safe default)
UPDATE private_analyses 
SET analysis_type = 'youtube_url' 
WHERE analysis_type IS NULL 
   OR analysis_type NOT IN ('youtube_url', 'youtube_downloaded', 'file_upload');

-- Drop the existing constraint
ALTER TABLE private_analyses DROP CONSTRAINT IF EXISTS private_analyses_analysis_type_check;

-- Add the new constraint with Instagram and Facebook support
ALTER TABLE private_analyses ADD CONSTRAINT private_analyses_analysis_type_check 
CHECK (analysis_type IN (
    'youtube_url', 
    'youtube_downloaded', 
    'file_upload', 
    'instagram_url',
    'instagram_downloaded',
    'facebook_url',
    'facebook_downloaded'
));