-- V25: Campaign Management Tables
-- Comprehensive campaign management system with collaboration features

-- Enable UUID extension if not already enabled
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Campaigns table - Core campaign information
CREATE TABLE IF NOT EXISTS public.campaigns (
  id uuid NOT NULL DEFAULT uuid_generate_v4(),
  user_id uuid NOT NULL REFERENCES public.users(id) ON DELETE CASCADE,
  
  -- Basic Information
  name text NOT NULL,
  description text,
  status text NOT NULL DEFAULT 'draft' CHECK (status IN ('draft', 'in-progress', 'pending-review', 'approved', 'launched', 'completed', 'cancelled')),
  stage text NOT NULL DEFAULT 'briefing' CHECK (stage IN ('briefing', 'ideation', 'scriptwriting', 'storyboard', 'production', 'review', 'launched')),
  progress integer DEFAULT 0 CHECK (progress >= 0 AND progress <= 100),
  
  -- Campaign Details
  objectives text[],
  key_message text,
  call_to_action text,
  target_audience text[],
  channels text[],
  
  -- Budget & Timeline
  budget_amount numeric,
  budget_currency text DEFAULT 'USD',
  start_date date,
  launch_date date,
  end_date date,
  
  -- Brand & Compliance
  brand_guidelines text,
  compliance_requirements text[],
  
  -- Metadata
  template_id text,
  tags text[],
  is_public boolean DEFAULT false,
  
  -- Timestamps
  created_at timestamp with time zone DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now(),
  
  CONSTRAINT campaigns_pkey PRIMARY KEY (id)
);

-- Campaign team members
CREATE TABLE IF NOT EXISTS public.campaign_team_members (
  id uuid NOT NULL DEFAULT uuid_generate_v4(),
  campaign_id uuid NOT NULL REFERENCES public.campaigns(id) ON DELETE CASCADE,
  user_id uuid NOT NULL REFERENCES public.users(id) ON DELETE CASCADE,
  
  -- Role & Permissions
  role text NOT NULL CHECK (role IN ('owner', 'manager', 'creative', 'copywriter', 'reviewer', 'viewer')),
  permissions text[] DEFAULT '{}',
  
  -- Status
  status text DEFAULT 'active' CHECK (status IN ('active', 'inactive', 'pending')),
  invited_by uuid REFERENCES public.users(id),
  invited_at timestamp with time zone DEFAULT now(),
  joined_at timestamp with time zone,
  
  CONSTRAINT campaign_team_members_pkey PRIMARY KEY (id),
  CONSTRAINT unique_campaign_user UNIQUE (campaign_id, user_id)
);

-- Campaign assets (scripts, images, videos, documents)
CREATE TABLE IF NOT EXISTS public.campaign_assets (
  id uuid NOT NULL DEFAULT uuid_generate_v4(),
  campaign_id uuid NOT NULL REFERENCES public.campaigns(id) ON DELETE CASCADE,
  user_id uuid NOT NULL REFERENCES public.users(id) ON DELETE CASCADE,
  
  -- Asset Information
  name text NOT NULL,
  description text,
  asset_type text NOT NULL CHECK (asset_type IN ('script', 'storyboard', 'image', 'video', 'audio', 'document', 'brief')),
  file_url text,
  file_size integer,
  mime_type text,
  
  -- Version Control
  version integer DEFAULT 1,
  parent_asset_id uuid REFERENCES public.campaign_assets(id),
  
  -- Status & Review
  status text DEFAULT 'draft' CHECK (status IN ('draft', 'in-review', 'approved', 'rejected', 'archived')),
  review_status text,
  reviewer_id uuid REFERENCES public.users(id),
  reviewed_at timestamp with time zone,
  
  -- AI Analysis
  ai_analysis jsonb,
  brand_alignment_score integer CHECK (brand_alignment_score >= 0 AND brand_alignment_score <= 100),
  
  -- Metadata
  tags text[],
  metadata jsonb,
  
  -- Timestamps
  created_at timestamp with time zone DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now(),
  
  CONSTRAINT campaign_assets_pkey PRIMARY KEY (id)
);

-- Campaign workflow stages and tasks
CREATE TABLE IF NOT EXISTS public.campaign_workflow_stages (
  id uuid NOT NULL DEFAULT uuid_generate_v4(),
  campaign_id uuid NOT NULL REFERENCES public.campaigns(id) ON DELETE CASCADE,
  
  -- Stage Information
  name text NOT NULL,
  description text,
  stage_order integer NOT NULL,
  
  -- Status & Timeline
  status text DEFAULT 'pending' CHECK (status IN ('pending', 'in-progress', 'completed', 'blocked', 'skipped')),
  due_date date,
  started_at timestamp with time zone,
  completed_at timestamp with time zone,
  
  -- Assignment
  assigned_to uuid REFERENCES public.users(id),
  
  -- Dependencies
  depends_on uuid[] DEFAULT '{}',
  
  -- Metadata
  metadata jsonb,
  
  -- Timestamps
  created_at timestamp with time zone DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now(),
  
  CONSTRAINT campaign_workflow_stages_pkey PRIMARY KEY (id)
);

-- Campaign comments and collaboration
CREATE TABLE IF NOT EXISTS public.campaign_comments (
  id uuid NOT NULL DEFAULT uuid_generate_v4(),
  campaign_id uuid NOT NULL REFERENCES public.campaigns(id) ON DELETE CASCADE,
  user_id uuid NOT NULL REFERENCES public.users(id) ON DELETE CASCADE,
  
  -- Comment Details
  content text NOT NULL,
  comment_type text DEFAULT 'general' CHECK (comment_type IN ('general', 'feedback', 'approval', 'question', 'suggestion')),
  
  -- Threading
  parent_comment_id uuid REFERENCES public.campaign_comments(id),
  thread_id uuid,
  
  -- Context
  asset_id uuid REFERENCES public.campaign_assets(id),
  stage_id uuid REFERENCES public.campaign_workflow_stages(id),
  
  -- Status
  is_resolved boolean DEFAULT false,
  resolved_by uuid REFERENCES public.users(id),
  resolved_at timestamp with time zone,
  
  -- Metadata
  metadata jsonb,
  
  -- Timestamps
  created_at timestamp with time zone DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now(),
  
  CONSTRAINT campaign_comments_pkey PRIMARY KEY (id)
);

-- Campaign AI interactions and suggestions
CREATE TABLE IF NOT EXISTS public.campaign_ai_interactions (
  id uuid NOT NULL DEFAULT uuid_generate_v4(),
  campaign_id uuid NOT NULL REFERENCES public.campaigns(id) ON DELETE CASCADE,
  user_id uuid NOT NULL REFERENCES public.users(id) ON DELETE CASCADE,

  -- Interaction Details
  interaction_type text NOT NULL CHECK (interaction_type IN ('suggestion', 'analysis', 'generation', 'review', 'optimization')),
  prompt text,
  response text,

  -- Context
  asset_id uuid REFERENCES public.campaign_assets(id),
  stage_id uuid REFERENCES public.campaign_workflow_stages(id),

  -- AI Model Info
  model_used text,
  model_version text,
  confidence_score numeric,

  -- Status
  status text DEFAULT 'completed' CHECK (status IN ('pending', 'completed', 'failed', 'cancelled')),
  applied boolean DEFAULT false,

  -- Metadata
  metadata jsonb,

  -- Timestamps
  created_at timestamp with time zone DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now(),

  CONSTRAINT campaign_ai_interactions_pkey PRIMARY KEY (id)
);

-- Indexes for performance
CREATE INDEX IF NOT EXISTS idx_campaigns_user_id ON public.campaigns(user_id);
CREATE INDEX IF NOT EXISTS idx_campaigns_status ON public.campaigns(status);
CREATE INDEX IF NOT EXISTS idx_campaigns_stage ON public.campaigns(stage);
CREATE INDEX IF NOT EXISTS idx_campaigns_created_at ON public.campaigns(created_at);

CREATE INDEX IF NOT EXISTS idx_campaign_team_members_campaign_id ON public.campaign_team_members(campaign_id);
CREATE INDEX IF NOT EXISTS idx_campaign_team_members_user_id ON public.campaign_team_members(user_id);

CREATE INDEX IF NOT EXISTS idx_campaign_assets_campaign_id ON public.campaign_assets(campaign_id);
CREATE INDEX IF NOT EXISTS idx_campaign_assets_user_id ON public.campaign_assets(user_id);
CREATE INDEX IF NOT EXISTS idx_campaign_assets_type ON public.campaign_assets(asset_type);
CREATE INDEX IF NOT EXISTS idx_campaign_assets_status ON public.campaign_assets(status);

CREATE INDEX IF NOT EXISTS idx_campaign_workflow_stages_campaign_id ON public.campaign_workflow_stages(campaign_id);
CREATE INDEX IF NOT EXISTS idx_campaign_workflow_stages_order ON public.campaign_workflow_stages(campaign_id, stage_order);

CREATE INDEX IF NOT EXISTS idx_campaign_comments_campaign_id ON public.campaign_comments(campaign_id);
CREATE INDEX IF NOT EXISTS idx_campaign_comments_user_id ON public.campaign_comments(user_id);
CREATE INDEX IF NOT EXISTS idx_campaign_comments_asset_id ON public.campaign_comments(asset_id);

CREATE INDEX IF NOT EXISTS idx_campaign_ai_interactions_campaign_id ON public.campaign_ai_interactions(campaign_id);
CREATE INDEX IF NOT EXISTS idx_campaign_ai_interactions_type ON public.campaign_ai_interactions(interaction_type);

-- Row Level Security (RLS) Policies
ALTER TABLE public.campaigns ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.campaign_team_members ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.campaign_assets ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.campaign_workflow_stages ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.campaign_comments ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.campaign_ai_interactions ENABLE ROW LEVEL SECURITY;

-- Campaigns: Users can access campaigns they own or are team members of
CREATE POLICY "Users can access their own campaigns or campaigns they're team members of"
ON public.campaigns FOR ALL USING (
  user_id = auth.uid() OR
  id IN (
    SELECT campaign_id FROM public.campaign_team_members
    WHERE user_id = auth.uid() AND status = 'active'
  )
);

-- Team members: Users can see team members of campaigns they have access to
CREATE POLICY "Users can see team members of accessible campaigns"
ON public.campaign_team_members FOR ALL USING (
  campaign_id IN (
    SELECT id FROM public.campaigns
    WHERE user_id = auth.uid() OR
    id IN (
      SELECT campaign_id FROM public.campaign_team_members
      WHERE user_id = auth.uid() AND status = 'active'
    )
  )
);

-- Assets: Users can access assets of campaigns they have access to
CREATE POLICY "Users can access assets of accessible campaigns"
ON public.campaign_assets FOR ALL USING (
  campaign_id IN (
    SELECT id FROM public.campaigns
    WHERE user_id = auth.uid() OR
    id IN (
      SELECT campaign_id FROM public.campaign_team_members
      WHERE user_id = auth.uid() AND status = 'active'
    )
  )
);

-- Workflow stages: Users can access workflow stages of campaigns they have access to
CREATE POLICY "Users can access workflow stages of accessible campaigns"
ON public.campaign_workflow_stages FOR ALL USING (
  campaign_id IN (
    SELECT id FROM public.campaigns
    WHERE user_id = auth.uid() OR
    id IN (
      SELECT campaign_id FROM public.campaign_team_members
      WHERE user_id = auth.uid() AND status = 'active'
    )
  )
);

-- Comments: Users can access comments of campaigns they have access to
CREATE POLICY "Users can access comments of accessible campaigns"
ON public.campaign_comments FOR ALL USING (
  campaign_id IN (
    SELECT id FROM public.campaigns
    WHERE user_id = auth.uid() OR
    id IN (
      SELECT campaign_id FROM public.campaign_team_members
      WHERE user_id = auth.uid() AND status = 'active'
    )
  )
);

-- AI interactions: Users can access AI interactions of campaigns they have access to
CREATE POLICY "Users can access AI interactions of accessible campaigns"
ON public.campaign_ai_interactions FOR ALL USING (
  campaign_id IN (
    SELECT id FROM public.campaigns
    WHERE user_id = auth.uid() OR
    id IN (
      SELECT campaign_id FROM public.campaign_team_members
      WHERE user_id = auth.uid() AND status = 'active'
    )
  )
);
