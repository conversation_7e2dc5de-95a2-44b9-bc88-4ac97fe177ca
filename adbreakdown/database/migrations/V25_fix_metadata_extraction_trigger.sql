-- V25: Fix metadata extraction trigger to avoid set-returning functions in WHERE clauses
-- This fixes the PostgreSQL error: "set-returning functions are not allowed in WHERE"

-- Updated function to extract metadata with proper CTE usage
CREATE OR REPLACE FUNCTION extract_metadata_from_analysis()
RETURNS TRIGGER AS $$
DECLARE
  celebrity_json JSON;
  campaign_category_json JSON;
  celebrity_array TEXT[];
  campaign_category_array TEXT[];
  celebrity_context_val TEXT;
BEGIN
  -- Only trigger when status changes to 'completed' and marketing_analysis exists
  IF NEW.status = 'completed' 
     AND NEW.marketing_analysis IS NOT NULL 
     AND (OLD.status IS NULL OR OLD.status != 'completed') THEN
    
    -- Extract celebrity field and convert to array
    celebrity_json := (NEW.marketing_analysis::json->'metadata'->>'celebrity')::json;
    
    IF celebrity_json IS NOT NULL THEN
      -- Handle both array and string formats from AI
      IF json_typeof(celebrity_json) = 'array' THEN
        -- AI provided an array: ["Name1", "Name2"]
        WITH celebrity_elements AS (
          SELECT json_array_elements_text(celebrity_json) AS element
        )
        SELECT ARRAY(
          SELECT element 
          FROM celebrity_elements 
          WHERE element IS NOT NULL 
            AND element != 'None'
            AND element != ''
        ) INTO celebrity_array;
      ELSIF json_typeof(celebrity_json) = 'string' AND celebrity_json::text != '"None"' AND celebrity_json::text != '""' THEN
        -- AI provided a string: "Name1, Name2" or "Name1"
        celebrity_array := string_to_array(celebrity_json #>> '{}', ',');
        -- Clean up array elements
        SELECT ARRAY(
          SELECT TRIM(elem) 
          FROM unnest(celebrity_array) AS elem 
          WHERE TRIM(elem) != '' AND TRIM(elem) != 'None'
        ) INTO celebrity_array;
      END IF;
    END IF;
    
    -- Extract campaign_category field and convert to array
    campaign_category_json := (NEW.marketing_analysis::json->'metadata'->>'campaign_category')::json;
    
    IF campaign_category_json IS NOT NULL THEN
      -- Handle both array and string formats from AI
      IF json_typeof(campaign_category_json) = 'array' THEN
        -- AI provided an array: ["Cat1", "Cat2"]
        WITH campaign_elements AS (
          SELECT json_array_elements_text(campaign_category_json) AS element
        )
        SELECT ARRAY(
          SELECT element 
          FROM campaign_elements 
          WHERE element IS NOT NULL 
            AND element != ''
        ) INTO campaign_category_array;
      ELSIF json_typeof(campaign_category_json) = 'string' AND campaign_category_json::text != '""' THEN
        -- AI provided a string: "Cat1 / Cat2" or "Cat1, Cat2" or "Cat1"
        -- Handle both slash and comma separators
        campaign_category_array := string_to_array(
          regexp_replace(campaign_category_json #>> '{}', '\s*[/]\s*', ',', 'g'),
          ','
        );
        -- Clean up array elements
        SELECT ARRAY(
          SELECT TRIM(elem) 
          FROM unnest(campaign_category_array) AS elem 
          WHERE TRIM(elem) != '' 
        ) INTO campaign_category_array;
      END IF;
    END IF;
    
    -- Extract celebrity_context if it exists
    celebrity_context_val := json_extract_path_text(NEW.marketing_analysis::json, 'metadata', 'celebrity_context');
    
    -- Update the record with extracted metadata
    UPDATE public.ad_analyses SET
      brand = COALESCE(
        json_extract_path_text(NEW.marketing_analysis::json, 'brand'),
        json_extract_path_text(NEW.marketing_analysis::json, 'metadata', 'brand')
      ),
      product_category = json_extract_path_text(NEW.marketing_analysis::json, 'metadata', 'product_category'),
      parent_entity = json_extract_path_text(NEW.marketing_analysis::json, 'metadata', 'parent_entity'),
      
      -- Use array fields instead of string fields
      campaign_category = CASE 
        WHEN campaign_category_array IS NOT NULL AND array_length(campaign_category_array, 1) > 0 
        THEN campaign_category_array
        ELSE NULL
      END,
      
      celebrity = CASE 
        WHEN celebrity_array IS NOT NULL AND array_length(celebrity_array, 1) > 0 
        THEN celebrity_array
        ELSE NULL
      END,
      
      celebrity_context = celebrity_context_val,
      
      geography = json_extract_path_text(NEW.marketing_analysis::json, 'metadata', 'geography'),
      agency = json_extract_path_text(NEW.marketing_analysis::json, 'metadata', 'agency'),
      launch_date = CASE 
        WHEN json_extract_path_text(NEW.marketing_analysis::json, 'metadata', 'campaign_launch_date') IS NOT NULL 
        THEN 
          CASE 
            -- Handle "May 2024" format - convert to first day of month
            WHEN json_extract_path_text(NEW.marketing_analysis::json, 'metadata', 'campaign_launch_date') ~ '^[A-Za-z]+ \d{4}$' 
            THEN (TO_DATE(json_extract_path_text(NEW.marketing_analysis::json, 'metadata', 'campaign_launch_date'), 'Month YYYY'))::timestamp with time zone
            -- Handle other date formats if they exist
            ELSE 
              CASE 
                WHEN json_extract_path_text(NEW.marketing_analysis::json, 'metadata', 'campaign_launch_date') ~ '^\d{4}-\d{2}-\d{2}' 
                THEN (json_extract_path_text(NEW.marketing_analysis::json, 'metadata', 'campaign_launch_date'))::timestamp with time zone
                ELSE NULL
              END
          END
        ELSE NULL 
      END,
      updated_at = NOW()
    WHERE id = NEW.id;
    
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Add comment for tracking
COMMENT ON FUNCTION extract_metadata_from_analysis() IS 'V25 migration: Fixed set-returning functions in WHERE clause issue';
