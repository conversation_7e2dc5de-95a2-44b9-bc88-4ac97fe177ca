-- Convert celebrity and campaign_category columns from TEXT to TEXT[]
-- This makes them proper PostgreSQL arrays instead of strings

BEGIN;

-- Step 1: Show current state
SELECT 
  'Current State' as status,
  COUNT(*) as total_records,
  COUNT(celebrity) as has_celebrity,
  COUNT(campaign_category) as has_campaign_category,
  -- Show sample data
  (SELECT celebrity FROM ad_analyses WHERE celebrity IS NOT NULL LIMIT 1) as sample_celebrity,
  (SELECT campaign_category FROM ad_analyses WHERE campaign_category IS NOT NULL LIMIT 1) as sample_campaign
FROM ad_analyses;

-- Step 2: Create backup columns
ALTER TABLE ad_analyses ADD COLUMN celebrity_backup TEXT;
ALTER TABLE ad_analyses ADD COLUMN campaign_category_backup TEXT;

-- Step 3: Backup current data
UPDATE ad_analyses SET 
  celebrity_backup = celebrity,
  campaign_category_backup = campaign_category;

-- Step 4: Drop existing columns
ALTER TABLE ad_analyses DROP COLUMN celebrity;
ALTER TABLE ad_analyses DROP COLUMN campaign_category;

-- Step 5: Create new columns as proper TEXT[] arrays
ALTER TABLE ad_analyses ADD COLUMN celebrity TEXT[];
ALTER TABLE ad_analyses ADD COLUMN campaign_category TEXT[];

-- Step 6: Convert the backed up string data to proper arrays
-- Handle celebrity data
UPDATE ad_analyses 
SET celebrity = (
  CASE 
    WHEN celebrity_backup IS NULL OR celebrity_backup = '' THEN NULL
    -- If it already looks like a PostgreSQL array string: {"Name1","Name2"}
    WHEN celebrity_backup ~ '^\{.*\}$' THEN 
      -- Convert PostgreSQL array string to actual array
      celebrity_backup::TEXT[]
    -- If it's a comma-separated string: "Name1, Name2, Name3"
    WHEN celebrity_backup LIKE '%,%' THEN
      string_to_array(celebrity_backup, ',')
    -- Single name
    ELSE
      ARRAY[celebrity_backup]
  END
)
WHERE celebrity_backup IS NOT NULL;

-- Handle campaign_category data
UPDATE ad_analyses 
SET campaign_category = (
  CASE 
    WHEN campaign_category_backup IS NULL OR campaign_category_backup = '' THEN NULL
    -- If it already looks like a PostgreSQL array string: {"Cat1","Cat2"}
    WHEN campaign_category_backup ~ '^\{.*\}$' THEN 
      -- Convert PostgreSQL array string to actual array
      campaign_category_backup::TEXT[]
    -- If it's a slash/comma-separated string: "Cat1 / Cat2" or "Cat1, Cat2"
    WHEN campaign_category_backup ~ '[,/]' THEN
      string_to_array(
        regexp_replace(campaign_category_backup, '\s*[/]\s*', ',', 'g'), 
        ','
      )
    -- Single category
    ELSE
      ARRAY[campaign_category_backup]
  END
)
WHERE campaign_category_backup IS NOT NULL;

-- Step 7: Clean up array elements (trim whitespace, remove nulls)
UPDATE ad_analyses 
SET celebrity = (
  SELECT ARRAY(
    SELECT TRIM(elem) 
    FROM unnest(celebrity) AS elem 
    WHERE TRIM(elem) != '' AND TRIM(elem) IS NOT NULL
  )
)
WHERE celebrity IS NOT NULL;

UPDATE ad_analyses 
SET campaign_category = (
  SELECT ARRAY(
    SELECT TRIM(elem) 
    FROM unnest(campaign_category) AS elem 
    WHERE TRIM(elem) != '' AND TRIM(elem) IS NOT NULL
  )
)
WHERE campaign_category IS NOT NULL;

-- Step 8: Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_ad_analyses_celebrity_gin ON ad_analyses USING GIN (celebrity);
CREATE INDEX IF NOT EXISTS idx_ad_analyses_campaign_category_gin ON ad_analyses USING GIN (campaign_category);

-- Step 9: Add comments
COMMENT ON COLUMN ad_analyses.celebrity IS 'Array of celebrity names (proper TEXT[] type)';
COMMENT ON COLUMN ad_analyses.campaign_category IS 'Array of campaign categories (proper TEXT[] type)';

-- Step 10: Show results
SELECT 
  'After Conversion' as status,
  COUNT(*) as total_records,
  COUNT(celebrity) as has_celebrity_arrays,
  COUNT(campaign_category) as has_category_arrays,
  COUNT(CASE WHEN array_length(celebrity, 1) > 1 THEN 1 END) as multi_celebrity_records,
  COUNT(CASE WHEN array_length(campaign_category, 1) > 1 THEN 1 END) as multi_category_records,
  MAX(array_length(celebrity, 1)) as max_celebrities_in_record,
  MAX(array_length(campaign_category, 1)) as max_categories_in_record
FROM ad_analyses;

-- Step 11: Show sample converted data
SELECT 
  'Sample Converted Data' as info,
  id,
  celebrity,
  campaign_category,
  array_length(celebrity, 1) as celebrity_count,
  array_length(campaign_category, 1) as category_count
FROM ad_analyses 
WHERE celebrity IS NOT NULL OR campaign_category IS NOT NULL
ORDER BY array_length(celebrity, 1) DESC NULLS LAST
LIMIT 5;

-- Step 12: Drop backup columns
ALTER TABLE ad_analyses DROP COLUMN celebrity_backup;
ALTER TABLE ad_analyses DROP COLUMN campaign_category_backup;

COMMIT;

SELECT '✅ SUCCESS: Columns are now proper TEXT[] arrays!' as result;