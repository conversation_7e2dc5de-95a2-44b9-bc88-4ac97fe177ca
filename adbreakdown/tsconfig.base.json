{"compilerOptions": {"target": "ES2020", "lib": ["dom", "dom.iterable", "esnext"], "allowJs": true, "skipLibCheck": true, "strict": true, "forceConsistentCasingInFileNames": true, "noEmit": true, "esModuleInterop": true, "module": "esnext", "moduleResolution": "node", "resolveJsonModule": true, "isolatedModules": true, "jsx": "preserve", "incremental": true, "baseUrl": ".", "paths": {"@api/*": ["packages/api/*"], "@lib/*": ["packages/lib/*"], "@hooks/*": ["packages/hooks/*"], "@components/*": ["packages/components/*"]}}, "exclude": ["node_modules", ".next", "out"], "include": ["apps", "packages"]}