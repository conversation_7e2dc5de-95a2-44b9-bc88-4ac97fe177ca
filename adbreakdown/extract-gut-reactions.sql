-- Extract gut reactions from marketing_analysis JSON and populate gut_reaction column
-- This SQL script safely extracts gut reaction data from existing marketing_analysis fields

-- Create a function to safely extract gut reaction from JSON text
CREATE OR REPLACE FUNCTION extract_gut_reaction(marketing_data TEXT)
RETURNS TEXT AS $$
DECLARE
    json_data JSONB;
    gut_reaction TEXT;
BEGIN
    -- Return null if no data
    IF marketing_data IS NULL OR marketing_data = '' THEN
        RETURN NULL;
    END IF;
    
    BEGIN
        -- Try to parse as JSON
        json_data := marketing_data::JSONB;
        
        -- Try multiple paths to find gut reaction
        -- Path 1: executive_briefing.gut_reaction
        IF json_data ? 'executive_briefing' AND json_data->'executive_briefing' ? 'gut_reaction' THEN
            gut_reaction := json_data->'executive_briefing'->>'gut_reaction';
        -- Path 2: gut_reaction (direct)
        ELSIF json_data ? 'gut_reaction' THEN
            gut_reaction := json_data->>'gut_reaction';
        -- Path 3: summary
        ELSIF json_data ? 'summary' THEN
            gut_reaction := json_data->>'summary';
        -- Path 4: executive_summary
        ELSIF json_data ? 'executive_summary' THEN
            gut_reaction := json_data->>'executive_summary';
        END IF;
        
        -- Clean up the text (remove surrounding quotes if present)
        IF gut_reaction IS NOT NULL THEN
            gut_reaction := TRIM(gut_reaction);
            IF (gut_reaction LIKE '"%"' OR gut_reaction LIKE '''%''') THEN
                gut_reaction := SUBSTRING(gut_reaction FROM 2 FOR LENGTH(gut_reaction) - 2);
            END IF;
        END IF;
        
        RETURN gut_reaction;
        
    EXCEPTION WHEN OTHERS THEN
        -- If JSON parsing fails, try to extract from text patterns
        BEGIN
            -- Look for "gut_reaction": "..." pattern
            IF marketing_data ~ '"gut_reaction"\s*:\s*"[^"]*"' THEN
                gut_reaction := (regexp_match(marketing_data, '"gut_reaction"\s*:\s*"([^"]*)"'))[1];
            -- Look for "summary": "..." pattern
            ELSIF marketing_data ~ '"summary"\s*:\s*"[^"]*"' THEN
                gut_reaction := (regexp_match(marketing_data, '"summary"\s*:\s*"([^"]*)"'))[1];
            END IF;
            
            RETURN gut_reaction;
        EXCEPTION WHEN OTHERS THEN
            RETURN NULL;
        END;
    END;
END;
$$ LANGUAGE plpgsql;

-- Show statistics before update
SELECT 
    COUNT(*) as total_analyses,
    COUNT(marketing_analysis) as has_marketing_analysis,
    COUNT(gut_reaction) as has_gut_reaction,
    COUNT(CASE WHEN marketing_analysis IS NOT NULL AND gut_reaction IS NULL THEN 1 END) as need_extraction
FROM ad_analyses;

-- Update gut_reaction for all analyses that have marketing_analysis but no gut_reaction
UPDATE ad_analyses 
SET gut_reaction = extract_gut_reaction(marketing_analysis)
WHERE marketing_analysis IS NOT NULL 
  AND gut_reaction IS NULL
  AND extract_gut_reaction(marketing_analysis) IS NOT NULL;

-- Show results
SELECT 
    COUNT(*) as total_analyses,
    COUNT(marketing_analysis) as has_marketing_analysis,
    COUNT(gut_reaction) as has_gut_reaction_after,
    COUNT(CASE WHEN marketing_analysis IS NOT NULL AND gut_reaction IS NULL THEN 1 END) as still_need_extraction
FROM ad_analyses;

-- Show some examples of extracted gut reactions
SELECT 
    id,
    title,
    LENGTH(marketing_analysis) as marketing_analysis_length,
    LENGTH(gut_reaction) as gut_reaction_length,
    LEFT(gut_reaction, 100) || '...' as gut_reaction_preview
FROM ad_analyses 
WHERE gut_reaction IS NOT NULL 
ORDER BY updated_at DESC 
LIMIT 5;

-- Clean up the function
DROP FUNCTION extract_gut_reaction(TEXT);