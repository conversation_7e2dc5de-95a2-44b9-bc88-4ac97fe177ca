'use client'

import React, { useState, useRef, useEffect } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Textarea } from '@/components/ui/textarea'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { 
  Save, 
  Send, 
  Eye, 
  Users, 
  Bot, 
  Sparkles, 
  AlertCircle, 
  CheckCircle,
  MessageSquare,
  Clock,
  Edit3
} from 'lucide-react'
import { useAuth } from '@/hooks/useAuth'

interface CollaborativeScriptEditorProps {
  campaignId: string
  assetId?: string
  initialContent?: string
  onSave?: (content: string) => void
  onSubmitForReview?: (content: string) => void
}

interface Collaborator {
  id: string
  name: string
  avatar: string
  isActive: boolean
  cursorPosition?: number
}

interface AIsuggestion {
  id: string
  type: 'grammar' | 'style' | 'brand' | 'tone'
  message: string
  suggestion: string
  position: number
  confidence: number
}

export default function CollaborativeScriptEditor({
  campaignId,
  assetId,
  initialContent = '',
  onSave,
  onSubmitForReview
}: CollaborativeScriptEditorProps) {
  const { isAuthenticated } = useAuth()
  const [content, setContent] = useState(initialContent)
  const [collaborators, setCollaborators] = useState<Collaborator[]>([
    { id: '1', name: 'Lisa Wang', avatar: 'LW', isActive: true, cursorPosition: 150 },
    { id: '2', name: 'Sarah Chen', avatar: 'SC', isActive: true, cursorPosition: 300 }
  ])
  const [aiSuggestions, setAiSuggestions] = useState<AIsuggestion[]>([
    {
      id: '1',
      type: 'brand',
      message: 'Consider emphasizing sustainability more prominently',
      suggestion: 'Add "eco-conscious" before "collection"',
      position: 200,
      confidence: 85
    },
    {
      id: '2',
      type: 'tone',
      message: 'The call-to-action could be more compelling',
      suggestion: 'Change "Shop the collection now" to "Discover your sustainable style today"',
      position: 400,
      confidence: 92
    }
  ])
  const [isGeneratingAI, setIsGeneratingAI] = useState(false)
  const [lastSaved, setLastSaved] = useState<Date | null>(null)
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false)
  const textareaRef = useRef<HTMLTextAreaElement>(null)

  useEffect(() => {
    if (content !== initialContent) {
      setHasUnsavedChanges(true)
    }
  }, [content, initialContent])

  const handleContentChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setContent(e.target.value)
    // Simulate real-time collaboration
    // In a real app, this would send cursor position and changes to other users
  }

  const handleSave = async () => {
    try {
      // Save to backend
      if (onSave) {
        onSave(content)
      }
      setLastSaved(new Date())
      setHasUnsavedChanges(false)
    } catch (error) {
      console.error('Error saving script:', error)
    }
  }

  const handleSubmitForReview = async () => {
    try {
      if (onSubmitForReview) {
        onSubmitForReview(content)
      }
    } catch (error) {
      console.error('Error submitting for review:', error)
    }
  }

  const handleAIAssist = async () => {
    setIsGeneratingAI(true)
    try {
      const response = await fetch(`/api/campaigns/${campaignId}/ai-assist`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          assistance_type: 'script_generation',
          prompt: 'Analyze and improve this script for better engagement and brand alignment',
          context: { current_script: content, asset_id: assetId }
        })
      })

      if (response.ok) {
        const data = await response.json()
        // Add AI suggestions to the list
        // In a real implementation, parse the AI response for specific suggestions
      }
    } catch (error) {
      console.error('Error getting AI assistance:', error)
    } finally {
      setIsGeneratingAI(false)
    }
  }

  const applySuggestion = (suggestion: AIsuggestion) => {
    // Apply the AI suggestion to the content
    const newContent = content.slice(0, suggestion.position) + 
                      suggestion.suggestion + 
                      content.slice(suggestion.position)
    setContent(newContent)
    
    // Remove the applied suggestion
    setAiSuggestions(prev => prev.filter(s => s.id !== suggestion.id))
  }

  const dismissSuggestion = (suggestionId: string) => {
    setAiSuggestions(prev => prev.filter(s => s.id !== suggestionId))
  }

  if (!isAuthenticated) {
    return (
      <Card>
        <CardContent className="p-6 text-center">
          <Edit3 className="w-12 h-12 mx-auto text-gray-400 mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">Sign in to edit scripts</h3>
          <p className="text-gray-600">Collaborate with your team on script development</p>
        </CardContent>
      </Card>
    )
  }

  return (
    <div className="space-y-4">
      {/* AI Suggestions */}
      {aiSuggestions.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-sm">
              <Bot className="w-4 h-4" />
              AI Suggestions
              <Badge variant="secondary">{aiSuggestions.length}</Badge>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            {aiSuggestions.map((suggestion) => (
              <div key={suggestion.id} className="bg-blue-50 border border-blue-200 rounded-lg p-3">
                <div className="flex items-start justify-between mb-2">
                  <div className="flex items-center gap-2">
                    <Badge variant="outline" className="text-xs">
                      {suggestion.type}
                    </Badge>
                    <span className="text-xs text-blue-600">
                      {suggestion.confidence}% confidence
                    </span>
                  </div>
                  <div className="flex gap-1">
                    <Button
                      size="sm"
                      variant="ghost"
                      onClick={() => applySuggestion(suggestion)}
                      className="h-6 px-2 text-xs"
                    >
                      Apply
                    </Button>
                    <Button
                      size="sm"
                      variant="ghost"
                      onClick={() => dismissSuggestion(suggestion.id)}
                      className="h-6 px-2 text-xs"
                    >
                      Dismiss
                    </Button>
                  </div>
                </div>
                <p className="text-sm text-blue-800 mb-1">{suggestion.message}</p>
                <p className="text-xs text-blue-600 italic">"{suggestion.suggestion}"</p>
              </div>
            ))}
          </CardContent>
        </Card>
      )}

      {/* Script Editor */}
      <Card>
        <CardHeader>
          <div className="flex justify-between items-center">
            <CardTitle className="flex items-center gap-2">
              <Edit3 className="w-5 h-5" />
              Script Editor
              {hasUnsavedChanges && (
                <Badge variant="outline" className="text-xs">
                  Unsaved changes
                </Badge>
              )}
            </CardTitle>
            <div className="flex items-center gap-2">
              {/* Active Collaborators */}
              <div className="flex items-center gap-2">
                <Users className="w-4 h-4 text-gray-400" />
                <div className="flex -space-x-2">
                  {collaborators.filter(c => c.isActive).map((collaborator) => (
                    <div
                      key={collaborator.id}
                      className="w-6 h-6 bg-green-500 rounded-full flex items-center justify-center text-white text-xs font-medium border-2 border-white"
                      title={`${collaborator.name} is editing`}
                    >
                      {collaborator.avatar}
                    </div>
                  ))}
                </div>
                <span className="text-xs text-gray-500">
                  {collaborators.filter(c => c.isActive).length} editing
                </span>
              </div>
            </div>
          </div>
          {lastSaved && (
            <p className="text-xs text-gray-500">
              Last saved: {lastSaved.toLocaleTimeString()}
            </p>
          )}
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <Textarea
              ref={textareaRef}
              value={content}
              onChange={handleContentChange}
              placeholder="Start writing your script here..."
              className="min-h-96 font-mono text-sm"
            />
            
            <div className="flex justify-between items-center">
              <div className="flex items-center gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleAIAssist}
                  disabled={isGeneratingAI}
                >
                  {isGeneratingAI ? (
                    <>
                      <Bot className="w-4 h-4 mr-2 animate-pulse" />
                      Analyzing...
                    </>
                  ) : (
                    <>
                      <Sparkles className="w-4 h-4 mr-2" />
                      AI Assist
                    </>
                  )}
                </Button>
                <span className="text-xs text-gray-500">
                  {content.length} characters
                </span>
              </div>
              
              <div className="flex gap-2">
                <Button variant="outline" size="sm" onClick={handleSave}>
                  <Save className="w-4 h-4 mr-2" />
                  Save Draft
                </Button>
                <Button variant="outline" size="sm">
                  <Eye className="w-4 h-4 mr-2" />
                  Preview
                </Button>
                <Button size="sm" onClick={handleSubmitForReview}>
                  <Send className="w-4 h-4 mr-2" />
                  Submit for Review
                </Button>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
