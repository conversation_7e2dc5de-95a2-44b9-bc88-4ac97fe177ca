'use client'

import React, { useState, useRef, useEffect, useCallback } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Textarea } from '@/components/ui/textarea'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { 
  Send, 
  Bot, 
  User, 
  Loader2, 
  Sparkles, 
  FileText, 
  Target, 
  Lightbulb,
  CheckCircle,
  AlertTriangle,
  TrendingUp,
  Users,
  MessageSquare
} from 'lucide-react'
import { useAuth } from '@/hooks/useAuth'
import { useUser } from '@clerk/nextjs'

interface Message {
  id: string
  role: 'user' | 'assistant'
  content: string
  timestamp: Date
  assistance_type?: string
  metadata?: any
}

interface CampaignChatInterfaceProps {
  campaignId: string
  campaignData: any
  onSuggestionApply?: (suggestion: any) => void
}

const assistanceTypes = [
  {
    id: 'script_generation',
    name: 'Script Generation',
    icon: FileText,
    description: 'Generate compelling video ad scripts',
    color: 'bg-blue-500'
  },
  {
    id: 'brand_analysis',
    name: 'Brand Analysis',
    icon: Target,
    description: 'Analyze brand alignment and consistency',
    color: 'bg-purple-500'
  },
  {
    id: 'audience_insights',
    name: 'Audience Insights',
    icon: Users,
    description: 'Get detailed audience analysis',
    color: 'bg-green-500'
  },
  {
    id: 'creative_suggestions',
    name: 'Creative Ideas',
    icon: Lightbulb,
    description: 'Generate creative concepts and ideas',
    color: 'bg-yellow-500'
  },
  {
    id: 'workflow_optimization',
    name: 'Workflow Optimization',
    icon: TrendingUp,
    description: 'Optimize campaign workflow and timeline',
    color: 'bg-orange-500'
  },
  {
    id: 'compliance_check',
    name: 'Compliance Check',
    icon: CheckCircle,
    description: 'Review compliance and regulatory requirements',
    color: 'bg-red-500'
  }
]

export default function CampaignChatInterface({ 
  campaignId, 
  campaignData, 
  onSuggestionApply 
}: CampaignChatInterfaceProps) {
  const { isAuthenticated } = useAuth()
  const { user } = useUser()
  const [messages, setMessages] = useState<Message[]>([
    {
      id: '1',
      role: 'assistant',
      content: `Hello! I'm your AI campaign assistant for "${campaignData?.name || 'this campaign'}". I can help you with script generation, brand analysis, audience insights, creative suggestions, workflow optimization, and compliance checks. What would you like to work on?`,
      timestamp: new Date()
    }
  ])
  const [input, setInput] = useState('')
  const [isLoading, setIsLoading] = useState(false)
  const [selectedAssistanceType, setSelectedAssistanceType] = useState<string | null>(null)
  const messagesEndRef = useRef<HTMLDivElement>(null)
  const textareaRef = useRef<HTMLTextAreaElement>(null)

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' })
  }

  useEffect(() => {
    scrollToBottom()
  }, [messages])

  const sendMessage = useCallback(async () => {
    if (!input.trim() || isLoading || !isAuthenticated) return

    const userMessage: Message = {
      id: Date.now().toString(),
      role: 'user',
      content: input.trim(),
      timestamp: new Date()
    }

    setMessages(prev => [...prev, userMessage])
    setInput('')
    setIsLoading(true)

    try {
      const response = await fetch(`/api/campaigns/${campaignId}/ai-assist`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          assistance_type: selectedAssistanceType || 'creative_suggestions',
          prompt: input.trim(),
          context: {
            campaign_data: campaignData,
            current_stage: campaignData?.stage,
            progress: campaignData?.progress
          }
        })
      })

      if (!response.ok) {
        throw new Error('Failed to get AI response')
      }

      const data = await response.json()
      
      const assistantMessage: Message = {
        id: (Date.now() + 1).toString(),
        role: 'assistant',
        content: data.response,
        timestamp: new Date(),
        assistance_type: data.assistance_type,
        metadata: { interaction_id: data.interaction_id }
      }

      setMessages(prev => [...prev, assistantMessage])
    } catch (error) {
      console.error('Error sending message:', error)
      const errorMessage: Message = {
        id: (Date.now() + 1).toString(),
        role: 'assistant',
        content: 'Sorry, I encountered an error while processing your request. Please try again.',
        timestamp: new Date()
      }
      setMessages(prev => [...prev, errorMessage])
    } finally {
      setIsLoading(false)
    }
  }, [input, isLoading, isAuthenticated, campaignId, selectedAssistanceType, campaignData])

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault()
      sendMessage()
    }
  }

  const handleQuickAction = (assistanceType: string, prompt: string) => {
    setSelectedAssistanceType(assistanceType)
    setInput(prompt)
    setTimeout(() => {
      textareaRef.current?.focus()
    }, 100)
  }

  if (!isAuthenticated) {
    return (
      <Card>
        <CardContent className="p-6 text-center">
          <MessageSquare className="w-12 h-12 mx-auto text-gray-400 mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">Sign in to use AI Assistant</h3>
          <p className="text-gray-600">Get AI-powered assistance for your campaign development</p>
        </CardContent>
      </Card>
    )
  }

  return (
    <div className="flex flex-col h-full">
      {/* Assistance Type Selector */}
      <div className="p-4 border-b bg-gray-50">
        <h3 className="text-sm font-medium text-gray-700 mb-3">AI Assistance Type</h3>
        <div className="grid grid-cols-2 md:grid-cols-3 gap-2">
          {assistanceTypes.map((type) => {
            const Icon = type.icon
            return (
              <button
                key={type.id}
                onClick={() => setSelectedAssistanceType(type.id)}
                className={`p-2 rounded-lg border text-left transition-all ${
                  selectedAssistanceType === type.id
                    ? 'border-blue-500 bg-blue-50 text-blue-700'
                    : 'border-gray-200 hover:border-gray-300 text-gray-700'
                }`}
              >
                <div className="flex items-center gap-2 mb-1">
                  <Icon className="w-4 h-4" />
                  <span className="text-xs font-medium">{type.name}</span>
                </div>
                <p className="text-xs text-gray-500">{type.description}</p>
              </button>
            )
          })}
        </div>
      </div>

      {/* Messages */}
      <div className="flex-1 overflow-y-auto p-4 space-y-4">
        {messages.map((message) => (
          <div
            key={message.id}
            className={`flex gap-3 ${message.role === 'user' ? 'justify-end' : 'justify-start'}`}
          >
            <div className={`flex gap-3 max-w-[80%] ${message.role === 'user' ? 'flex-row-reverse' : 'flex-row'}`}>
              <div className={`flex-shrink-0 w-8 h-8 rounded-full flex items-center justify-center ${
                message.role === 'user' 
                  ? 'bg-blue-500 text-white' 
                  : 'bg-purple-500 text-white'
              }`}>
                {message.role === 'user' ? <User className="w-4 h-4" /> : <Bot className="w-4 h-4" />}
              </div>
              <div className={`rounded-lg p-3 ${
                message.role === 'user'
                  ? 'bg-blue-500 text-white'
                  : 'bg-gray-100 text-gray-900'
              }`}>
                {message.assistance_type && (
                  <Badge variant="secondary" className="mb-2">
                    <Sparkles className="w-3 h-3 mr-1" />
                    {assistanceTypes.find(t => t.id === message.assistance_type)?.name}
                  </Badge>
                )}
                <div className="whitespace-pre-wrap text-sm">{message.content}</div>
                <div className="text-xs opacity-70 mt-2">
                  {message.timestamp.toLocaleTimeString()}
                </div>
              </div>
            </div>
          </div>
        ))}
        
        {isLoading && (
          <div className="flex justify-start">
            <div className="flex gap-3 max-w-[80%]">
              <div className="flex-shrink-0 w-8 h-8 rounded-full flex items-center justify-center bg-purple-500 text-white">
                <Bot className="w-4 h-4" />
              </div>
              <div className="rounded-lg p-3 bg-gray-100">
                <div className="flex items-center gap-2">
                  <Loader2 className="w-4 h-4 animate-spin" />
                  <span className="text-sm text-gray-600">AI is thinking...</span>
                </div>
              </div>
            </div>
          </div>
        )}
        
        <div ref={messagesEndRef} />
      </div>

      {/* Quick Actions */}
      {messages.length <= 1 && (
        <div className="p-4 border-t bg-gray-50">
          <h4 className="text-sm font-medium text-gray-700 mb-2">Quick Actions</h4>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
            <button
              onClick={() => handleQuickAction('script_generation', 'Generate a 30-second video ad script for this campaign')}
              className="p-2 text-left border border-gray-200 rounded-lg hover:border-gray-300 transition-colors"
            >
              <div className="text-sm font-medium text-gray-900">Generate Script</div>
              <div className="text-xs text-gray-500">Create a compelling video ad script</div>
            </button>
            <button
              onClick={() => handleQuickAction('audience_insights', 'Analyze the target audience for this campaign')}
              className="p-2 text-left border border-gray-200 rounded-lg hover:border-gray-300 transition-colors"
            >
              <div className="text-sm font-medium text-gray-900">Audience Analysis</div>
              <div className="text-xs text-gray-500">Get detailed audience insights</div>
            </button>
            <button
              onClick={() => handleQuickAction('creative_suggestions', 'Suggest creative concepts for this campaign')}
              className="p-2 text-left border border-gray-200 rounded-lg hover:border-gray-300 transition-colors"
            >
              <div className="text-sm font-medium text-gray-900">Creative Ideas</div>
              <div className="text-xs text-gray-500">Generate creative concepts</div>
            </button>
            <button
              onClick={() => handleQuickAction('workflow_optimization', 'Optimize the workflow for this campaign')}
              className="p-2 text-left border border-gray-200 rounded-lg hover:border-gray-300 transition-colors"
            >
              <div className="text-sm font-medium text-gray-900">Optimize Workflow</div>
              <div className="text-xs text-gray-500">Improve campaign timeline</div>
            </button>
          </div>
        </div>
      )}

      {/* Input */}
      <div className="p-4 border-t">
        <div className="flex gap-2">
          <Textarea
            ref={textareaRef}
            value={input}
            onChange={(e) => setInput(e.target.value)}
            onKeyDown={handleKeyDown}
            placeholder={`Ask for ${selectedAssistanceType ? assistanceTypes.find(t => t.id === selectedAssistanceType)?.name.toLowerCase() : 'AI assistance'}...`}
            className="min-h-[60px] max-h-[120px] resize-none"
            disabled={isLoading}
          />
          <Button
            onClick={sendMessage}
            disabled={!input.trim() || isLoading}
            size="icon"
            className="flex-shrink-0 h-[60px] w-[60px]"
          >
            {isLoading ? (
              <Loader2 className="w-4 h-4 animate-spin" />
            ) : (
              <Send className="w-4 h-4" />
            )}
          </Button>
        </div>
        
        {selectedAssistanceType && (
          <div className="mt-2 flex items-center gap-2">
            <Badge variant="outline" className="text-xs">
              <Sparkles className="w-3 h-3 mr-1" />
              {assistanceTypes.find(t => t.id === selectedAssistanceType)?.name}
            </Badge>
            <button
              onClick={() => setSelectedAssistanceType(null)}
              className="text-xs text-gray-500 hover:text-gray-700"
            >
              Clear
            </button>
          </div>
        )}
      </div>
    </div>
  )
}
