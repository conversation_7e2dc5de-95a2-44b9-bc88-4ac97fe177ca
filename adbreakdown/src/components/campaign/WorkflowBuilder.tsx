'use client'

import React, { useState } from 'react'
import { <PERSON><PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { 
  Plus, 
  Edit, 
  Trash2, 
  CheckCircle, 
  Clock, 
  PlayCircle, 
  AlertTriangle,
  ArrowRight,
  Calendar,
  User
} from 'lucide-react'

interface WorkflowStage {
  id: string
  name: string
  description: string
  status: 'pending' | 'in-progress' | 'completed' | 'blocked' | 'skipped'
  stage_order: number
  due_date?: string
  assigned_to?: string
  depends_on: string[]
}

interface WorkflowBuilderProps {
  campaignId: string
  stages: WorkflowStage[]
  onStagesUpdate: (stages: WorkflowStage[]) => void
}

const statusConfig = {
  pending: { color: 'bg-gray-400', label: 'Pending', icon: Clock },
  'in-progress': { color: 'bg-blue-500', label: 'In Progress', icon: PlayCircle },
  completed: { color: 'bg-green-500', label: 'Completed', icon: CheckCircle },
  blocked: { color: 'bg-red-500', label: 'Blocked', icon: AlertTriangle },
  skipped: { color: 'bg-yellow-500', label: 'Skipped', icon: ArrowRight }
}

export default function WorkflowBuilder({ campaignId, stages, onStagesUpdate }: WorkflowBuilderProps) {
  const [editingStage, setEditingStage] = useState<string | null>(null)
  const [newStage, setNewStage] = useState({
    name: '',
    description: '',
    due_date: '',
    assigned_to: ''
  })
  const [showAddForm, setShowAddForm] = useState(false)

  const addStage = () => {
    if (!newStage.name.trim()) return

    const stage: WorkflowStage = {
      id: Date.now().toString(),
      name: newStage.name,
      description: newStage.description,
      status: 'pending',
      stage_order: stages.length + 1,
      due_date: newStage.due_date || undefined,
      assigned_to: newStage.assigned_to || undefined,
      depends_on: []
    }

    onStagesUpdate([...stages, stage])
    setNewStage({ name: '', description: '', due_date: '', assigned_to: '' })
    setShowAddForm(false)
  }

  const updateStageStatus = (stageId: string, status: WorkflowStage['status']) => {
    const updatedStages = stages.map(stage =>
      stage.id === stageId ? { ...stage, status } : stage
    )
    onStagesUpdate(updatedStages)
  }

  const deleteStage = (stageId: string) => {
    const updatedStages = stages
      .filter(stage => stage.id !== stageId)
      .map((stage, index) => ({ ...stage, stage_order: index + 1 }))
    onStagesUpdate(updatedStages)
  }

  const moveStage = (stageId: string, direction: 'up' | 'down') => {
    const stageIndex = stages.findIndex(s => s.id === stageId)
    if (
      (direction === 'up' && stageIndex === 0) ||
      (direction === 'down' && stageIndex === stages.length - 1)
    ) {
      return
    }

    const newStages = [...stages]
    const targetIndex = direction === 'up' ? stageIndex - 1 : stageIndex + 1
    
    // Swap stages
    [newStages[stageIndex], newStages[targetIndex]] = [newStages[targetIndex], newStages[stageIndex]]
    
    // Update stage orders
    newStages.forEach((stage, index) => {
      stage.stage_order = index + 1
    })

    onStagesUpdate(newStages)
  }

  const getProgressPercentage = () => {
    if (stages.length === 0) return 0
    const completedStages = stages.filter(s => s.status === 'completed').length
    return Math.round((completedStages / stages.length) * 100)
  }

  return (
    <div className="space-y-6">
      {/* Progress Overview */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <span>Workflow Progress</span>
            <Badge variant="outline">{getProgressPercentage()}% Complete</Badge>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="w-full bg-gray-200 rounded-full h-2">
            <div 
              className="bg-blue-500 h-2 rounded-full transition-all duration-300"
              style={{ width: `${getProgressPercentage()}%` }}
            />
          </div>
          <div className="flex justify-between text-sm text-gray-600 mt-2">
            <span>{stages.filter(s => s.status === 'completed').length} completed</span>
            <span>{stages.filter(s => s.status === 'in-progress').length} in progress</span>
            <span>{stages.filter(s => s.status === 'pending').length} pending</span>
          </div>
        </CardContent>
      </Card>

      {/* Workflow Stages */}
      <div className="space-y-4">
        {stages.map((stage, index) => {
          const StatusIcon = statusConfig[stage.status].icon
          const isLast = index === stages.length - 1

          return (
            <div key={stage.id} className="relative">
              <Card className="hover:shadow-md transition-shadow">
                <CardContent className="p-4">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-4 flex-1">
                      {/* Stage Number & Status */}
                      <div className="flex flex-col items-center">
                        <div className={`w-8 h-8 rounded-full flex items-center justify-center ${statusConfig[stage.status].color} text-white font-medium text-sm`}>
                          {stage.status === 'completed' ? (
                            <CheckCircle className="w-4 h-4" />
                          ) : (
                            stage.stage_order
                          )}
                        </div>
                        {!isLast && (
                          <div className="w-px h-8 bg-gray-200 mt-2" />
                        )}
                      </div>

                      {/* Stage Details */}
                      <div className="flex-1">
                        <div className="flex items-center gap-2 mb-1">
                          <h3 className="font-medium">{stage.name}</h3>
                          <Badge className={`${statusConfig[stage.status].color} text-white text-xs`}>
                            <StatusIcon className="w-3 h-3 mr-1" />
                            {statusConfig[stage.status].label}
                          </Badge>
                        </div>
                        {stage.description && (
                          <p className="text-sm text-gray-600 mb-2">{stage.description}</p>
                        )}
                        <div className="flex items-center gap-4 text-xs text-gray-500">
                          {stage.due_date && (
                            <div className="flex items-center gap-1">
                              <Calendar className="w-3 h-3" />
                              Due: {new Date(stage.due_date).toLocaleDateString()}
                            </div>
                          )}
                          {stage.assigned_to && (
                            <div className="flex items-center gap-1">
                              <User className="w-3 h-3" />
                              {stage.assigned_to}
                            </div>
                          )}
                        </div>
                      </div>
                    </div>

                    {/* Actions */}
                    <div className="flex items-center gap-2">
                      {stage.status === 'pending' && (
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => updateStageStatus(stage.id, 'in-progress')}
                        >
                          Start
                        </Button>
                      )}
                      {stage.status === 'in-progress' && (
                        <Button
                          size="sm"
                          onClick={() => updateStageStatus(stage.id, 'completed')}
                        >
                          Complete
                        </Button>
                      )}
                      <Button
                        size="sm"
                        variant="ghost"
                        onClick={() => setEditingStage(stage.id)}
                      >
                        <Edit className="w-4 h-4" />
                      </Button>
                      <Button
                        size="sm"
                        variant="ghost"
                        onClick={() => deleteStage(stage.id)}
                        className="text-red-600 hover:text-red-700"
                      >
                        <Trash2 className="w-4 h-4" />
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          )
        })}
      </div>

      {/* Add New Stage */}
      {showAddForm ? (
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Add New Stage</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <Input
              placeholder="Stage name"
              value={newStage.name}
              onChange={(e) => setNewStage(prev => ({ ...prev, name: e.target.value }))}
            />
            <Textarea
              placeholder="Stage description (optional)"
              value={newStage.description}
              onChange={(e) => setNewStage(prev => ({ ...prev, description: e.target.value }))}
              rows={2}
            />
            <div className="grid grid-cols-2 gap-4">
              <Input
                type="date"
                placeholder="Due date"
                value={newStage.due_date}
                onChange={(e) => setNewStage(prev => ({ ...prev, due_date: e.target.value }))}
              />
              <Input
                placeholder="Assigned to"
                value={newStage.assigned_to}
                onChange={(e) => setNewStage(prev => ({ ...prev, assigned_to: e.target.value }))}
              />
            </div>
            <div className="flex gap-2">
              <Button onClick={addStage}>Add Stage</Button>
              <Button variant="outline" onClick={() => setShowAddForm(false)}>
                Cancel
              </Button>
            </div>
          </CardContent>
        </Card>
      ) : (
        <Button
          variant="outline"
          onClick={() => setShowAddForm(true)}
          className="w-full border-dashed"
        >
          <Plus className="w-4 h-4 mr-2" />
          Add New Stage
        </Button>
      )}
    </div>
  )
}
