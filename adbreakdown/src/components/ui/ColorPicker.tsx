'use client'

import React from 'react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
// Using Button component instead of RadioGroup for simpler implementation

interface ColorPickerProps {
  label: string
  type: 'gradient' | 'solid'
  onTypeChange: (type: 'gradient' | 'solid') => void
  solidColor?: string
  onSolidColorChange: (color: string) => void
  gradientStart?: string
  gradientMiddle?: string
  gradientEnd?: string
  onGradientStartChange: (color: string) => void
  onGradientMiddleChange: (color: string) => void
  onGradientEndChange: (color: string) => void
  showMiddleColor?: boolean
}

export function ColorPicker({
  label,
  type,
  onTypeChange,
  solidColor = '#000000',
  onSolidColorChange,
  gradientStart = '#4338ca',
  gradientMiddle = '#7c3aed',
  gradientEnd = '#2563eb',
  onGradientStartChange,
  onGradientMiddleChange,
  onGradientEndChange,
  showMiddleColor = true
}: ColorPickerProps) {
  return (
    <div className="space-y-3">
      <Label className="text-sm font-medium">{label}</Label>
      
      <div className="flex gap-2">
        <Button
          type="button"
          size="sm"
          variant={type === 'solid' ? 'default' : 'outline'}
          onClick={() => onTypeChange('solid')}
        >
          Solid Color
        </Button>
        <Button
          type="button"
          size="sm"
          variant={type === 'gradient' ? 'default' : 'outline'}
          onClick={() => onTypeChange('gradient')}
        >
          Gradient
        </Button>
      </div>

      {type === 'solid' ? (
        <div className="flex gap-2 items-center">
          <Input
            type="color"
            value={solidColor}
            onChange={(e) => onSolidColorChange(e.target.value)}
            className="w-16 h-10 p-1 border rounded cursor-pointer"
          />
          <Input
            value={solidColor}
            onChange={(e) => onSolidColorChange(e.target.value)}
            placeholder="#000000"
            className="flex-1 font-mono text-sm"
          />
        </div>
      ) : (
        <div className="space-y-2">
          <div className="flex gap-2 items-center">
            <Label className="text-xs text-gray-600 w-12">Start:</Label>
            <Input
              type="color"
              value={gradientStart}
              onChange={(e) => onGradientStartChange(e.target.value)}
              className="w-12 h-8 p-1 border rounded cursor-pointer"
            />
            <Input
              value={gradientStart}
              onChange={(e) => onGradientStartChange(e.target.value)}
              placeholder="#4338ca"
              className="flex-1 font-mono text-xs"
            />
          </div>
          
          {showMiddleColor && (
            <div className="flex gap-2 items-center">
              <Label className="text-xs text-gray-600 w-12">Middle:</Label>
              <Input
                type="color"
                value={gradientMiddle}
                onChange={(e) => onGradientMiddleChange(e.target.value)}
                className="w-12 h-8 p-1 border rounded cursor-pointer"
              />
              <Input
                value={gradientMiddle}
                onChange={(e) => onGradientMiddleChange(e.target.value)}
                placeholder="#7c3aed"
                className="flex-1 font-mono text-xs"
              />
            </div>
          )}
          
          <div className="flex gap-2 items-center">
            <Label className="text-xs text-gray-600 w-12">End:</Label>
            <Input
              type="color"
              value={gradientEnd}
              onChange={(e) => onGradientEndChange(e.target.value)}
              className="w-12 h-8 p-1 border rounded cursor-pointer"
            />
            <Input
              value={gradientEnd}
              onChange={(e) => onGradientEndChange(e.target.value)}
              placeholder="#2563eb"
              className="flex-1 font-mono text-xs"
            />
          </div>
        </div>
      )}
    </div>
  )
}