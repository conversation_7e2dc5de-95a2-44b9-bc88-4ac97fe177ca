'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'
import { Shield, ChevronRight, Lock, Eye, AlertTriangle, CheckCircle2, Crown, Upload, X, Trash2 } from 'lucide-react'
import { Progress } from '@/components/ui/progress'
import Link from 'next/link'
import Image from 'next/image'
import { useUser } from '@clerk/nextjs'

interface PrivateAnalysis {
  id: string
  slug: string
  title: string
  inferred_brand: string
  youtube_url: string
  status: string
  created_at: string
  thumbnail_url?: string
}

interface PrivateAnalysisCardProps {
  creditsRemaining: number
}

export default function PrivateAnalysisCard({ creditsRemaining }: PrivateAnalysisCardProps) {
  const [privateLoading, setPrivateLoading] = useState(false)
  const [privateAnalyses, setPrivateAnalyses] = useState<PrivateAnalysis[]>([])
  const [analysesLoading, setAnalysesLoading] = useState(true)
  const [youtubeUrl, setYoutubeUrl] = useState('')
  const [error, setError] = useState('')
  const [showUploadModal, setShowUploadModal] = useState(false)
  const [uploadForm, setUploadForm] = useState({
    brand: '',
    title: '',
    file: null as File | null
  })
  const [uploadProgress, setUploadProgress] = useState(0)
  const [isUploading, setIsUploading] = useState(false)
  const [isAnalyzingFile, setIsAnalyzingFile] = useState(false)
  const [deletingAnalysis, setDeletingAnalysis] = useState<string | null>(null)
  const { user } = useUser()


  useEffect(() => {
    const fetchPrivateAnalyses = async () => {
      try {
        setAnalysesLoading(true)
        const response = await fetch('/api/analyses/private?limit=5')
        if (response.ok) {
          const data = await response.json()
          setPrivateAnalyses(data.analyses || [])
        }
      } catch (error) {
        console.error('Error fetching private analyses:', error)
      } finally {
        setAnalysesLoading(false)
      }
    }

    fetchPrivateAnalyses()
  }, [])


  const handlePrivateAnalyze = async (youtubeUrl: string) => {
    try {
      setPrivateLoading(true)
      setError('')
      console.log('🔒 Starting private analysis:', { youtubeUrl })

      const response = await fetch('/api/analyses/private', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ youtubeUrl })
      })

      const data = await response.json()

      if (!response.ok) {
        if (data.requiresAuth) {
          throw new Error('OAuth authentication required')
        }
        throw new Error(data.error || 'Failed to create private analysis')
      }

      // Navigate to pre-launch analysis page
      console.log('✅ Private analysis created, redirecting to:', data.redirect)
      if (data.redirect) {
        window.location.href = data.redirect
      } else {
        window.location.href = `/ad/pre-launch/${data.analysis.slug}`
      }

    } catch (error: any) {
      console.error('❌ Error creating private analysis:', error)
      setError(error.message || 'An error occurred while processing your request')
    } finally {
      setPrivateLoading(false)
    }
  }

  const handleSubmit = async () => {
    if (!youtubeUrl.trim()) {
      setError('Please enter a URL')
      return
    }

    // Check credits before proceeding
    if (creditsRemaining <= 0) {
      setError('Insufficient credits. Please upgrade your plan.')
      return
    }

    // Validate social media URL format
    const socialMediaRegex = /^(https?:\/\/)?(www\.)?(youtube\.com|youtu\.be|instagram\.com|facebook\.com|fb\.watch)/
    if (!socialMediaRegex.test(youtubeUrl)) {
      setError('Please enter a valid YouTube URL')
      return
    }

    // 🚫 Temporary Feature Restriction (only in production):
    if (process.env.NODE_ENV === 'production') {
      // In production, reject Instagram and Facebook URLs immediately.
      // This can be enabled later by removing this check.
      if (/instagram\.com|facebook\.com|fb\.watch/.test(youtubeUrl)) {
        setError('Instagram and Facebook video analysis is currently disabled. Consider uploading the video file directly.')
        return
      }

      // 📢 Additional YouTube restriction:
      // Only allow public YouTube URLs for now.
      // This check rejects URLs that contain privacy indicators such as "list=" for unlisted or require OAuth/private access.
      // In production, replace with a call to backend to determine privacy status before allowing.
      const youtubePublicRegex = /^(https?:\/\/)?(www\.)?(youtube\.com|youtu\.be)\/(watch\?v=|shorts\/)[\w-]{11}$/
      if (!youtubePublicRegex.test(youtubeUrl)) {
        setError('Only public YouTube video URLs are supported at this time. Consider uploading the video file directly.')
        return
      }
    }

    await handlePrivateAnalyze(youtubeUrl)
    setYoutubeUrl('')
  }

  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (file) {
      // Check file type (video files)
      const allowedTypes = ['video/mp4', 'video/webm', 'video/ogg', 'video/avi', 'video/mov', 'video/wmv']
      if (!allowedTypes.includes(file.type)) {
        setError('Please select a valid video file (MP4, WebM, OGG, AVI, MOV, WMV)')
        return
      }
      
      // Check file size (limit to 100MB)
      const maxSize = 100 * 1024 * 1024 // 100MB
      if (file.size > maxSize) {
        setError('File size must be under 100MB')
        return
      }
      
      setUploadForm(prev => ({ ...prev, file }))
      setError('')
    }
  }

  const handleUploadAnalyze = async () => {
    if (!uploadForm.brand.trim() || !uploadForm.title.trim() || !uploadForm.file) {
      setError('Please fill in all fields and select a file')
      return
    }

    if (creditsRemaining <= 0) {
      setError('Insufficient credits. Please upgrade your plan.')
      return
    }

    setIsUploading(true)
    setUploadProgress(0)
    setError('')

    try {
      // 1. Get signed URL from your API
      console.log('Requesting signed URL for upload modal...')
      const signedUrlRes = await fetch('/api/uploads/signed-url', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ fileName: uploadForm.file.name, contentType: uploadForm.file.type }),
      })

      if (!signedUrlRes.ok) {
        const errorData = await signedUrlRes.json()
        throw new Error(errorData.error || 'Failed to get signed URL')
      }

      const { url, fileName: uniqueFileName } = await signedUrlRes.json()
      console.log('Received signed URL:', url)

      // 2. Upload file directly to GCS using the signed URL
      console.log('Uploading file to GCS...')
      const xhr = new XMLHttpRequest()
      xhr.open('PUT', url)
      xhr.setRequestHeader('Content-Type', uploadForm.file.type)

      xhr.upload.onprogress = (event) => {
        if (event.lengthComputable) {
          const percentCompleted = Math.round((event.loaded * 100) / event.total)
          setUploadProgress(percentCompleted)
          console.log(`Upload progress: ${percentCompleted}%`)
        }
      }

      await new Promise<void>((resolve, reject) => {
        xhr.onload = () => {
          console.log('XHR onload - Status:', xhr.status, 'StatusText:', xhr.statusText)
          if (xhr.status >= 200 && xhr.status < 300) {
            console.log('File uploaded successfully to GCS.')
            resolve()
          } else {
            console.error('GCS upload failed - Status:', xhr.status, 'StatusText:', xhr.statusText)
            console.error('Response text:', xhr.responseText)
            reject(new Error(`GCS upload failed: ${xhr.status} ${xhr.statusText}`))
          }
        }
        xhr.onerror = (event) => {
          console.error('XHR onerror event:', event)
          reject(new Error(`Network error during file upload. Status: ${xhr.status}, ReadyState: ${xhr.readyState}`))
        }
        xhr.ontimeout = () => {
          console.error('XHR timeout during GCS upload')
          reject(new Error('Upload timeout - please try again with a smaller file'))
        }
        xhr.send(uploadForm.file)
      })

      setIsUploading(false)
      setIsAnalyzingFile(true)
      console.log('Creating analysis entry in database...')

      // 3. Create analysis using the main private analysis endpoint with GCS URI
      const analysisRes = await fetch('/api/analyses/private', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ 
          gcsUri: `gs://${process.env.NEXT_PUBLIC_GCS_BUCKET_NAME}/${uniqueFileName}`,
          brand: uploadForm.brand,
          title: uploadForm.title
        }),
      })

      if (!analysisRes.ok) {
        const errorData = await analysisRes.json()
        throw new Error(errorData.error || 'Failed to create analysis')
      }

      const analysisData = await analysisRes.json()
      console.log('Private analysis created successfully!', analysisData)
      
      // Reset form and close modal immediately
      setUploadForm({ brand: '', title: '', file: null })
      setUploadProgress(0)
      setIsAnalyzingFile(false)
      setShowUploadModal(false)
      
      // Navigate to the analysis page immediately (analysis will continue in background)
      console.log('✅ Navigating to analysis page:', analysisData.redirect)
      if (analysisData.redirect) {
        window.location.href = analysisData.redirect
      } else {
        // Fallback redirect
        window.location.href = `/ad/pre-launch/${analysisData.analysis.slug}`
      }

    } catch (error: any) {
      console.error('❌ Error uploading video:', error)
      setError(error.message || 'An error occurred while uploading your video')
      setIsUploading(false)
      setIsAnalyzingFile(false)
    }
  }

  const resetUploadForm = () => {
    setUploadForm({ brand: '', title: '', file: null })
    setError('')
    setUploadProgress(0)
    setIsUploading(false)
    setIsAnalyzingFile(false)
    setShowUploadModal(false)
  }

  const handleDeleteAnalysis = async (analysis: PrivateAnalysis, event: React.MouseEvent) => {
    event.preventDefault() // Prevent navigation to analysis page
    event.stopPropagation()

    const confirmDelete = window.confirm(
      `Are you sure you want to delete "${analysis.title}"?\n\n` +
      'This action cannot be undone. ' +
      (analysis.youtube_url.startsWith('gs://') 
        ? 'The uploaded video file will also be permanently deleted from storage.' 
        : 'This will remove the analysis from your account.')
    )

    if (!confirmDelete) return

    try {
      setDeletingAnalysis(analysis.id)
      
      const response = await fetch(`/api/analyses/private/${analysis.slug}/delete`, {
        method: 'DELETE',
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to delete analysis')
      }

      const result = await response.json()
      console.log('✅ Analysis deleted successfully:', result)

      // Remove the analysis from the local state
      setPrivateAnalyses(prev => prev.filter(a => a.id !== analysis.id))
      
    } catch (error: any) {
      console.error('❌ Error deleting analysis:', error)
      setError(`Failed to delete analysis: ${error.message}`)
    } finally {
      setDeletingAnalysis(null)
    }
  }

  return (
    <Card className="flex flex-col bg-gradient-to-br from-blue-150 via-indigo-150 to-purple-300 border-gray-200 shadow-sm hover:shadow-md transition-all duration-200">
      <CardHeader className="flex-none border-b border-gray-200 bg-white rounded-t-lg">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <div className="p-1.5 bg-slate-100 rounded-lg">
              <Shield className="h-5 w-5 text-slate-600" />
            </div>
            <div>
              <CardTitle className="text-lg text-gray-900">Pre-Launch Analysis</CardTitle>
              <CardDescription className="text-gray-600 text-sm">
                Private video analysis with optimization insights
              </CardDescription>
            </div>
          </div>
          <div className="flex items-center gap-2">
            <div className="px-2 py-1 bg-slate-100 text-slate-700 rounded-full text-xs font-medium">
              BETA
            </div>
          </div>
        </div>
      </CardHeader>
      
      <CardContent className="flex-1 flex flex-col space-y-4 p-6">
        {/* Supported Video Types */}
        <div className="flex-none space-y-4">
          <div className="bg-white/80 border border-slate-200 rounded-lg p-4">
            <h3 className="text-sm font-semibold text-gray-900 mb-3">Supported Video Types</h3>
            <div className="space-y-2">
              <div className="flex items-center gap-2">
                <CheckCircle2 className="h-4 w-4 text-green-600" />
                <span className="text-sm text-gray-700">YouTube Public URLs</span>
              </div>
              <div className="flex items-center gap-2">
                <CheckCircle2 className="h-4 w-4 text-green-600" />
                <span className="text-sm text-gray-700">Upload Video File</span>
              </div>
              <div className="flex items-center gap-2">
                <X className="h-4 w-4 text-red-500" />
                <span className="text-sm text-gray-500 line-through">YouTube Unlisted URLs</span>
              </div>
              <div className="flex items-center gap-2">
                <X className="h-4 w-4 text-red-500" />
                <span className="text-sm text-gray-500 line-through">YouTube Private URLs</span>
              </div>
              <div className="flex items-center gap-2">
                <X className="h-4 w-4 text-red-500" />
                <span className="text-sm text-gray-500 line-through">Instagram Reels</span>
              </div>
              <div className="flex items-center gap-2">
                <X className="h-4 w-4 text-red-500" />
                <span className="text-sm text-gray-500 line-through">Facebook Videos</span>
              </div>

            </div>
          </div>

          {/* URL Input */}
          <div className="space-y-4">
            <div className="space-y-2">
              <Input
                type="url"
                placeholder="Enter YouTube, Instagram, or Facebook URL"
                value={youtubeUrl}
                onChange={(e) => {
                  setYoutubeUrl(e.target.value)
                  setError('')
                }}
                className="border-slate-200 focus:border-slate-400 focus:ring-slate-400/20 bg-white/80 placeholder:text-slate-400"
                disabled={privateLoading}
              />
              
              {error && (
                <div className="flex items-center gap-2 p-2 bg-red-50 border border-red-200 rounded-lg">
                  <AlertTriangle className="h-4 w-4 text-red-600 flex-shrink-0" />
                  <p className="text-sm text-red-700">{error}</p>
                </div>
              )}

              <Button 
                onClick={handleSubmit}
                disabled={privateLoading || !youtubeUrl.trim()}
                className="w-full bg-slate-800 hover:bg-slate-900 text-white border-0 shadow-sm"
              >
                {privateLoading ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                    Analyzing...
                  </>
                ) : (
                  <>
                    <Eye className="h-4 w-4 mr-2" />
                    Analyze Video
                  </>
                )}
              </Button>
            </div>

            {/* OR Separator */}
            <div className="relative">
              <div className="absolute inset-0 flex items-center">
                <span className="w-full border-t border-slate-200" />
              </div>
              <div className="relative flex justify-center text-xs uppercase">
                <span className="bg-white px-2 text-slate-500">Or</span>
              </div>
            </div>

            {/* Upload File Button */}
            <Dialog open={showUploadModal} onOpenChange={(open) => {
              if (!isUploading && !isAnalyzingFile) {
                setShowUploadModal(open)
              }
            }}>
              <DialogTrigger asChild>
                <Button
                  variant="outline"
                  className="w-full border-slate-200 bg-blue-200 hover:bg-blue-250"
                >
                  <Upload className="h-4 w-4 mr-2" />
                  Upload Video File
                </Button>
              </DialogTrigger>
              
              <DialogContent className="sm:max-w-md">
                <DialogHeader>
                  <div className="flex items-center justify-between">
                    <DialogTitle className="text-lg font-semibold">Upload Video for Analysis</DialogTitle>
                    {!isUploading && !isAnalyzingFile && (
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={resetUploadForm}
                        className="h-6 w-6 p-0"
                      >
                        <X className="h-4 w-4" />
                      </Button>
                    )}
                  </div>
                </DialogHeader>
                
                <div className="space-y-4 py-4">
                  {/* Brand Input */}
                  <div className="space-y-2">
                    <label className="text-sm font-medium text-gray-700">Brand Name</label>
                    <Input
                      type="text"
                      placeholder="Enter brand name"
                      value={uploadForm.brand}
                      onChange={(e) => setUploadForm(prev => ({ ...prev, brand: e.target.value }))}
                      className="w-full"
                      disabled={isUploading || isAnalyzingFile}
                    />
                  </div>
                  
                  {/* Title Input */}
                  <div className="space-y-2">
                    <label className="text-sm font-medium text-gray-700">Ad Title</label>
                    <Input
                      type="text"
                      placeholder="Enter ad title"
                      value={uploadForm.title}
                      onChange={(e) => setUploadForm(prev => ({ ...prev, title: e.target.value }))}
                      className="w-full"
                      disabled={isUploading || isAnalyzingFile}
                    />
                  </div>
                  
                  {/* File Upload */}
                  <div className="space-y-2">
                    <label className="text-sm font-medium text-gray-700">Video File</label>
                    <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-gray-400 transition-colors">
                      {uploadForm.file ? (
                        <div className="space-y-2">
                          <div className="flex items-center justify-center gap-2 text-green-600">
                            <CheckCircle2 className="h-5 w-5" />
                            <span className="text-sm font-medium">File Selected</span>
                          </div>
                          <p className="text-sm text-gray-600">{uploadForm.file.name}</p>
                          <p className="text-xs text-gray-500">
                            {(uploadForm.file.size / (1024 * 1024)).toFixed(2)} MB
                          </p>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => setUploadForm(prev => ({ ...prev, file: null }))}
                            disabled={isUploading || isAnalyzingFile}
                          >
                            Change File
                          </Button>
                        </div>
                      ) : (
                        <div className="space-y-2">
                          <Upload className="h-8 w-8 text-gray-400 mx-auto" />
                          <div>
                            <label htmlFor="video-upload" className="cursor-pointer">
                              <span className="text-sm font-medium text-blue-600 hover:text-blue-500">
                                Click to upload
                              </span>
                              <span className="text-sm text-gray-500"> or drag and drop</span>
                            </label>
                            <input
                              id="video-upload"
                              type="file"
                              accept="video/*"
                              onChange={handleFileUpload}
                              className="hidden"
                              disabled={isUploading || isAnalyzingFile}
                            />
                          </div>
                          <p className="text-xs text-gray-500">
                            MP4, WebM, OGG, AVI, MOV, WMV up to 100MB
                          </p>
                        </div>
                      )}
                    </div>
                  </div>

                  {/* Progress Display */}
                  {(isUploading || isAnalyzingFile) && (
                    <div className="space-y-3">
                      <Progress value={isUploading ? uploadProgress : 100} className="w-full h-3" />
                      <div className="text-center">
                        {isUploading && (
                          <div className="space-y-2">
                            <p className="text-lg font-semibold text-blue-600">
                              {uploadProgress}%
                            </p>
                            <p className="text-sm text-gray-600">
                              Uploading to secure cloud storage...
                            </p>
                          </div>
                        )}
                        
                        {isAnalyzingFile && (
                          <div className="space-y-2">
                            <div className="flex items-center justify-center gap-2">
                              <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-blue-600"></div>
                              <p className="text-lg font-semibold text-blue-600">
                                Creating Analysis
                              </p>
                            </div>
                            <p className="text-sm text-gray-600">
                              Setting up your private analysis...
                            </p>
                          </div>
                        )}
                      </div>
                    </div>
                  )}

                  {/* Error Display */}
                  {error && (
                    <div className="flex items-center gap-2 p-3 bg-red-50 border border-red-200 rounded-lg">
                      <AlertTriangle className="h-4 w-4 text-red-600 flex-shrink-0" />
                      <p className="text-sm text-red-700">{error}</p>
                    </div>
                  )}

                  {/* Action Buttons */}
                  <div className="flex gap-3 pt-4">
                    <Button
                      variant="outline"
                      onClick={resetUploadForm}
                      className="flex-1"
                      disabled={isUploading || isAnalyzingFile}
                    >
                      Cancel
                    </Button>
                    <Button
                      onClick={handleUploadAnalyze}
                      disabled={isUploading || isAnalyzingFile || !uploadForm.brand.trim() || !uploadForm.title.trim() || !uploadForm.file}
                      className="flex-1 bg-slate-800 hover:bg-slate-900 text-white"
                    >
                      {isUploading ? (
                        <>
                          <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                          Uploading...
                        </>
                      ) : isAnalyzingFile ? (
                        <>
                          <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                          Analyzing...
                        </>
                      ) : (
                        <>
                          <Eye className="h-4 w-4 mr-2" />
                          Analyze
                        </>
                      )}
                    </Button>
                  </div>
                </div>
              </DialogContent>
            </Dialog>
          </div>
        </div>
        
        {/* Recent Private Analyses List */}
        <div className="flex-1 min-h-0">
          <div className="flex items-center justify-between mb-3">
            <h4 className="font-medium text-gray-900 text-sm">Recent Private Analyses</h4>
            <Link href="/pre-launch-testing">
              <Button variant="ghost" size="sm" className="text-slate-600 hover:text-slate-800 hover:bg-slate-100 h-8 px-2">
                <span className="text-xs">View All</span>
                <ChevronRight className="h-3 w-3 ml-1" />
              </Button>
            </Link>
          </div>
          
          <div className="space-y-2 max-h-[280px] overflow-y-auto">
            {analysesLoading ? (
              <div className="flex items-center justify-center py-8">
                <div className="animate-spin h-5 w-5 border-b-2 border-slate-400"></div>
              </div>
            ) : privateAnalyses.length > 0 ? (
              privateAnalyses.map((analysis) => (
                <div key={analysis.id} className="group border border-slate-200 rounded-lg p-3 hover:bg-white/80 hover:border-slate-300 hover:shadow-sm transition-all duration-200">
                    <div className="flex items-center gap-3">
                      {analysis.thumbnail_url && (
                        <div className="relative">
                          <Image 
                            src={analysis.thumbnail_url} 
                            alt={analysis.title}
                            width={60}
                            height={40}
                            className="rounded-md object-cover border border-slate-200"
                          />
                          <div className="absolute -top-1 -right-1 p-0.5 bg-slate-800 rounded-full">
                            <Lock className="h-2.5 w-2.5 text-white" />
                          </div>
                        </div>
                      )}
                      <Link href={`/ad/pre-launch/${analysis.slug}`} className="flex-1 min-w-0">
                        <div className="cursor-pointer">
                          <p className="font-medium text-sm text-slate-900 truncate group-hover:text-slate-700">
                            {analysis.title}
                          </p>
                          <p className="text-xs text-slate-500">{analysis.inferred_brand}</p>
                          <div className="flex items-center gap-2 mt-1.5">
                            <span className={`text-xs px-2 py-0.5 rounded-full font-medium ${
                              analysis.status === 'completed' ? 'bg-green-100 text-green-700 border border-green-200' :
                              analysis.status === 'processing' ? 'bg-blue-100 text-blue-700 border border-blue-200' :
                              'bg-amber-100 text-amber-700 border border-amber-200'
                            }`}>
                              {analysis.status}
                            </span>
                            <span className="text-xs text-slate-400">
                              {new Date(analysis.created_at).toLocaleDateString()}
                            </span>
                          </div>
                        </div>
                      </Link>
                      <div className="flex items-center gap-1">
                        <Button
                          size="sm"
                          variant="ghost"
                          onClick={(e) => handleDeleteAnalysis(analysis, e)}
                          disabled={deletingAnalysis === analysis.id}
                          className="h-6 w-6 p-0 text-red-500 hover:text-red-700 hover:bg-red-50 opacity-0 group-hover:opacity-100 transition-opacity"
                        >
                          {deletingAnalysis === analysis.id ? (
                            <div className="animate-spin h-3 w-3 border border-red-500 border-t-transparent rounded-full" />
                          ) : (
                            <Trash2 className="h-3 w-3" />
                          )}
                        </Button>
                        <ChevronRight className="h-4 w-4 text-slate-400 group-hover:text-slate-600 transition-colors" />
                      </div>
                    </div>
                  </div>
                ))
              ) : (
                <div className="text-center py-8 text-slate-500">
                  <div className="p-3 bg-slate-100 rounded-xl inline-block mb-3">
                    <Shield className="h-8 w-8 text-slate-400" />
                  </div>
                  <p className="text-sm">No private analyses yet</p>
                  <p className="text-xs text-slate-400 mt-1">Analyze your first private video above</p>
                </div>
              )}
            </div>
        </div>
      </CardContent>
    </Card>
  )
}