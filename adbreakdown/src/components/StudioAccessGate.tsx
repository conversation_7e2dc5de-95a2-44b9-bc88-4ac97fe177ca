'use client'

import { useEffect, useState } from 'react'
import { useAuth } from '@/hooks/useAuth'
import { useRouter, usePathname } from 'next/navigation'

interface StudioAccessGateProps {
  children: React.ReactNode
}

export default function StudioAccessGate({ children }: StudioAccessGateProps) {
  const { isAuthenticated, loading: authLoading } = useAuth()
  const router = useRouter()
  const pathname = usePathname()
  const [hasStudioAccess, setHasStudioAccess] = useState<boolean | null>(null)

  useEffect(() => {
    const checkStudioAccess = async () => {
      // Skip check if not authenticated, not on a studio route, or already on waitlist page
      if (!isAuthenticated || !pathname?.startsWith('/studio') || pathname === '/studio/waitlist') {
        setHasStudioAccess(true) // Allow access for non-studio routes or waitlist page
        return
      }
      
      try {
        const response = await fetch('/api/studio/access-check')
        if (response.ok) {
          const { hasAccess } = await response.json()
          setHasStudioAccess(hasAccess)
          
          // If no access and not already on waitlist page, redirect to waitlist
          if (!hasAccess && pathname !== '/studio/waitlist') {
            console.log('🚫 Studio access denied, redirecting to waitlist')
            router.push('/studio/waitlist')
          }
        } else {
          console.error('Failed to check studio access')
          setHasStudioAccess(false)
          // Redirect to waitlist on error (fail closed)
          if (pathname !== '/studio/waitlist') {
            router.push('/studio/waitlist')
          }
        }
      } catch (error) {
        console.error('Error checking studio access:', error)
        setHasStudioAccess(false)
        // Redirect to waitlist on error (fail closed)
        if (pathname !== '/studio/waitlist') {
          router.push('/studio/waitlist')
        }
      }
    }

    checkStudioAccess()
  }, [isAuthenticated, pathname, router])

  // Show loading state while checking access
  if (authLoading || hasStudioAccess === null) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  // Show content if access is granted or not on a studio route
  if (hasStudioAccess || !pathname?.startsWith('/studio') || pathname === '/studio/waitlist') {
    return <>{children}</>
  }

  // This should rarely be reached due to redirects, but provide fallback
  return (
    <div className="min-h-screen flex items-center justify-center">
      <div className="text-center">
        <h2 className="text-xl font-semibold mb-2">Checking access...</h2>
        <div className="animate-spin h-6 w-6 border-b-2 border-blue-600 mx-auto"></div>
      </div>
    </div>
  )
}