'use client'

import React from 'react'
import { <PERSON>, Card<PERSON>ontent, CardH<PERSON>er, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { AlertTriangle, Info, Eye } from 'lucide-react'

interface NotAdvertisementCardProps {
  suitability: {
    is_advertising: boolean
    confidence: number
    reasoning: string
    content_type: string
    early_termination?: boolean
  }
}

export default function NotAdvertisementCard({ suitability }: NotAdvertisementCardProps) {
  if (!suitability || suitability.is_advertising === true) {
    return null
  }

  const getConfidenceColor = (confidence: number) => {
    if (confidence >= 90) return 'bg-red-100 text-red-800 border-red-200'
    if (confidence >= 75) return 'bg-orange-100 text-orange-800 border-orange-200'  
    if (confidence >= 60) return 'bg-yellow-100 text-yellow-800 border-yellow-200'
    return 'bg-gray-100 text-gray-800 border-gray-200'
  }

  const getContentTypeIcon = (contentType: string) => {
    if (contentType?.toLowerCase().includes('tutorial')) return '🎓'
    if (contentType?.toLowerCase().includes('music')) return '🎵'  
    if (contentType?.toLowerCase().includes('entertainment')) return '🎬'
    if (contentType?.toLowerCase().includes('news')) return '📰'
    if (contentType?.toLowerCase().includes('vlog')) return '📹'
    return '📄'
  }

  return (
    <Card className="w-full">
      <CardHeader className="pb-4">
        <CardTitle className="flex items-center gap-2 text-lg font-semibold text-orange-700">
          <AlertTriangle className="h-5 w-5" />
          Content Analysis Result
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Main Status */}
        <div className="bg-orange-50 p-4 rounded-lg border border-orange-200">
          <div className="flex items-center justify-between mb-2">
            <span className="text-sm font-medium text-orange-900">Classification</span>
            <Badge className="bg-orange-100 text-orange-800 border-orange-300">
              Not Advertising
            </Badge>
          </div>
          <p className="text-sm text-orange-800">
            This content does not appear to be commercial advertising or marketing material.
          </p>
        </div>

        {/* Confidence & Content Type */}
        <div className="grid grid-cols-1 gap-3">
          <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
            <div className="flex items-center gap-2">
              <Eye className="h-4 w-4 text-gray-600" />
              <span className="text-sm font-medium text-gray-700">AI Confidence</span>
            </div>
            <Badge className={getConfidenceColor(suitability.confidence)}>
              {suitability.confidence}%
            </Badge>
          </div>

          <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
            <div className="flex items-center gap-2">
              <span className="text-lg">{getContentTypeIcon(suitability.content_type)}</span>
              <span className="text-sm font-medium text-gray-700">Content Type</span>
            </div>
            <span className="text-sm text-gray-600 capitalize">
              {suitability.content_type.replace(/_/g, ' ')}
            </span>
          </div>
        </div>

        {/* AI Reasoning */}
        {suitability.reasoning && (
          <div className="bg-blue-50 p-4 rounded-lg border border-blue-200">
            <div className="flex items-start gap-2 mb-2">
              <Info className="h-4 w-4 text-blue-600 mt-0.5 flex-shrink-0" />
              <span className="text-sm font-medium text-blue-900">AI Analysis</span>
            </div>
            <p className="text-sm text-blue-800 leading-relaxed">
              {suitability.reasoning}
            </p>
          </div>
        )}

        {/* What This Means */}
        <div className="bg-gray-50 p-4 rounded-lg border border-gray-200">
          <h4 className="text-sm font-medium text-gray-900 mb-2">What This Means</h4>
          <ul className="text-sm text-gray-700 space-y-1">
            <li>• This content wasn&apos;t designed for commercial advertising purposes</li>
            <li>• Standard marketing analysis metrics don&apos;t apply</li>
            <li>• Content is kept private and won&apos;t appear in public listings</li>
          </ul>
        </div>

        {/* Alternative Suggestion */}
        <div className="bg-green-50 p-4 rounded-lg border border-green-200">
          <h4 className="text-sm font-medium text-green-900 mb-2">Looking for Analysis?</h4>
          <p className="text-sm text-green-800">
            AdBreakdown specializes in analyzing commercial advertisements, brand campaigns, 
            and marketing content. Try submitting a TV commercial, brand video, or sponsored content for detailed analysis.
          </p>
        </div>
      </CardContent>
    </Card>
  )
}