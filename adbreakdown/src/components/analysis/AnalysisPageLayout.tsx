'use client'

import React from 'react'
import { Badge } from '@/components/ui/badge'
import { AnalysisActions } from '@/components/analysis/AnalysisActions'

interface AnalysisPageLayoutProps {
  analysis: any
  videoMetadata: any
  parsedData: any
  isAuthenticated: boolean
  isFeaturedMode: boolean
  analysisActions: any
  children: React.ReactNode
  getBadges: () => Array<{ label: string; variant: any }>
}

export function AnalysisPageLayout({
  analysis,
  videoMetadata,
  parsedData,
  isAuthenticated,
  isFeaturedMode,
  analysisActions,
  children,
  getBadges
}: AnalysisPageLayoutProps) {
  return (
    <main className="container mx-auto px-3 md:px-4 py-3 md:py-4 max-w-6xl">
      {/* Header Section */}
      {analysis && (
        <div className="mb-6">
          <div className="flex items-start justify-between gap-4 mb-3">
            <div className="flex-1 min-w-0">
              <h1 className="text-lg md:text-xl font-medium text-gray-900">
                {`${videoMetadata.title} - Ad Analysis`}
              </h1>
            </div>
            <AnalysisActions
              analysis={analysis}
              isAuthenticated={isAuthenticated}
              isFeaturedMode={isFeaturedMode}
              parsedData={parsedData}
              actions={analysisActions}
            />
          </div>
          
          {/* Badges */}
          <div className="flex gap-2 overflow-x-auto scrollbar-hide pb-2">
            <div className="flex gap-2 min-w-max">
              {getBadges().map((badge, index) => (
                <Badge 
                  key={index} 
                  variant={badge.variant} 
                  className="bg-gray-50 text-gray-700 border-gray-200 whitespace-nowrap flex-shrink-0"
                >
                  {badge.label}
                </Badge>
              ))}
            </div>
          </div>
        </div>
      )}

      {/* Main Content */}
      {children}
    </main>
  )
}
