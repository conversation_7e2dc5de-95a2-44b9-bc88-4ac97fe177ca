import { Suspense } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import DeleteAnalysisModal from '@/components/analysis/DeleteAnalysisModal'
import ShareAnalysisModal from '@/components/analysis/ShareAnalysisModal'
import { useAuth } from '@/hooks/useAuth'
import { useCredits } from '@/hooks/useCredits'
import { isPaidPlan } from '@/lib/constants/usage'
import { Trash2, Share2, Globe } from 'lucide-react'

interface AnalysisActionsProps {
  analysis: any
  isAuthenticated: boolean
  isFeaturedMode: boolean
  parsedData?: any // For checking suitability
  actions: {
    isDeleting: boolean
    togglePublicLoading: boolean
    showDeleteConfirm: boolean
    showShareModal: boolean
    handleDeleteAnalysis: () => void
    togglePublicStatus: () => void
    handleShare: () => void
    setShowDeleteConfirm: (show: boolean) => void
    setShowShareModal: (show: boolean) => void
    shareToTwitter: () => void
    shareToLinkedIn: () => void
    shareToWhatsApp: () => void
    shareToInstagram: () => void
    copyToClipboard: () => void
  }
}

export function AnalysisActions({
  analysis,
  isAuthenticated,
  isFeaturedMode,
  parsedData,
  actions
}: AnalysisActionsProps) {
  const { isAdmin } = useAuth()
  const { profile } = useCredits()

  if (isFeaturedMode) {
    return null
  }

  // Check if user can delete (pro user or admin)
  const canDelete = isAdmin || (profile && isPaidPlan(profile.subscription_status))

  // Check if analysis can be published (must be advertising content)
  const canPublish = parsedData?.suitability?.is_advertising !== false

  return (
    <>
      {/* Action Buttons */}
      <div className="flex items-center gap-2 shrink-0">
        {/* Publish/Unpublish Toggle Button - Hidden on mobile */}
        {isAuthenticated && analysis && (
          <Button
            onClick={actions.togglePublicStatus}
            variant={analysis.is_public ? "outline" : "default"}
            size="sm"
            disabled={actions.togglePublicLoading || !canPublish}
            className="hidden md:flex items-center gap-1"
            title={!canPublish ? "Only advertising content can be published" : ""}
          >
            <Globe className="w-3 h-3" />
            {actions.togglePublicLoading
              ? 'Updating...'
              : analysis.is_public
                ? 'Unpublish'
                : 'Publish'
            }
          </Button>
        )}

        {/* Share Button */}
        <Button
          onClick={actions.handleShare}
          variant="outline"
          size="sm"
          className="flex items-center gap-1"
        >
          <Share2 className="w-3 h-3" />
          <span className="hidden sm:inline">Share</span>
        </Button>

        {/* Delete Button - Only for pro users/admin */}
        {isAuthenticated && analysis?.is_owner && (
          <Button
            onClick={() => actions.setShowDeleteConfirm(true)}
            variant="outline"
            size="sm"
            disabled={!canDelete}
            className={`flex items-center gap-1 ${
              canDelete
                ? "text-red-600 hover:text-red-700 hover:bg-red-50"
                : "text-gray-400 cursor-not-allowed"
            }`}
            title={!canDelete ? "Delete is available for Pro users only" : ""}
          >
            <Trash2 className="w-3 h-3" />
            <span className="hidden sm:inline">Delete</span>
          </Button>
        )}
      </div>

      {/* Modals */}
      <Suspense fallback={null}>
        <DeleteAnalysisModal 
          isOpen={actions.showDeleteConfirm}
          onClose={() => actions.setShowDeleteConfirm(false)}
          onConfirm={actions.handleDeleteAnalysis}
          isDeleting={actions.isDeleting}
        />

        <ShareAnalysisModal 
          isOpen={actions.showShareModal}
          onClose={() => actions.setShowShareModal(false)}
          analysis={analysis}
          shareHandlers={{
            shareToTwitter: actions.shareToTwitter,
            shareToLinkedIn: actions.shareToLinkedIn,
            shareToWhatsApp: actions.shareToWhatsApp,
            shareToInstagram: actions.shareToInstagram,
            copyToClipboard: actions.copyToClipboard
          }}
        />
      </Suspense>
    </>
  )
}
