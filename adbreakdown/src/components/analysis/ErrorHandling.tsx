import Link from 'next/link'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'

interface ErrorDetails {
  title: string
  description: string
  actionText: string
  actionLink: string
}

interface ErrorHandlingProps {
  analysis: any
  loading: boolean
  error: string
  analysisId: string
}

export function ErrorHandling({ analysis, loading, error, analysisId }: ErrorHandlingProps) {
  // Show loading state
  if (loading && !analysis) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <p>Loading analysis data...</p>
      </div>
    )
  }

  // If analysis is null after loading, show specific error message
  if (!analysis && !loading) {
    const getErrorDetails = (): ErrorDetails => {
      if (error.includes('not found for')) {
        return {
          title: 'Analysis Not Found',
          description: error,
          actionText: 'Back to Dashboard',
          actionLink: '/studio'
        }
      }
      if (error.includes('private') || error.includes('sign in')) {
        return {
          title: 'Sign In Required',
          description: error,
          actionText: 'Sign In',
          actionLink: '/sign-in'
        }
      }
      if (error.includes('Access denied') || error.includes('belongs to another user')) {
        return {
          title: 'Access Denied',
          description: error,
          actionText: 'Back to Library',
          actionLink: '/ad-library'
        }
      }
      return {
        title: 'Analysis Not Available',
        description: error || `Unable to load analysis: ${analysisId}`,
        actionText: 'Back to Dashboard',
        actionLink: '/studio'
      }
    }
    
    const errorDetails = getErrorDetails()
    
    return (
      <div className="min-h-screen bg-white">
        <div className="flex items-center justify-center min-h-screen">
          <Card className="mb-8 text-center max-w-md">
            <CardHeader>
              <CardTitle>{errorDetails.title}</CardTitle>
              <CardDescription className="text-left">{errorDetails.description}</CardDescription>
            </CardHeader>
            <CardContent>
              <Link href={errorDetails.actionLink}>
                <Button className="w-full">{errorDetails.actionText}</Button>
              </Link>
            </CardContent>
          </Card>
        </div>
      </div>
    )
  }

  return null
}
