'use client'

import React, { useState, useEffect, useCallback, lazy, Suspense } from 'react'
import { useAuth } from '@/hooks/useAuth'
import { useCredits } from '@/hooks/useCredits'
import { useAnalysisStream } from '@/hooks/useAnalysisStream'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import ProcessingStatusModal from '@/components/analysis/ProcessingStatusModal'
import ErrorDisplay from '@/components/analysis/ErrorDisplay'
import LoginOverlay from '@/components/ui/LoginOverlay'
import { ErrorHandling } from '@/components/analysis/ErrorHandling'
import { AnalysisPageLayout } from '@/components/analysis/AnalysisPageLayout'
import { AnalysisContent } from '@/components/analysis/AnalysisContent'
import { AnalysisSidebar } from '@/components/analysis/AnalysisSidebar'

// Lazy load chat components
const FloatingChatButton = lazy(() => import('@/components/chat/FloatingChatButton'))
const ChatOverlay = lazy(() => import('@/components/chat/ChatOverlay'))

// Interfaces
interface VideoMetadata {
  title: string
  thumbnail: string
  duration: string
  inferredBrandName: string
}

interface YouTubeMetadata {
  title: string
  description: string
  channelTitle: string
  publishedAt: string
  viewCount: string
  likeCount: string
  commentCount: string
  tags: string[]
  categoryId: string
  defaultLanguage?: string
  duration: string
  definition: string
}

interface SharedAnalysisPageProps {
  analysisId: string
  isFeaturedMode?: boolean
  preloadedAnalysis?: any
}

// Main Component
export default function CompactSharedAnalysisPage({ 
  analysisId, 
  isFeaturedMode = false, 
  preloadedAnalysis = null 
}: SharedAnalysisPageProps) {
  const { isAuthenticated, loading: authLoading } = useAuth()
  const { checkCredits, deductCredits } = useCredits()
  const { isStreaming, error: streamError, startStream } = useAnalysisStream()
  
  // Core state
  const [analysis, setAnalysis] = useState<any>(preloadedAnalysis)
  const [loading, setLoading] = useState(!preloadedAnalysis)
  const [error, setError] = useState('')
  const [youtubeVideoId, setYoutubeVideoId] = useState<string | null>(null)
  const [youtubeMetadata, setYoutubeMetadata] = useState<YouTubeMetadata | null>(null)
  const [parsedData, setParsedData] = useState<any>(null)
  const [enhancedScript, setEnhancedScript] = useState('')
  const [enhancedScriptLoading, setEnhancedScriptLoading] = useState(false)
  const [showLoginOverlay, setShowLoginOverlay] = useState(false)
  const [isChatOpen, setIsChatOpen] = useState(false)
  
  // Video metadata state
  const [videoMetadata, setVideoMetadata] = useState<VideoMetadata>({
    title: 'Loading analysis...',
    thumbnail: 'https://placehold.co/700x400/3B82F6/FFFFFF?text=Loading...',
    duration: '0:00',
    inferredBrandName: 'Loading...'
  })

  // Helper functions
  const extractYouTubeVideoId = (url: string): string | null => {
    if (!url) return null
    const regex = /(?:youtube\.com\/(?:[^\/]+\/.+\/|(?:v|e(?:mbed)?)\/|.*[?&]v=)|youtu\.be\/)([^"&?\/\s]{11})/
    const match = url.match(regex)
    return match ? match[1] : null
  }

  const parseMarketingAnalysis = useCallback((data: any) => {
    if (!data) return null
    try {
      return typeof data === 'object' ? data : JSON.parse(data)
    } catch (error) {
      console.error('Error parsing marketing analysis:', error)
      return null
    }
  }, [])

  const isAdvertisingContent = useCallback(() => {
    return parsedData?.suitability?.is_advertising !== false
  }, [parsedData])

  // Data fetching
  const fetchAnalysis = useCallback(async () => {
    if (!analysisId) return
    
    try {
      setLoading(true)
      const response = await fetch(`/api/analyses/${analysisId}`, {
        cache: 'no-store',
        headers: { 'Cache-Control': 'no-cache' }
      })
      
      if (response.ok) {
        const data = await response.json()
        setAnalysis(data)
        
        // Extract YouTube video ID
        if (data.video_url) {
          const videoId = extractYouTubeVideoId(data.video_url)
          setYoutubeVideoId(videoId)
        }
        
        // Set video metadata
        setVideoMetadata({
          title: data.video_title || data.title || 'No Title',
          thumbnail: data.video_thumbnail_url || data.thumbnail_url || 'https://placehold.co/700x400/3B82F6/FFFFFF?text=Video+Thumbnail',
          duration: data.video_duration || '0:00',
          inferredBrandName: data.inferred_brand || 'N/A'
        })

        // Parse marketing analysis
        const parsed = parseMarketingAnalysis(data.marketing_analysis)
        setParsedData(parsed)
        
        // Set enhanced script
        if (data.deciphered_script?.enhanced_analysis) {
          setEnhancedScript(data.deciphered_script.enhanced_analysis)
        }
      } else {
        const errorData = await response.json()
        setError(errorData.error || 'Failed to load analysis')
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An unknown error occurred.')
    } finally {
      setLoading(false)
    }
  }, [analysisId, parseMarketingAnalysis])

  // Enhanced script generation
  const generateEnhancedScript = async () => {
    if (!analysis?.id) return
    
    setEnhancedScriptLoading(true)
    setError('')
    
    try {
      const creditDeducted = await deductCredits(3)
      if (!creditDeducted) {
        setError('Failed to process credit. Please try again.')
        return
      }
      
      const response = await fetch(`/api/analyses/${analysisId}/generate-enhanced-script`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' }
      })
      
      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to generate production note')
      }
      
      const result = await response.json()
      setEnhancedScript(result.enhanced_analysis)
      fetchAnalysis()
      
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to generate production note')
    } finally {
      setEnhancedScriptLoading(false)
    }
  }

  // Badge generation
  const getBadges = () => {
    const badges = []
    if (analysis?.is_public) badges.push({ label: 'Public', variant: 'default' })
    if (parsedData?.suitability?.is_advertising === false) badges.push({ label: 'Not Advertisement', variant: 'secondary' })
    if (videoMetadata.inferredBrandName !== 'N/A') badges.push({ label: videoMetadata.inferredBrandName, variant: 'outline' })
    return badges
  }

  // Analysis actions (simplified)
  const analysisActions = {
    isDeleting: false,
    togglePublicLoading: false,
    showDeleteConfirm: false,
    showShareModal: false,
    handleDeleteAnalysis: () => {},
    togglePublicStatus: () => {},
    handleShare: () => {},
    setShowDeleteConfirm: () => {},
    setShowShareModal: () => {},
    shareToTwitter: () => {},
    shareToLinkedIn: () => {},
    shareToWhatsApp: () => {},
    shareToInstagram: () => {},
    copyToClipboard: () => {}
  }

  // Load analysis on mount
  useEffect(() => {
    if (!preloadedAnalysis) {
      fetchAnalysis()
    }
  }, [preloadedAnalysis, fetchAnalysis])

  // Error handling
  const errorHandlingResult = ErrorHandling({ analysis, loading, error, analysisId })
  if (errorHandlingResult) {
    return errorHandlingResult
  }

  return (
    <div className="min-h-screen bg-white">
      <ProcessingStatusModal isOpen={isStreaming} />
      <ErrorDisplay error={error} />

      {/* Processing State */}
      {analysis?.status === 'processing' && (
        <div className="flex items-center justify-center min-h-[400px]">
          <Card className="w-full max-w-lg text-center">
            <CardHeader>
              <CardTitle className="flex items-center justify-center gap-2">
                <div className="animate-spin h-5 w-5 border-2 border-blue-600 border-t-transparent rounded-full"></div>
                Analysis Ready to Process
              </CardTitle>
              <CardDescription>
                Your analysis is queued and ready. Click below to start the AI analysis.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Button
                onClick={() => startStream(analysisId, analysis.video_url)}
                disabled={isStreaming}
                className="w-full"
              >
                {isStreaming ? 'Processing...' : 'Start Analysis'}
              </Button>
              {streamError && <p className="text-red-600 text-sm mt-2">{streamError}</p>}
            </CardContent>
          </Card>
        </div>
      )}

      {/* Main Content */}
      {(analysis?.status === 'completed' || analysis?.status === 'generated' || analysis?.status === 'pending') && (
        <AnalysisPageLayout
          analysis={analysis}
          videoMetadata={videoMetadata}
          parsedData={parsedData}
          isAuthenticated={isAuthenticated}
          isFeaturedMode={isFeaturedMode}
          analysisActions={analysisActions}
          getBadges={getBadges}
        >
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {/* Main Content */}
            <div className="lg:col-span-2">
              <AnalysisContent
                analysis={analysis}
                youtubeVideoId={youtubeVideoId}
                videoMetadata={videoMetadata}
                parsedData={parsedData}
                isAdvertisingContent={isAdvertisingContent}
              />
            </div>

            {/* Sidebar */}
            <div className="lg:col-span-1">
              <AnalysisSidebar
                analysis={analysis}
                parsedData={parsedData}
                youtubeMetadata={youtubeMetadata}
                videoMetadata={videoMetadata}
                enhancedScript={enhancedScript}
                isAdvertisingContent={isAdvertisingContent}
                isAuthenticated={isAuthenticated}
                generateEnhancedScript={generateEnhancedScript}
                enhancedScriptLoading={enhancedScriptLoading}
              />
            </div>
          </div>
        </AnalysisPageLayout>
      )}

      {/* Chat Components */}
      {!isFeaturedMode && (
        <Suspense fallback={null}>
          <FloatingChatButton onClick={() => setIsChatOpen(true)} />
          <ChatOverlay
            isOpen={isChatOpen}
            onClose={() => setIsChatOpen(false)}
            analysisId={analysisId}
            analysisData={analysis}
          />
        </Suspense>
      )}

      {/* Login Overlay */}
      {!isAuthenticated && showLoginOverlay && (
        <LoginOverlay onClose={() => setShowLoginOverlay(false)} />
      )}
    </div>
  )
}
