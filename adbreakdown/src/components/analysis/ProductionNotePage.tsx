'use client'

import React, { useState, useEffect } from 'react'
import Link from 'next/link'
import { ArrowLeft, Calendar, Clock, Building, Video } from 'lucide-react'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { AnalysisMarkdown } from '@/components/ui/markdown-renderer'
import { useAuth } from '@/hooks/useAuth'
import LoginOverlay from '@/components/ui/LoginOverlay'

interface ProductionNotePageProps {
  analysisId: string
}

interface Analysis {
  id: string
  slug?: string
  title?: string
  video_title?: string
  inferred_brand?: string
  is_public?: boolean
  is_owner?: boolean
  deciphered_script?: {
    enhanced_analysis?: string
  }
  video_thumbnail_url?: string
  video_url?: string
  youtube_video_id?: string
  video_duration?: number
  duration_seconds?: number
  created_at?: string
  updated_at?: string
}

export default function ProductionNotePage({ analysisId }: ProductionNotePageProps) {
  const { isAuthenticated, loading: authLoading } = useAuth()
  const [analysis, setAnalysis] = useState<Analysis | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState('')
  const [showLoginOverlay, setShowLoginOverlay] = useState(false)

  // Fetch analysis data
  useEffect(() => {
    const fetchAnalysis = async () => {
      if (!analysisId) return
      
      setLoading(true)
      try {
        const timestamp = Date.now()
        const response = await fetch(`/api/analyses/${analysisId}?_t=${timestamp}`, {
          cache: 'no-store',
          headers: {
            'Cache-Control': 'no-cache, no-store, must-revalidate',
            'Pragma': 'no-cache',
            'Expires': '0'
          }
        })
        
        if (!response.ok) {
          let errorMessage = 'Failed to fetch analysis data.'
          try {
            const errData = await response.json()
            errorMessage = errData.error || errorMessage
          } catch (parseError) {
            errorMessage = `Server error (${response.status}): Unable to load analysis`
          }
          
          // If it's an authentication issue, show login overlay instead of error
          if (response.status === 401 || errorMessage.includes('sign in') || errorMessage.includes('private')) {
            setShowLoginOverlay(true)
            setLoading(false)
            return
          }
          
          throw new Error(errorMessage)
        }
        
        const data = await response.json()
        setAnalysis(data)
        
        // Check if production note exists
        if (!data.deciphered_script?.enhanced_analysis) {
          setError('Production note not available for this analysis.')
        }
      } catch (err) {
        setError(err instanceof Error ? err.message : 'An unknown error occurred.')
      } finally {
        setLoading(false)
      }
    }

    if (!authLoading) {
      fetchAnalysis()
    }
  }, [analysisId, authLoading])

  // Loading state
  if (authLoading || loading) {
    return (
      <div className="container mx-auto px-4 py-6 max-w-4xl">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 rounded w-1/3 mb-4"></div>
          <div className="h-4 bg-gray-200 rounded w-2/3 mb-6"></div>
          <Card className="mb-6">
            <CardContent className="p-6">
              <div className="flex flex-col md:flex-row gap-6">
                <div className="w-48 h-32 bg-gray-200 rounded-lg"></div>
                <div className="flex-grow space-y-3">
                  <div className="h-6 bg-gray-200 rounded w-3/4"></div>
                  <div className="h-4 bg-gray-200 rounded w-1/2"></div>
                  <div className="h-4 bg-gray-200 rounded w-1/3"></div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    )
  }

  // Error state
  if (error && !analysis) {
    return (
      <div className="container mx-auto px-4 py-6 max-w-4xl">
        <div className="text-center py-12">
          <Video className="w-16 h-16 mx-auto mb-4 text-gray-400" />
          <h2 className="text-xl font-semibold text-gray-900 mb-2">Production Note Not Found</h2>
          <p className="text-gray-600 mb-6">{error}</p>
          <Link href="/ad-library">
            <Button>
              Browse Ad Library
            </Button>
          </Link>
        </div>
      </div>
    )
  }

  // No analysis found
  if (!analysis) {
    return (
      <div className="container mx-auto px-4 py-6 max-w-4xl">
        <div className="text-center py-12">
          <Video className="w-16 h-16 mx-auto mb-4 text-gray-400" />
          <h2 className="text-xl font-semibold text-gray-900 mb-2">Analysis Not Found</h2>
          <p className="text-gray-600 mb-6">The requested analysis could not be found.</p>
          <Link href="/ad-library">
            <Button>
              Browse Ad Library
            </Button>
          </Link>
        </div>
      </div>
    )
  }
  const formatDuration = (seconds: number | null) => {
    if (seconds === null || seconds === undefined) return '0:00'
    const mins = Math.floor(seconds / 60)
    const secs = Math.round(seconds % 60)
    return `${mins}:${secs.toString().padStart(2, '0')}`
  }

  const formatDate = (dateString?: string) => {
    if (!dateString) return 'Unknown'
    try {
      return new Date(dateString).toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      })
    } catch (error) {
      return 'Unknown'
    }
  }

  const getYouTubeThumbnail = (videoId: string, quality: 'hqdefault' | 'maxresdefault' = 'hqdefault'): string => {
    return `https://img.youtube.com/vi/${videoId}/${quality}.jpg`
  }

  const extractYouTubeVideoId = (url: string): string | null => {
    const regex = /(?:youtube\.com\/(?:[^\/]+\/.+\/|(?:v|e(?:mbed)?)\/|.*[?&]v=)|youtu\.be\/)([^"&?\/\s]{11})/
    const match = url.match(regex)
    return match ? match[1] : null
  }

  // Get the best available thumbnail
  const getThumbnailUrl = () => {
    if (analysis.youtube_video_id) {
      return getYouTubeThumbnail(analysis.youtube_video_id, 'hqdefault')
    }
    if (analysis.video_url) {
      const videoId = extractYouTubeVideoId(analysis.video_url)
      if (videoId) {
        return getYouTubeThumbnail(videoId, 'hqdefault')
      }
    }
    return analysis.video_thumbnail_url || '/placeholder-video.png'
  }

  const adTitle = analysis.video_title || analysis.title || `${analysis.inferred_brand || 'Brand'} Ad`
  const duration = formatDuration(analysis.video_duration ?? analysis.duration_seconds ?? null)
  
  return (
    <div className="container mx-auto px-4 py-6 max-w-4xl">
      {/* Header */}
      <div className="mb-6">
        <div className="flex items-center gap-4 mb-4">
          <Link href={`/ad/${analysis.slug || analysis.id}`}>
            <Button variant="ghost" size="sm" className="flex items-center gap-2">
              <ArrowLeft className="w-4 h-4" />
              Back to Analysis
            </Button>
          </Link>
        </div>
        
        <h1 className="text-2xl md:text-3xl font-bold text-gray-900 mb-2">
          Production Note
        </h1>
        <p className="text-gray-600">
          Detailed production analysis for &ldquo;{adTitle}&rdquo;
        </p>
      </div>

      {/* Video Info Card */}
      <Card className="mb-6">
        <CardContent className="p-6">
          <div className="flex flex-col md:flex-row gap-6">
            {/* Video Thumbnail */}
            <div className="flex-shrink-0">
              <img
                src={getThumbnailUrl()}
                alt={adTitle}
                className="w-full md:w-48 h-32 object-cover rounded-lg bg-gray-100"
                onError={(e) => {
                  const target = e.target as HTMLImageElement
                  target.src = '/placeholder-video.png'
                }}
              />
            </div>
            
            {/* Video Details */}
            <div className="flex-grow">
              <h2 className="text-xl font-semibold text-gray-900 mb-3">
                {adTitle}
              </h2>
              
              <div className="flex flex-wrap gap-3 text-sm text-gray-600">
                {analysis.inferred_brand && (
                  <div className="flex items-center gap-1">
                    <Building className="w-4 h-4" />
                    <span>{analysis.inferred_brand}</span>
                  </div>
                )}
                
                <div className="flex items-center gap-1">
                  <Clock className="w-4 h-4" />
                  <span>{duration}</span>
                </div>
                
                <div className="flex items-center gap-1">
                  <Calendar className="w-4 h-4" />
                  <span>Analyzed {formatDate(analysis.created_at)}</span>
                </div>
                
                {analysis.video_url && (
                  <div className="flex items-center gap-1">
                    <Video className="w-4 h-4" />
                    <Link
                      href={analysis.video_url}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-blue-600 hover:text-blue-800 underline"
                    >
                      Watch Original
                    </Link>
                  </div>
                )}
              </div>
              
              <div className="flex items-center gap-2 mt-3">
                <Badge variant={analysis.is_public ? "default" : "secondary"}>
                  {analysis.is_public ? "Public" : "Private"}
                </Badge>
                {analysis.is_owner && (
                  <Badge variant="outline">
                    Your Analysis
                  </Badge>
                )}
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Production Note Content */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Video className="w-5 h-5" />
            Production Analysis
          </CardTitle>
        </CardHeader>
        <CardContent>
          {analysis.deciphered_script?.enhanced_analysis ? (
            <div className="prose prose-sm max-w-none text-justify">
              <AnalysisMarkdown content={analysis.deciphered_script.enhanced_analysis} />
            </div>
          ) : (
            <div className="text-center py-8 text-gray-500">
              <Video className="w-12 h-12 mx-auto mb-3 opacity-50" />
              <p>Production note not available for this analysis.</p>
              <p className="text-sm mt-1">
                <Link href={`/ad/${analysis.slug || analysis.id}`} className="text-blue-600 hover:text-blue-800 underline">
                  Return to main analysis
                </Link>
              </p>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Footer */}
      <div className="mt-8 text-center">
        <Link href={`/ad/${analysis.slug || analysis.id}`}>
          <Button variant="outline">
            <ArrowLeft className="w-4 h-4 mr-2" />
            Back to Analysis
          </Button>
        </Link>
      </div>

      {/* Login Overlay for unauthenticated users viewing private analyses */}
      {showLoginOverlay && !isAuthenticated && (
        <LoginOverlay 
          isVisible={showLoginOverlay}
          title="Sign In Required"
          description="This production note is private. Sign in to access detailed production analysis and insights."
        />
      )}
    </div>
  )
}