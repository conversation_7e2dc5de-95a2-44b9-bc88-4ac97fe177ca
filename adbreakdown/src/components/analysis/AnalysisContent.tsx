'use client'

import React from 'react'
import { <PERSON>, Card<PERSON>ontent, Card<PERSON>eader, Card<PERSON>itle } from '@/components/ui/card'
import { BarChart3, Tag, Target, Users } from 'lucide-react'
import { VideoSection } from '@/components/analysis/VideoSection'
import ExecutiveBriefingCard from '@/components/analysis/ExecutiveBriefingCard'
import BottomLineCard from '@/components/analysis/BottomLineCard'
import CoreAnalysisCard from '@/components/analysis/CoreAnalysisCard'
import Diagnosis<PERSON><PERSON> from '@/components/analysis/DiagnosisCard'
import CitationsCard from '@/components/analysis/CitationsCard'
import TabsComponent from '@/components/analysis/TabsComponent'

interface AnalysisContentProps {
  analysis: any
  youtubeVideoId: string | null
  videoMetadata: any
  parsedData: any
  isAdvertisingContent: () => boolean
}

export function AnalysisContent({
  analysis,
  youtubeVideoId,
  videoMetadata,
  parsedData,
  isAdvertisingContent
}: AnalysisContentProps) {
  return (
    <div className="space-y-6">
      {/* Video Section */}
      <VideoSection
        analysis={analysis}
        youtubeVideoId={youtubeVideoId}
        videoMetadata={videoMetadata}
      />

      {/* Executive Briefing - Only for advertising content */}
      {isAdvertisingContent() && (
        <ExecutiveBriefingCard parsedData={parsedData} />
      )}

      {/* Bottom Line - Only for advertising content */}
      {isAdvertisingContent() && (
        <BottomLineCard theOneThingThatMatters={parsedData?.executive_briefing?.the_one_thing_that_matters} />
      )}

      {/* Core Analysis - Only for advertising content */}
      {isAdvertisingContent() && (
        <CoreAnalysisCard parsedData={parsedData} />
      )}

      {/* Strategic Elements - Only for advertising content */}
      {isAdvertisingContent() && (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {/* Target Audience */}
          {parsedData?.strategic_deep_dive?.brand_and_market_context?.target_audience_evidence && (
            <Card className="shadow-sm border border-gray-200 bg-white">
              <CardHeader className="pb-3">
                <CardTitle className="text-sm font-medium text-gray-900 flex items-center">
                  <Users className="w-4 h-4 mr-2 text-gray-500" />
                  Target Audience
                </CardTitle>
              </CardHeader>
              <CardContent className="pt-0">
                <p className="text-gray-700 text-sm leading-relaxed">
                  {parsedData.strategic_deep_dive.brand_and_market_context.target_audience_evidence}
                </p>
              </CardContent>
            </Card>
          )}

          {/* Competitive Landscape */}
          {parsedData?.strategic_deep_dive?.brand_and_market_context?.competitive_landscape && (
            <Card className="shadow-sm border border-gray-200 bg-white">
              <CardHeader className="pb-3">
                <CardTitle className="text-sm font-medium text-gray-900 flex items-center">
                  <Target className="w-4 h-4 mr-2 text-gray-500" />
                  Competitive Landscape
                </CardTitle>
              </CardHeader>
              <CardContent className="pt-0">
                <p className="text-gray-700 text-sm leading-relaxed">
                  {parsedData.strategic_deep_dive.brand_and_market_context.competitive_landscape}
                </p>
              </CardContent>
            </Card>
          )}
        </div>
      )}

      {/* Keywords Section - Only for advertising content */}
      {isAdvertisingContent() && parsedData?.metadata?.theme_keywords && Array.isArray(parsedData.metadata.theme_keywords) && parsedData.metadata.theme_keywords.length > 0 && (
        <Card className="shadow-sm border border-gray-200 bg-white">
          <CardHeader className="pb-3">
            <CardTitle className="text-sm font-medium text-gray-900 flex items-center">
              <Tag className="w-4 h-4 mr-2 text-gray-500" />
              Theme Keywords
            </CardTitle>
          </CardHeader>
          <CardContent className="pt-0">
            <div className="flex flex-wrap gap-2">
              {parsedData.metadata.theme_keywords.map((keyword: string, index: number) => (
                <span
                  key={index}
                  className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-50 text-blue-700 border border-blue-200"
                >
                  {keyword}
                </span>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Diagnosis Section */}
      <DiagnosisCard parsedData={parsedData} />

      {/* Additional Notes Section */}
      <div className="bg-white rounded-lg p-4 md:p-6 border border-gray-100">
        <h2 className="text-base md:text-lg font-medium text-gray-900 mb-3 md:mb-4 flex items-center">
          <BarChart3 className="w-4 h-4 md:w-5 md:h-5 mr-2 text-gray-600" />
          Additional Notes
        </h2>
        <TabsComponent parsedData={parsedData} />
      </div>

      {/* Citations Section */}
      <CitationsCard citations={parsedData?.citations} />
    </div>
  )
}
