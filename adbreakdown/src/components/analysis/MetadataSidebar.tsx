'use client'

import React from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Info, InfoIcon, Search } from 'lucide-react'
// import { useMetadataValidation } from '@/hooks/useMetadataValidation'

interface MetadataSidebarProps {
  parsedData: any
  youtubeMetadata?: any
  videoMetadata?: any
  validationData?: {
    validated_metadata?: {
      [key: string]: {
        value: string
        confidence: number
        changes_made?: string
        source_verification?: string
      }
    }
  }
  analysisId?: string
  currentMetadata?: any
  isOwner?: boolean
}

export default function MetadataSidebar({ parsedData, youtubeMetadata, videoMetadata, validationData, analysisId, currentMetadata, isOwner }: MetadataSidebarProps) {
  const metadata = parsedData?.metadata
  
  // All validation logic has been removed

  const formatLabel = (key: string) => {
    if (key === 'ad_title') return 'Title'
    return key
      .split('_')
      .map(word => word.charAt(0).toUpperCase() + word.slice(1))
      .join(' ')
  }

  const getConfidenceColor = (confidence: number): string => {
    if (confidence >= 7) return 'text-green-700 bg-green-50 border-green-200'
    if (confidence >= 4) return 'text-yellow-700 bg-yellow-50 border-yellow-200'
    return 'text-red-700 bg-red-50 border-red-200'
  }

  const getConfidenceIcon = (confidence: number): string => {
    if (confidence >= 7) return '●'
    if (confidence >= 4) return '◐'
    return '○'
  }

  const getOverallConfidence = (): { avgConfidence: number, hasValidation: boolean } => {
    if (!validationData?.validated_metadata) {
      return { avgConfidence: 0, hasValidation: false }
    }
    
    const confidenceScores = Object.values(validationData.validated_metadata)
      .map(field => field.confidence)
      .filter(score => typeof score === 'number')
    
    if (confidenceScores.length === 0) {
      return { avgConfidence: 0, hasValidation: false }
    }
    
    const avgConfidence = confidenceScores.reduce((sum, score) => sum + score, 0) / confidenceScores.length
    return { avgConfidence, hasValidation: true }
  }


  const getLineItemTextColor = (confidence: number): string => {
    if (confidence >= 7) {
      return 'text-green-700'
    }
    if (confidence >= 4) {
      return 'text-yellow-700'
    }
    return 'text-red-700'
  }

  const renderMetadata = (data: any) => {
    return Object.entries(data)
      .filter(([key, value]) => {
        // Filter out ad_script and theme_keywords
        if (key === 'ad_script' || key === 'theme_keywords') return false
        // Filter out null/empty values
        return value && value !== 'None' && value !== 'N/A' && value !== ''
      })
      .map(([key, value]) => {
        const validatedField = validationData?.validated_metadata?.[key]
        const confidence = validatedField?.confidence
        const hasValidation = confidence !== undefined && validatedField
        const displayValue = hasValidation ? validatedField.value : String(value)
        
        const textColorClass = hasValidation 
          ? getLineItemTextColor(confidence)
          : 'text-gray-900'
        
        return (
          <div key={key} className="relative group">
            <div className="grid grid-cols-[auto_1fr_auto] gap-x-2 text-xs items-center">
              <span className="font-medium text-gray-600">{formatLabel(key)}:</span>
              <span className={`${textColorClass} text-xs`}>{displayValue}</span>
              {hasValidation && (
                <div className="relative">
                  <InfoIcon className="w-4 h-4 text-gray-400 hover:text-gray-600 cursor-help" />
                  {/* Tooltip */}
                  <div className="absolute right-0 bottom-full mb-2 hidden group-hover:block bg-gray-900 text-white text-xs rounded py-2 px-3 min-w-[200px] z-10">
                    <div className="mb-1">
                      <strong>Confidence:</strong> {confidence}/10
                    </div>
                    {validatedField?.changes_made && validatedField.changes_made !== 'No changes made' && (
                      <div className="mb-1">
                        <strong>Changes:</strong> {validatedField.changes_made}
                      </div>
                    )}
                    {validatedField?.source_verification && (
                      <div>
                        <strong>Sources:</strong> {validatedField.source_verification}
                      </div>
                    )}
                    {/* Arrow */}
                    <div className="absolute top-full right-4 w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-gray-900"></div>
                  </div>
                </div>
              )}
            </div>
          </div>
        )
      });
  };

  if (!metadata) {
    // Fallback to existing metadata structure if available
    const fallbackData = {
      brand: videoMetadata?.inferredBrandName,
      duration: videoMetadata?.duration,
      channel: youtubeMetadata?.channelTitle,
    };

    return (
      <Card className="shadow-sm border border-gray-200 bg-white">
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between w-full">
            <CardTitle className="text-sm md:text-base font-medium text-gray-900 flex items-center">
              <Info className="w-4 h-4 mr-2 text-gray-500" />
              Credits
            </CardTitle>
          </div>
        </CardHeader>
        <CardContent className="pt-0">
          <div className="space-y-2">
            {renderMetadata(fallbackData)}
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="shadow-sm border border-gray-200 bg-white">
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between w-full">
          <CardTitle className="text-sm font-medium text-gray-900 flex items-center">
            <Info className="w-4 h-4 mr-2 text-gray-500" />
            Credits
          </CardTitle>
        </div>
      </CardHeader>
      <CardContent className="pt-0">
        <div className="space-y-2">
          {renderMetadata(metadata)}
        </div>
      </CardContent>
    </Card>
  );
}