import VideoPlayer from '@/components/analysis/VideoPlayer'
import { VideoMetadata } from '@/hooks/useAnalysisData'

interface VideoSectionProps {
  analysis: any
  youtubeVideoId: string | null
  videoMetadata: VideoMetadata
  className?: string
}

export function VideoSection({
  analysis,
  youtubeVideoId,
  videoMetadata,
  className = ""
}: VideoSectionProps) {
  if (!analysis || (!analysis.video_url && !youtubeVideoId)) {
    return null
  }

  return (
    <div className={`w-full aspect-video ${className}`}>
      <VideoPlayer
        youtubeVideoId={youtubeVideoId}
        videoMetadata={videoMetadata}
        isLoading={false}
      />
    </div>
  )
}
