'use client'

import React from 'react'
import Link from 'next/link'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Tag, Lightbulb } from 'lucide-react'
import ScorecardSidebar from '@/components/analysis/ScorecardSidebar'
import NotAdvertisementCard from '@/components/analysis/NotAdvertisementCard'
import MetadataSidebar from '@/components/analysis/MetadataSidebar'

interface AnalysisSidebarProps {
  analysis: any
  parsedData: any
  youtubeMetadata: any
  videoMetadata: any
  enhancedScript: string
  isAdvertisingContent: () => boolean
  isAuthenticated: boolean
  generateEnhancedScript: () => void
  enhancedScriptLoading: boolean
}

export function AnalysisSidebar({
  analysis,
  parsedData,
  youtubeMetadata,
  videoMetadata,
  enhancedScript,
  isAdvertisingContent,
  isAuthenticated,
  generateEnhancedScript,
  enhancedScriptLoading
}: AnalysisSidebarProps) {
  return (
    <div className="space-y-4 lg:sticky lg:top-6">
      {/* Scorecard or Not Advertisement Card */}
      {isAdvertisingContent() ? (
        <ScorecardSidebar parsedData={parsedData} />
      ) : (
        <NotAdvertisementCard suitability={parsedData?.suitability} />
      )}

      {/* Metadata Sidebar */}
      <MetadataSidebar
        parsedData={parsedData}
        youtubeMetadata={youtubeMetadata}
        videoMetadata={videoMetadata}
      />

      {/* Keywords Section */}
      {parsedData?.metadata?.theme_keywords && Array.isArray(parsedData.metadata.theme_keywords) && parsedData.metadata.theme_keywords.length > 0 && (
        <Card className="shadow-sm border border-gray-200 bg-white">
          <CardHeader className="pb-1">
            <CardTitle className="text-sm font-medium text-gray-900 flex items-center">
              <Tag className="w-4 h-4 mr-2 text-gray-500" />
              Theme Keywords
            </CardTitle>
          </CardHeader>
          <CardContent className="pt-2">
            <div className="flex flex-wrap gap-1.5">
              {parsedData.metadata.theme_keywords.slice(0, 8).map((keyword: string, index: number) => (
                <span
                  key={index}
                  className="inline-flex items-center px-2 py-1 rounded-md text-xs font-medium bg-blue-50 text-blue-700 border border-blue-200"
                >
                  {keyword}
                </span>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Curator Tools - Only for authenticated users and owners */}
      {isAuthenticated && analysis?.is_owner && (
        <Card className="shadow-sm border border-gray-200 bg-white">
          <CardHeader className="pb-2">
            <h3 className="text-sm font-medium text-gray-900 flex items-center">
              <Lightbulb className="w-4 h-4 mr-2 text-gray-500" />
              Curator Tools
            </h3>
            <p className="text-gray-600 text-sm mb-4">
              As the curator of this ad, use your credits to generate detailed production notes and additional marketing assets.
            </p>
          </CardHeader>
          <CardContent className="pt-0">
            <div className="space-y-3">
              {analysis?.deciphered_script?.enhanced_analysis || enhancedScript ? (
                <Link href={`/ad/${analysis.id}/production-note`} className="w-full">
                  <Button className="w-full text-sm" size="sm" variant="outline">
                    View Production Note
                  </Button>
                </Link>
              ) : (
                <Button
                  onClick={generateEnhancedScript}
                  disabled={enhancedScriptLoading}
                  className="w-full text-sm"
                  size="sm"
                  variant="outline"
                >
                  {enhancedScriptLoading ? 'Generating...' : 'Generate Production Note (3 credits)'}
                </Button>
              )}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  )
}
