'use client'

import React from 'react'
import { Card, CardContent } from '@/components/ui/card'

interface BottomLineCardProps {
  theOneThingThatMatters: string | null | undefined
}

export default function BottomLineCard({ theOneThingThatMatters }: BottomLineCardProps) {
  if (!theOneThingThatMatters) {
    return null
  }

  return (
    <Card className="shadow-sm border border-gray-200 bg-white">
      <CardContent className="p-6">
        <h3 className="text-base md:text-lg font-medium text-gray-900 mb-3">Quick Summary</h3>
          <p className="text-sm text-gray-700 leading-relaxed">
            {theOneThingThatMatters}
          </p>
      </CardContent>
    </Card>
  )
}
