'use client'

import React, { useState, useEffect } from 'react'
// Removed tabs-related imports since we no longer use them
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import {
  TrendingUp, Users, Target, Lightbulb
} from 'lucide-react'

interface TabsComponentProps {
  parsedData: any
}

export default function TabsComponent({ parsedData }: TabsComponentProps) {
  const [isOpen, setIsOpen] = useState(false)

  useEffect(() => {
    // Set expanded by default on desktop (screens >= 768px)
    const checkScreenSize = () => {
      setIsOpen(window.innerWidth >= 768)
    }
    
    checkScreenSize()
    window.addEventListener('resize', checkScreenSize)
    
    return () => window.removeEventListener('resize', checkScreenSize)
  }, [])

  // Remove unused Tabs imports to prevent JSX errors

  if (!parsedData) {
    return (
      <div className="text-center py-8">
        <p className="text-gray-600">Analysis data not available</p>
      </div>
    )
  }

  return (
    <div className="w-full">
      <button
        className="w-full flex justify-between items-center px-4 py-2 bg-blue-50 text-slate-700 rounded-md"
        onClick={() => setIsOpen(!isOpen)}
      >
        <span className="font-medium">Additional Notes</span>
        <span>{isOpen ? '▼' : '▶'}</span>
      </button>
      
      {isOpen && (
        <div className="mt-4 grid grid-cols-1 md:grid-cols-2 gap-4">
          {/* Hypothetical Brief */}
          {parsedData.strategic_deep_dive?.brand_strategy?.hypothetical_brief && (
            <Card className="border-gray-200">
              <CardHeader>
                <CardTitle className="flex items-center text-gray-900 text-sm md:text-base font-medium">
                  <Target className="w-4 h-4 mr-2" />
                  Hypothetical Brief
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-gray-700 leading-relaxed">
                  {parsedData.strategic_deep_dive.brand_strategy.hypothetical_brief}
                </p>
              </CardContent>
            </Card>
          )}
          {/* Performance Predictions */}
          {parsedData.internal_signals?.prediction_factors && (
            <Card className="border-gray-200">
              <CardHeader>
                <CardTitle className="flex items-center text-gray-900 text-sm md:text-base font-medium">
                  <TrendingUp className="w-4 h-4 mr-2" />
                  Performance Predictions
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-gray-700 leading-relaxed">
                  {parsedData.internal_signals.prediction_factors}
                </p>
              </CardContent>
            </Card>
          )}
        </div>
      )}
    </div>
  )
}