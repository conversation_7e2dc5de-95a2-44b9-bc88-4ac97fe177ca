'use client'

import React, { useState } from 'react'
import { Card, CardContent } from '@/components/ui/card'
import { MessageSquare } from 'lucide-react'
import { AnalysisMarkdown } from '@/components/ui/markdown-renderer'

interface CoreAnalysisCardProps {
  parsedData: any
}

export default function CoreAnalysisCard({ parsedData }: CoreAnalysisCardProps) {
  const [showFull, setShowFull] = useState(false)
  const isMobile = typeof window !== 'undefined' && window.innerWidth < 768

  if (!parsedData?.core_analysis?.essay) {
    return null
  }

  const content = parsedData.core_analysis.essay
  const shouldClamp = (isMobile || true) && content.length > 300

  return (
    <Card className="shadow-sm border border-gray-200 bg-white">
      <CardContent className="p-6">
        <h3 className="text-base md:text-lg font-medium text-gray-900 mb-4 flex items-center">
          <MessageSquare className="w-5 h-5 mr-2 text-gray-500" />
          Executive Summary
        </h3>
        {shouldClamp ? (
          <div className="text-sm text-gray-700 leading-relaxed text-left">
            {showFull ? (
              <div>
                <AnalysisMarkdown content={content} />
                <button
                  onClick={() => setShowFull(false)}
                  className="text-blue-600 hover:text-blue-800 font-medium ml-1"
                >
                  Read Less
                </button>
              </div>
            ) : (
              <div className="relative">
                <div className="line-clamp-5">
                  <AnalysisMarkdown content={content} />
                </div>
                <div className="absolute bottom-0 right-0 bg-white pl-2">
                  <button
                    onClick={() => setShowFull(true)}
                    className="text-blue-600 hover:text-blue-800 font-medium"
                  >
                    ...Read More
                  </button>
                </div>
              </div>
            )}
          </div>
        ) : (
          <AnalysisMarkdown content={content} />
        )}
      </CardContent>
    </Card>
  )
}