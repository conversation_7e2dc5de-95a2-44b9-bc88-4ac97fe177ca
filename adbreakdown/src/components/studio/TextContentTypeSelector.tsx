'use client';

import { ContentType, TextIdea } from '@/types/studio';
import { Tv, Instagram, Zap, Target, Search, Facebook } from 'lucide-react';

interface TextContentTypeSelectorProps {
  ideas: TextIdea[];
  onSelect: (contentType: ContentType) => void;
}

const TextContentTypeSelector: React.FC<TextContentTypeSelectorProps> = ({ ideas, onSelect }) => {
  const contentTypes = [
    {
      id: 'tv-ad-30s' as ContentType,
      name: '30s TV Commercial',
      description: 'Traditional television advertisement script',
      icon: Tv,
      duration: '30 seconds',
      platform: 'Television',
      color: 'from-blue-500 to-blue-600'
    },
    {
      id: 'reel-30s' as ContentType,
      name: '30s Social Reel',
      description: 'Instagram/TikTok reel script for social media',
      icon: Instagram,
      duration: '30 seconds',
      platform: 'Social Media',
      color: 'from-pink-500 to-purple-600'
    },
    {
      id: 'bumper-5s' as ContentType,
      name: '5s Bumper Ad',
      description: 'Short, punchy bumper advertisement',
      icon: Zap,
      duration: '5 seconds',
      platform: 'YouTube/Digital',
      color: 'from-yellow-500 to-orange-600'
    },
    {
      id: 'performance-ad' as ContentType,
      name: 'Performance Ad Copy',
      description: 'Conversion-focused ad copy for campaigns',
      icon: Target,
      duration: 'Variable',
      platform: 'Digital Platforms',
      color: 'from-green-500 to-emerald-600'
    },
    {
      id: 'google-ad' as ContentType,
      name: 'Google Ads Copy',
      description: 'Search and display ad copy for Google',
      icon: Search,
      duration: 'Headlines & Descriptions',
      platform: 'Google Ads',
      color: 'from-red-500 to-red-600'
    },
    {
      id: 'meta-ad' as ContentType,
      name: 'Meta Ads Copy',
      description: 'Facebook and Instagram ad copy',
      icon: Facebook,
      duration: 'Primary Text & Headlines',
      platform: 'Meta Platforms',
      color: 'from-blue-600 to-indigo-600'
    }
  ];

  return (
    <div className="bg-white border border-gray-200 rounded-lg p-6 max-w-4xl">
      <div className="mb-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-2">Choose Content Type</h3>
        <p className="text-sm text-gray-600">Select the type of content you&apos;d like to generate for your campaign</p>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {contentTypes.map((type) => {
          const IconComponent = type.icon;
          return (
            <button
              key={type.id}
              onClick={() => onSelect(type.id)}
              className="group relative p-6 rounded-xl border-2 border-gray-200 hover:border-gray-300 transition-all duration-200 text-left hover:shadow-md bg-white hover:bg-gray-50"
            >
              {/* Background Gradient */}
              <div className={`absolute inset-0 bg-gradient-to-br ${type.color} opacity-0 group-hover:opacity-5 rounded-xl transition-opacity duration-200`} />
              
              {/* Content */}
              <div className="relative">
                <div className="flex items-center gap-3 mb-3">
                  <div className={`p-2 rounded-lg bg-gradient-to-br ${type.color} text-white`}>
                    <IconComponent size={20} />
                  </div>
                  <div className="flex-1">
                    <h4 className="font-semibold text-gray-900 text-sm">{type.name}</h4>
                    <p className="text-xs text-gray-500">{type.platform}</p>
                  </div>
                </div>
                
                <p className="text-xs text-gray-600 mb-3 leading-relaxed">
                  {type.description}
                </p>
                
                <div className="flex items-center justify-between">
                  <span className="text-xs font-medium text-gray-500">
                    Duration: {type.duration}
                  </span>
                  <div className="opacity-0 group-hover:opacity-100 transition-opacity">
                    <span className="text-xs font-medium text-blue-600">Select →</span>
                  </div>
                </div>
              </div>
            </button>
          );
        })}
      </div>

      {/* Additional Info */}
      <div className="mt-6 p-4 bg-gray-50 rounded-lg">
        <div className="flex items-start gap-2">
          <div className="w-2 h-2 bg-blue-500 rounded-full mt-2 flex-shrink-0"></div>
          <div>
            <h4 className="text-sm font-medium text-gray-900 mb-1">Content Optimization</h4>
            <p className="text-xs text-gray-600 leading-relaxed">
              Each content type is optimized for its specific platform and audience behavior. 
              The AI will adapt the tone, structure, and messaging to maximize engagement and conversions.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default TextContentTypeSelector;
