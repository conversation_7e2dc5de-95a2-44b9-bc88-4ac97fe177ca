'use client';

import { AudioType, AudioIdea } from '@/types/studio';
import { Radio, Music, Volume2, Mic, Podcast, Zap } from 'lucide-react';

interface AudioTypeSelectorProps {
  ideas: AudioIdea[];
  onSelect: (audioType: AudioType) => void;
}

const AudioTypeSelector: React.FC<AudioTypeSelectorProps> = ({ ideas, onSelect }) => {
  const audioTypes = [
    {
      id: 'radio-ad' as AudioType,
      name: 'Radio Advertisement',
      description: 'Professional radio commercial with voiceover',
      icon: Radio,
      duration: '15-60 seconds',
      technology: 'Chirp AI',
      color: 'from-red-500 to-red-600',
      features: ['Professional voiceover', 'Background music', 'Sound effects', 'Call-to-action']
    },
    {
      id: 'jingle' as AudioType,
      name: 'Brand Jingle',
      description: 'Catchy musical jingle for brand recognition',
      icon: Music,
      duration: '5-30 seconds',
      technology: 'Lyra + Chirp',
      color: 'from-purple-500 to-purple-600',
      features: ['Memorable melody', 'Brand name integration', 'Catchy lyrics', 'Multiple variations']
    },
    {
      id: 'background-music' as AudioType,
      name: 'Background Music',
      description: 'Ambient music for videos and presentations',
      icon: Volume2,
      duration: '30s - 5 minutes',
      technology: 'Lyra AI',
      color: 'from-blue-500 to-blue-600',
      features: ['Mood-based composition', 'Seamless loops', 'Multiple genres', 'Royalty-free']
    },
    {
      id: 'voiceover' as AudioType,
      name: 'Professional Voiceover',
      description: 'High-quality narration for any content',
      icon: Mic,
      duration: '10s - 5 minutes',
      technology: 'Chirp AI',
      color: 'from-green-500 to-green-600',
      features: ['Multiple voice options', 'Natural intonation', 'Script optimization', 'Emotion control']
    },
    {
      id: 'podcast-intro' as AudioType,
      name: 'Podcast Intro',
      description: 'Branded introduction for podcast episodes',
      icon: Podcast,
      duration: '10-30 seconds',
      technology: 'Chirp + Lyra',
      color: 'from-orange-500 to-orange-600',
      features: ['Show branding', 'Host introduction', 'Theme music', 'Professional quality']
    },
    {
      id: 'sound-effect' as AudioType,
      name: 'Custom Sound Effects',
      description: 'Branded audio effects and transitions',
      icon: Zap,
      duration: '1-10 seconds',
      technology: 'Lyra AI',
      color: 'from-yellow-500 to-yellow-600',
      features: ['Brand-specific sounds', 'Transition effects', 'Logo sounds', 'Audio branding']
    }
  ];

  return (
    <div className="bg-white border border-gray-200 rounded-lg p-6 max-w-6xl">
      <div className="mb-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-2">Choose Audio Type</h3>
        <p className="text-sm text-gray-600">Select the type of audio content you&apos;d like to generate for your brand</p>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {audioTypes.map((type) => {
          const IconComponent = type.icon;
          return (
            <button
              key={type.id}
              onClick={() => onSelect(type.id)}
              className="group relative p-6 rounded-xl border-2 border-gray-200 hover:border-gray-300 transition-all duration-200 text-left hover:shadow-lg bg-white hover:bg-gray-50"
            >
              {/* Background Gradient */}
              <div className={`absolute inset-0 bg-gradient-to-br ${type.color} opacity-0 group-hover:opacity-5 rounded-xl transition-opacity duration-200`} />
              
              {/* Content */}
              <div className="relative">
                <div className="flex items-center gap-3 mb-4">
                  <div className={`p-3 rounded-lg bg-gradient-to-br ${type.color} text-white`}>
                    <IconComponent size={24} />
                  </div>
                  <div className="flex-1">
                    <h4 className="font-semibold text-gray-900 text-sm">{type.name}</h4>
                    <p className="text-xs text-gray-500">{type.technology}</p>
                  </div>
                </div>
                
                <p className="text-xs text-gray-600 mb-4 leading-relaxed">
                  {type.description}
                </p>
                
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <span className="text-xs font-medium text-gray-500">Duration:</span>
                    <span className="text-xs text-gray-700">{type.duration}</span>
                  </div>
                  
                  <div>
                    <span className="text-xs font-medium text-gray-500 block mb-2">Features:</span>
                    <div className="space-y-1">
                      {type.features.map((feature, index) => (
                        <div key={index} className="flex items-center gap-2">
                          <div className="w-1 h-1 bg-gray-400 rounded-full flex-shrink-0"></div>
                          <span className="text-xs text-gray-600">{feature}</span>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
                
                <div className="mt-4 opacity-0 group-hover:opacity-100 transition-opacity">
                  <span className="text-xs font-medium text-orange-600">Select →</span>
                </div>
              </div>
            </button>
          );
        })}
      </div>

      {/* Technology Info */}
      <div className="mt-8 grid md:grid-cols-2 gap-4">
        <div className="p-4 bg-blue-50 rounded-lg">
          <div className="flex items-center gap-2 mb-2">
            <Mic className="w-4 h-4 text-blue-600" />
            <h4 className="text-sm font-medium text-blue-900">Chirp AI Technology</h4>
          </div>
          <p className="text-xs text-blue-700 leading-relaxed">
            Advanced voice synthesis for natural-sounding speech, radio ads, and voiceovers with emotion control and multiple voice options.
          </p>
        </div>
        
        <div className="p-4 bg-purple-50 rounded-lg">
          <div className="flex items-center gap-2 mb-2">
            <Music className="w-4 h-4 text-purple-600" />
            <h4 className="text-sm font-medium text-purple-900">Lyra AI Technology</h4>
          </div>
          <p className="text-xs text-purple-700 leading-relaxed">
            AI music generation for background tracks, jingles, and sound effects with mood-based composition and genre flexibility.
          </p>
        </div>
      </div>
    </div>
  );
};

export default AudioTypeSelector;
