'use client';

import { useState, useEffect, useRef, useCallback } from 'react';
import { ChatMessage, AudioGenerationState, AudioIdea, AudioType, VoiceType, AudioMood, AudioDuration } from '@/types/studio';
import UnifiedInputForm, { UnifiedInputData } from './UnifiedInputForm';
import AudioTypeSelector from './AudioTypeSelector';
import AudioCustomizationForm from './AudioCustomizationForm';
import AudioFinalReview from './AudioFinalReview';
import AudioGenerationAnimation from './AudioGenerationAnimation';
import { Bot, User } from 'lucide-react';

interface AudioChatInterfaceProps {
  state: AudioGenerationState;
  setState: React.Dispatch<React.SetStateAction<AudioGenerationState>>;
}

const AudioChatInterface: React.FC<AudioChatInterfaceProps> = ({ state, setState }) => {
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  
  const selectedIdeaRef = useRef<AudioIdea | null>(null);
  const ideasRef = useRef<AudioIdea[]>([]);
  const audioTypeRef = useRef<AudioType | null>(null);
  const voiceRef = useRef<VoiceType | null>(null);
  const moodRef = useRef<AudioMood | null>(null);
  const durationRef = useRef<AudioDuration | null>(null);
  const brandContextRef = useRef<string>('');
  const animationMessageRef = useRef<string | null>(null);
  const initialMessageAddedRef = useRef<boolean>(false);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const addBotMessage = useCallback((text: string, component?: React.ReactNode) => {
    const message: ChatMessage = {
      id: `bot-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      text,
      sender: 'bot',
      component,
      timestamp: new Date(),
    };
    setMessages(prev => [...prev, message]);
    return message.id;
  }, []);

  const updateBotMessage = useCallback((messageId: string, text: string, component?: React.ReactNode) => {
    setMessages(prev => prev.map(msg => 
      msg.id === messageId ? { ...msg, text, component } : msg
    ));
  }, []);

  const addUserMessage = useCallback((text: string) => {
    const message: ChatMessage = {
      id: `user-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      text,
      sender: 'user',
      timestamp: new Date(),
    };
    setMessages(prev => [...prev, message]);
  }, []);

  // Stable handler for generation
  const handleAudioGeneration = useCallback(async () => {
    setState(prev => ({ ...prev, step: 'audio-generation', status: 'pending' }));
    addUserMessage("Generate my audio!");
    
    // Show the animation immediately
    const animationMessageId = addBotMessage(
      "🎵 Creating your professional audio content now...",
      <AudioGenerationAnimation 
        status="processing"
        progress={0}
        message="Starting audio generation"
      />
    );
    animationMessageRef.current = animationMessageId;
    
    try {
      const response = await fetch('/api/v1/audio/generate', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          audioType: audioTypeRef.current,
          voice: voiceRef.current,
          mood: moodRef.current,
          duration: durationRef.current,
          brandInfo: brandContextRef.current || state.url || 'Brand information'
        }),
      });
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(`Failed to generate audio: ${errorData.error || response.statusText}`);
      }
      
      const result = await response.json();
      setState(prev => ({ ...prev, generatedAudio: result.audioUrl, status: 'completed', step: 'completed' }));
      
      // Hide animation and show results
      if (animationMessageRef.current) {
        updateBotMessage(
          animationMessageRef.current,
          "🎉 Your audio content is ready! Here's your generated audio:",
          <div className="space-y-4">
            <div className="bg-white border border-gray-200 rounded-lg p-6">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-semibold text-gray-900">Generated Audio</h3>
                <div className="flex gap-2">
                  <button
                    onClick={() => {
                      const link = document.createElement('a');
                      link.href = result.audioUrl;
                      link.download = `${audioTypeRef.current}-audio.mp3`;
                      document.body.appendChild(link);
                      link.click();
                      document.body.removeChild(link);
                    }}
                    className="bg-orange-600 text-white px-3 py-1 rounded-md text-sm font-medium hover:bg-orange-700 transition-colors"
                  >
                    Download
                  </button>
                </div>
              </div>
              <div className="space-y-4">
                <audio controls className="w-full">
                  <source src={result.audioUrl} type="audio/mpeg" />
                  Your browser does not support the audio element.
                </audio>
                <div className="text-sm text-gray-600">
                  <p><strong>Type:</strong> {audioTypeRef.current?.replace('-', ' ').toUpperCase()}</p>
                  <p><strong>Voice:</strong> {voiceRef.current?.replace('-', ' ')}</p>
                  <p><strong>Mood:</strong> {moodRef.current}</p>
                  <p><strong>Duration:</strong> {durationRef.current}</p>
                </div>
              </div>
            </div>
          </div>
        );
        animationMessageRef.current = null;
      }
      
    } catch (error) {
      console.error('Audio generation error:', error);
      setState(prev => ({ ...prev, status: 'failed' }));
      
      if (animationMessageRef.current) {
        updateBotMessage(
          animationMessageRef.current,
          "Sorry, I encountered an error while generating your audio. Please try again.",
          null
        );
        animationMessageRef.current = null;
      }
    }
 }, [setState, addUserMessage, updateBotMessage, addBotMessage, state.url]);

  // Stable handler for customization submit
  const handleCustomizationSubmit = useCallback((voice: VoiceType, mood: AudioMood, duration: AudioDuration) => {
    voiceRef.current = voice;
    moodRef.current = mood;
    durationRef.current = duration;
    setState(prev => ({ ...prev, voice, mood, duration, step: 'final-review' }));
    addUserMessage(`Voice: ${voice}, Mood: ${mood}, Duration: ${duration}`);
    
    addBotMessage(
      "Perfect! Here's a summary of your audio requirements. Ready to generate?",
      <AudioFinalReview
        audioType={audioTypeRef.current!}
        voice={voice}
        mood={mood}
        duration={duration}
        onGenerate={() => handleAudioGeneration()}
      />
    );
  }, [setState, addUserMessage, addBotMessage, handleAudioGeneration]);

  // Stable handler for audio type selection
  const handleAudioTypeSelect = useCallback((audioType: AudioType) => {
    audioTypeRef.current = audioType;
    setState(prev => ({ ...prev, audioType, step: 'audio-customization' }));
    addUserMessage(`Selected audio type: ${audioType.replace('-', ' ').toUpperCase()}`);
    
    addBotMessage(
      "Great choice! Now let's customize your audio with voice, mood, and duration preferences:",
      <AudioCustomizationForm
        audioType={audioType}
        onSubmit={(voice, mood, duration) => handleCustomizationSubmit(voice, mood, duration)}
      />
    );
  }, [setState, addUserMessage, addBotMessage, handleCustomizationSubmit]);

  const handleFormSubmit = useCallback(async (data: UnifiedInputData) => {
    setState(prev => ({ ...prev, url: data.url || '', step: 'generating-ideas' }));

    // Store the full brand context for later use
    brandContextRef.current = JSON.stringify(data);

    const submissionText = data.url
      ? `Website: ${data.url}${data.brandName ? `, Brand: ${data.brandName}` : ''}${data.category ? `, Category: ${data.category}` : ''}`
      : `Brand: ${data.brandName}, Category: ${data.category}, Target: ${data.targetAudience}`;

    addUserMessage(submissionText);
    addBotMessage("Perfect! Let me analyze your information and generate some audio content ideas...");
    
    try {
      const response = await fetch('/api/v1/audio/ideas', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(data),
      });
      
      if (!response.ok) {
        throw new Error(`Failed to generate ideas: ${response.statusText}`);
      }
      
      const ideas = await response.json();
      ideasRef.current = ideas;
      setState(prev => ({ ...prev, ideas, step: 'idea-selection' }));
      
      addBotMessage(
        "I've generated some audio content ideas for your brand! Please select the type of audio you'd like to create:",
        <AudioTypeSelector
          ideas={ideas}
          onSelect={(audioType) => handleAudioTypeSelect(audioType)}
        />
      );
    } catch (error) {
      console.error('Ideas generation error:', error);
      addBotMessage("Sorry, I encountered an error while generating ideas. Please try again.");
    }
  }, [setState, addUserMessage, addBotMessage, handleAudioTypeSelect]);

  // Initialize with welcome message
  useEffect(() => {
    if (!initialMessageAddedRef.current) {
      addBotMessage(
        "🎵 Welcome to the AI Audio Generator! I'll help you create professional radio ads, catchy jingles, and background music for your brand. Let's start by gathering some information about your business.",
        <UnifiedInputForm onSubmit={handleFormSubmit} contentType="audio" />
      );
      initialMessageAddedRef.current = true;
    }
  }, [addBotMessage, handleFormSubmit]);

  return (
    <div className="flex flex-col h-[600px]">
      <div className="flex-1 overflow-y-auto p-4 space-y-4">
        {messages.map((message) => (
          <div
            key={message.id}
            className={`flex ${message.sender === 'user' ? 'justify-end' : 'justify-start'}`}
          >
            <div
              className={`flex items-start gap-3 max-w-[80%] ${
                message.sender === 'user' ? 'flex-row-reverse' : 'flex-row'
              }`}
            >
              <div
                className={`w-8 h-8 rounded-full flex items-center justify-center ${
                  message.sender === 'user'
                    ? 'bg-orange-600 text-white'
                    : 'bg-gray-200 text-gray-600'
                }`}
              >
                {message.sender === 'user' ? <User size={16} /> : <Bot size={16} />}
              </div>
              <div
                className={`rounded-lg p-3 ${
                  message.sender === 'user'
                    ? 'bg-orange-600 text-white'
                    : 'bg-gray-100 text-gray-900'
                }`}
              >
                <p className="text-sm whitespace-pre-wrap">{message.text}</p>
                {message.component && (
                  <div className="mt-3">
                    {message.component}
                  </div>
                )}
              </div>
            </div>
          </div>
        ))}
        <div ref={messagesEndRef} />
      </div>
    </div>
  );
};

export default AudioChatInterface;
