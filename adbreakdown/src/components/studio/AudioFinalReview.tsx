'use client';

import { AudioType, VoiceType, AudioMood, AudioDuration } from '@/types/studio';
import { 
  Music, 
  Radio, 
  Volume2, 
  Mic, 
  Podcast, 
  Zap, 
  Play,
  Eye,
  Clock,
  User,
  Heart
} from 'lucide-react';

interface AudioFinalReviewProps {
  audioType: AudioType;
  voice: VoiceType;
  mood: AudioMood;
  duration: AudioDuration;
  onGenerate: () => void;
}

const AudioFinalReview: React.FC<AudioFinalReviewProps> = ({
  audioType,
  voice,
  mood,
  duration,
  onGenerate
}) => {
  const getAudioTypeDisplay = (type: AudioType) => {
    switch (type) {
      case 'radio-ad':
        return { name: 'Radio Advertisement', icon: '📻', description: 'Professional radio commercial with voiceover' };
      case 'jingle':
        return { name: 'Brand Jingle', icon: '🎵', description: 'Catchy musical jingle for brand recognition' };
      case 'background-music':
        return { name: 'Background Music', icon: '🎶', description: 'Ambient music for videos and presentations' };
      case 'voiceover':
        return { name: 'Professional Voiceover', icon: '🎤', description: 'High-quality narration for any content' };
      case 'podcast-intro':
        return { name: 'Podcast Intro', icon: '🎙️', description: 'Branded introduction for podcast episodes' };
      case 'sound-effect':
        return { name: 'Custom Sound Effects', icon: '⚡', description: 'Branded audio effects and transitions' };
      default:
        return { name: type, icon: '🔊', description: 'Custom audio content' };
    }
  };

  const getVoiceDisplay = (voiceType: VoiceType) => {
    switch (voiceType) {
      case 'male-professional':
        return { name: 'Male Professional', icon: User };
      case 'female-professional':
        return { name: 'Female Professional', icon: User };
      case 'male-casual':
        return { name: 'Male Casual', icon: User };
      case 'female-casual':
        return { name: 'Female Casual', icon: User };
      case 'male-energetic':
        return { name: 'Male Energetic', icon: Zap };
      case 'female-energetic':
        return { name: 'Female Energetic', icon: Zap };
      case 'narrator':
        return { name: 'Narrator', icon: Mic };
      case 'child':
        return { name: 'Child Voice', icon: Heart };
      case 'elderly':
        return { name: 'Elderly Voice', icon: User };
      default:
        return { name: voiceType, icon: User };
    }
  };

  const audioTypeDisplay = getAudioTypeDisplay(audioType);
  const voiceDisplay = getVoiceDisplay(voice);
  const VoiceIcon = voiceDisplay.icon;

  const getTechnologyUsed = () => {
    switch (audioType) {
      case 'radio-ad':
      case 'voiceover':
      case 'podcast-intro':
        return 'Chirp AI + Lyra AI';
      case 'jingle':
        return 'Lyra AI + Chirp AI';
      case 'background-music':
      case 'sound-effect':
        return 'Lyra AI';
      default:
        return 'AI Audio Generation';
    }
  };

  const getExpectedFeatures = () => {
    switch (audioType) {
      case 'radio-ad':
        return ['Professional voiceover', 'Background music', 'Sound effects', 'Clear call-to-action'];
      case 'jingle':
        return ['Memorable melody', 'Brand name integration', 'Catchy rhythm', 'Multiple variations'];
      case 'background-music':
        return ['Mood-based composition', 'Seamless loops', 'No vocals', 'Royalty-free'];
      case 'voiceover':
        return ['Natural speech patterns', 'Clear pronunciation', 'Emotion control', 'Professional quality'];
      case 'podcast-intro':
        return ['Show branding', 'Theme music', 'Professional intro', 'Consistent format'];
      case 'sound-effect':
        return ['Brand-specific sounds', 'High-quality audio', 'Multiple formats', 'Instant recognition'];
      default:
        return ['High-quality audio', 'Brand-aligned content', 'Professional production'];
    }
  };

  return (
    <div className="bg-white border border-gray-200 rounded-lg p-6 max-w-4xl">
      <div className="flex items-center gap-2 mb-6">
        <Eye className="w-5 h-5 text-orange-600" />
        <h3 className="text-lg font-semibold text-gray-900">Final Review</h3>
      </div>

      <div className="space-y-6">
        {/* Audio Type Summary */}
        <div className="bg-orange-50 border border-orange-200 rounded-lg p-4">
          <div className="flex items-center gap-2 mb-3">
            <Music className="w-4 h-4 text-orange-600" />
            <h4 className="font-medium text-orange-900">Audio Type</h4>
          </div>
          <div className="flex items-center gap-3">
            <span className="text-2xl">{audioTypeDisplay.icon}</span>
            <div>
              <p className="text-sm font-medium text-orange-800">{audioTypeDisplay.name}</p>
              <p className="text-xs text-orange-600">{audioTypeDisplay.description}</p>
            </div>
          </div>
        </div>

        {/* Configuration Grid */}
        <div className="grid md:grid-cols-3 gap-4">
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <div className="flex items-center gap-2 mb-2">
              <VoiceIcon className="w-4 h-4 text-blue-600" />
              <h4 className="font-medium text-blue-900">Voice</h4>
            </div>
            <p className="text-sm text-blue-800">{voiceDisplay.name}</p>
          </div>

          <div className="bg-purple-50 border border-purple-200 rounded-lg p-4">
            <div className="flex items-center gap-2 mb-2">
              <Heart className="w-4 h-4 text-purple-600" />
              <h4 className="font-medium text-purple-900">Mood</h4>
            </div>
            <p className="text-sm text-purple-800 capitalize">{mood}</p>
          </div>

          <div className="bg-green-50 border border-green-200 rounded-lg p-4">
            <div className="flex items-center gap-2 mb-2">
              <Clock className="w-4 h-4 text-green-600" />
              <h4 className="font-medium text-green-900">Duration</h4>
            </div>
            <p className="text-sm text-green-800">{duration}</p>
          </div>
        </div>

        {/* Technology & Features */}
        <div className="grid md:grid-cols-2 gap-4">
          <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
            <div className="flex items-center gap-2 mb-2">
              <Zap className="w-4 h-4 text-gray-600" />
              <h4 className="font-medium text-gray-900">Technology</h4>
            </div>
            <p className="text-sm text-gray-800">{getTechnologyUsed()}</p>
          </div>

          <div className="bg-indigo-50 border border-indigo-200 rounded-lg p-4">
            <div className="flex items-center gap-2 mb-2">
              <Volume2 className="w-4 h-4 text-indigo-600" />
              <h4 className="font-medium text-indigo-900">Audio Quality</h4>
            </div>
            <p className="text-sm text-indigo-800">Professional, High-fidelity</p>
          </div>
        </div>

        {/* Expected Features */}
        <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
          <div className="flex items-center gap-2 mb-2">
            <Music className="w-4 h-4 text-yellow-600" />
            <h4 className="font-medium text-yellow-900">What You&apos;ll Get</h4>
          </div>
          <ul className="text-sm text-yellow-800 space-y-1">
            {getExpectedFeatures().map((feature, index) => (
              <li key={index}>• {feature}</li>
            ))}
            <li>• Downloadable MP3 format</li>
            <li>• Commercial usage rights</li>
          </ul>
        </div>

        {/* Generate Button */}
        <div className="flex justify-center pt-4">
          <button
            onClick={onGenerate}
            className="px-8 py-4 bg-gradient-to-r from-orange-600 to-amber-600 text-white rounded-lg hover:from-orange-700 hover:to-amber-700 transition-all font-medium text-lg flex items-center gap-3 shadow-lg hover:shadow-xl transform hover:scale-105"
          >
            <Play size={20} />
            Generate My Audio
          </button>
        </div>

        {/* Disclaimer */}
        <div className="text-center">
          <p className="text-xs text-gray-500">
            Audio generation typically takes 30-90 seconds depending on duration and complexity.
          </p>
        </div>
      </div>
    </div>
  );
};

export default AudioFinalReview;
