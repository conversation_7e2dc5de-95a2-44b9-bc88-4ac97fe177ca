'use client';

import { useState, useEffect, useRef, useCallback } from 'react';
import { ChatMessage, TextGenerationState, TextIdea, ContentType, Platform, ToneOfVoice, ContentLength } from '@/types/studio';
import UnifiedInputForm, { UnifiedInputData } from './UnifiedInputForm';
import TextContentTypeSelector from './TextContentTypeSelector';
import TextCustomizationForm from './TextCustomizationForm';
import TextFinalReview from './TextFinalReview';
import TextGenerationAnimation from './TextGenerationAnimation';
import { Bot, User } from 'lucide-react';

interface TextChatInterfaceProps {
  state: TextGenerationState;
  setState: React.Dispatch<React.SetStateAction<TextGenerationState>>;
}

const TextChatInterface: React.FC<TextChatInterfaceProps> = ({ state, setState }) => {
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  
  const selectedIdeaRef = useRef<TextIdea | null>(null);
  const ideasRef = useRef<TextIdea[]>([]);
  const contentTypeRef = useRef<ContentType | null>(null);
  const platformRef = useRef<Platform | null>(null);
  const toneRef = useRef<ToneOfVoice | null>(null);
  const lengthRef = useRef<ContentLength | null>(null);
  const brandContextRef = useRef<string>('');
  const animationMessageRef = useRef<string | null>(null);
  const initialMessageAddedRef = useRef<boolean>(false);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const addBotMessage = useCallback((text: string, component?: React.ReactNode) => {
    const message: ChatMessage = {
      id: `bot-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      text,
      sender: 'bot',
      component,
      timestamp: new Date(),
    };
    setMessages(prev => [...prev, message]);
    return message.id;
  }, []);

  const updateBotMessage = useCallback((messageId: string, text: string, component?: React.ReactNode) => {
    setMessages(prev => prev.map(msg => 
      msg.id === messageId ? { ...msg, text, component } : msg
    ));
  }, []);

  const addUserMessage = useCallback((text: string) => {
    const message: ChatMessage = {
      id: `user-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      text,
      sender: 'user',
      timestamp: new Date(),
    };
    setMessages(prev => [...prev, message]);
  }, []);

  // Initialize with welcome message
  useEffect(() => {
    if (!initialMessageAddedRef.current) {
      addBotMessage(
        "✍️ Welcome to the AI Text Generator! I'll help you create compelling ad copy and scripts for your campaigns. Let's start by gathering some information about your business.",
        <UnifiedInputForm onSubmit={handleFormSubmit} contentType="text" />
      );
      initialMessageAddedRef.current = true;
    }
  }, [addBotMessage]);
  
  const handleTextGeneration = useCallback(async () => {
    setState(prev => ({ ...prev, step: 'text-generation', status: 'pending' }));
    addUserMessage("Generate my content!");
    
    // Show the animation immediately
    const animationMessageId = addBotMessage(
      "✍️ Creating your compelling ad copy now...",
      <TextGenerationAnimation
        status="processing"
        progress={0}
        message="Starting content generation"
      />
    );
    animationMessageRef.current = animationMessageId;
    
    try {
      const response = await fetch('/api/v1/text/generate', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          contentType: contentTypeRef.current,
          platform: platformRef.current,
          tone: toneRef.current,
          length: lengthRef.current,
          brandInfo: brandContextRef.current || state.url || 'Brand information'
        }),
      });
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(`Failed to generate content: ${errorData.error || response.statusText}`);
      }
      
      const result = await response.json();
      setState(prev => ({ ...prev, generatedContent: result.content, status: 'completed', step: 'completed' }));
      
      // Hide animation and show results
      if (animationMessageRef.current) {
        updateBotMessage(
          animationMessageRef.current,
          "🎉 Your ad copy is ready! Here's your generated content:",
          <div className="space-y-4">
            <div className="bg-white border border-gray-200 rounded-lg p-6">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-semibold text-gray-900">Generated Content</h3>
                <div className="flex gap-2">
                  <button
                    onClick={() => navigator.clipboard.writeText(result.content)}
                    className="bg-green-600 text-white px-3 py-1 rounded-md text-sm font-medium hover:bg-green-700 transition-colors"
                  >
                    Copy
                  </button>
                  <button
                    onClick={() => {
                      const blob = new Blob([result.content], { type: 'text/plain' });
                      const url = URL.createObjectURL(blob);
                      const link = document.createElement('a');
                      link.href = url;
                      link.download = `${contentTypeRef.current}-content.txt`;
                      document.body.appendChild(link);
                      link.click();
                      document.body.removeChild(link);
                      URL.revokeObjectURL(url);
                    }}
                    className="bg-blue-600 text-white px-3 py-1 rounded-md text-sm font-medium hover:bg-blue-700 transition-colors"
                  >
                    Download
                  </button>
                </div>
              </div>
              <div className="prose prose-sm max-w-none">
                <pre className="whitespace-pre-wrap text-gray-800 font-sans leading-relaxed">
                  {result.content}
                </pre>
              </div>
            </div>
          </div>
        );
        animationMessageRef.current = null;
      }
      
    } catch (error) {
      console.error('Text generation error:', error);
      setState(prev => ({ ...prev, status: 'failed' }));
      
      if (animationMessageRef.current) {
        updateBotMessage(
          animationMessageRef.current,
          "Sorry, I encountered an error while generating your content. Please try again.",
          null
        );
        animationMessageRef.current = null;
      }
    }
  }, [setState, addUserMessage, updateBotMessage, addBotMessage, state.url]);

  const handleCustomizationSubmit = useCallback((platform: Platform, tone: ToneOfVoice, length: ContentLength) => {
    platformRef.current = platform;
    toneRef.current = tone;
    lengthRef.current = length;
    setState(prev => ({ ...prev, platform, tone, length, step: 'final-review' }));
    addUserMessage(`Platform: ${platform}, Tone: ${tone}, Length: ${length}`);
    
    addBotMessage(
      "Perfect! Here's a summary of your content requirements. Ready to generate?",
      <TextFinalReview
        contentType={contentTypeRef.current!}
        platform={platform}
        tone={tone}
        length={length}
        onGenerate={() => handleTextGeneration()}
      />
    );
  }, [setState, addUserMessage, addBotMessage, handleTextGeneration]);

  const handleContentTypeSelect = useCallback((contentType: ContentType) => {
    contentTypeRef.current = contentType;
    setState(prev => ({ ...prev, contentType, step: 'content-customization' }));
    addUserMessage(`Selected content type: ${contentType.replace('-', ' ').toUpperCase()}`);
    
    addBotMessage(
      "Great choice! Now let's customize your content with platform, tone, and length preferences:",
      <TextCustomizationForm
        contentType={contentType}
        onSubmit={(platform, tone, length) => handleCustomizationSubmit(platform, tone, length)}
      />
    );
  }, [setState, addUserMessage, addBotMessage, handleCustomizationSubmit]);

  const handleFormSubmit = useCallback(async (data: UnifiedInputData) => {
    setState(prev => ({ ...prev, url: data.url || '', step: 'generating-ideas' }));

    // Store the full brand context for later use
    const fullBrandContext = data.url
      ? `Website: ${data.url}${data.brandName ? `, Brand: ${data.brandName}` : ''}${data.category ? `, Category: ${data.category}` : ''}`
      : `Brand: ${data.brandName}, Category: ${data.category}, Target: ${data.targetAudience}, USP: ${data.usp}`;

    brandContextRef.current = JSON.stringify(data); // Store the full data object

    const submissionText = data.url
      ? `Website: ${data.url}${data.brandName ? `, Brand: ${data.brandName}` : ''}${data.category ? `, Category: ${data.category}` : ''}`
      : `Brand: ${data.brandName}, Category: ${data.category}, Target: ${data.targetAudience}`;

    addUserMessage(submissionText);
    addBotMessage("Perfect! Let me analyze your information and generate some content ideas...");

    try {
      const response = await fetch('/api/v1/text/ideas', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        throw new Error(`Failed to generate ideas: ${response.statusText}`);
      }

      const ideas = await response.json();
      ideasRef.current = ideas;
      setState(prev => ({ ...prev, ideas, step: 'idea-selection' }));

      addBotMessage(
        "I've generated some content ideas for your campaign! Please select the type of content you'd like to create:",
        <TextContentTypeSelector
          ideas={ideas}
          onSelect={(contentType) => handleContentTypeSelect(contentType)}
        />
      );
    } catch (error) {
      console.error('Ideas generation error:', error);
      addBotMessage("Sorry, I encountered an error while generating ideas. Please try again.");
    }
  }, [setState, addUserMessage, addBotMessage, handleContentTypeSelect]);

  return (
    <div className="flex flex-col h-[600px]">
      <div className="flex-1 overflow-y-auto p-4 space-y-4">
        {messages.map((message) => (
          <div
            key={message.id}
            className={`flex ${message.sender === 'user' ? 'justify-end' : 'justify-start'}`}
          >
            <div
              className={`flex items-start gap-3 max-w-[80%] ${
                message.sender === 'user' ? 'flex-row-reverse' : 'flex-row'
              }`}
            >
              <div
                className={`w-8 h-8 rounded-full flex items-center justify-center ${
                  message.sender === 'user'
                    ? 'bg-green-600 text-white'
                    : 'bg-gray-200 text-gray-600'
                }`}
              >
                {message.sender === 'user' ? <User size={16} /> : <Bot size={16} />}
              </div>
              <div
                className={`rounded-lg p-3 ${
                  message.sender === 'user'
                    ? 'bg-green-600 text-white'
                    : 'bg-gray-100 text-gray-900'
                }`}
              >
                <p className="text-sm whitespace-pre-wrap">{message.text}</p>
                {message.component && (
                  <div className="mt-3">
                    {message.component}
                  </div>
                )}
              </div>
            </div>
          </div>
        ))}
        <div ref={messagesEndRef} />
      </div>
    </div>
  );
};

export default TextChatInterface;
