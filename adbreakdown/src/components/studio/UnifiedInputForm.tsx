'use client';

import { useState } from 'react';
import { Building2, Users, Target, Globe, Lightbulb, MessageSquare, Link } from 'lucide-react';

interface UnifiedInputFormProps {
  onSubmit: (data: UnifiedInputData) => void;
  contentType?: 'video' | 'image' | 'audio' | 'text';
}

export interface UnifiedInputData {
  url?: string;
  brandName?: string;
  category?: string;
  subCategory?: string;
  targetAudience?: string;
  market?: string;
  usp?: string;
  videoDescription?: string;
}

const UnifiedInputForm: React.FC<UnifiedInputFormProps> = ({ onSubmit, contentType = 'video' }) => {
  const [formData, setFormData] = useState<UnifiedInputData>({
    url: '',
    brandName: '',
    category: '',
    subCategory: '',
    targetAudience: '',
    market: '',
    usp: '',
    videoDescription: ''
  });
  const [isLoading, setIsLoading] = useState(false);

  const categories = [
    'Fashion & Apparel',
    'Technology & Electronics',
    'Health & Fitness',
    'Food & Beverage',
    'Home & Garden',
    'Beauty & Personal Care',
    'Automotive',
    'Finance & Banking',
    'Education',
    'Entertainment',
    'Travel & Tourism',
    'Sports & Recreation',
    'Other'
  ];

  const markets = [
    'United States',
    'United Kingdom',
    'Canada',
    'Australia',
    'Germany',
    'France',
    'Spain',
    'Italy',
    'Netherlands',
    'India',
    'Japan',
    'Singapore',
    'Other'
  ];

  const handleInputChange = (field: keyof UnifiedInputData, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    // Check if we have either URL or manual data
    const hasUrl = formData.url?.trim();
    const hasManualData = formData.brandName?.trim() && formData.category && formData.targetAudience?.trim() && formData.usp?.trim();
    
    if (!hasUrl && !hasManualData) {
      alert('Please provide either a URL or fill in all required fields: Brand Name, Category, Target Audience, and USP');
      return;
    }
    
    // Prepare final form data with normalized URL if needed
    let finalFormData = { ...formData };
    
    // If URL is provided, validate and normalize it
    if (hasUrl) {
      let normalizedUrl = formData.url!.trim();
      
      // Add https:// if no protocol is specified
      if (!normalizedUrl.startsWith('http://') && !normalizedUrl.startsWith('https://')) {
        normalizedUrl = 'https://' + normalizedUrl;
      }
      
      // Validate using browser's URL constructor
      try {
        new URL(normalizedUrl);
      } catch (error) {
        alert('Please enter a valid URL (e.g., example.com, www.example.com, or https://example.com)');
        return;
      }
      
      // Update final form data with normalized URL
      finalFormData.url = normalizedUrl;
    }

    setIsLoading(true);
    try {
      onSubmit(finalFormData);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="bg-white border border-gray-200 rounded-lg p-6 max-w-2xl">
      <div className="mb-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-2">
          Create Your {contentType === 'video' ? 'Video' : contentType === 'image' ? 'Image' : contentType === 'audio' ? 'Audio' : 'Text'} Ad
        </h3>
        <p className="text-sm text-gray-600">Provide a website URL, brand details, or both to get started</p>
      </div>

      <form onSubmit={handleSubmit} className="space-y-3">
        {/* Compact grid layout for most fields */}
        <div className="grid grid-cols-[140px_1fr] gap-x-4 gap-y-3 items-center">
          {/* Website URL */}
          <label className="flex items-center gap-2 text-sm font-medium text-gray-900">
            <Link size={14} />
            Website
          </label>
          <div>
            <input
              type="text"
              value={formData.url}
              onChange={(e) => handleInputChange('url', e.target.value)}
              placeholder="uni.cards or www.example.com"
              className="w-full px-3 py-2 text-sm border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent"
              disabled={isLoading}
            />
          </div>

          {/* Brand Name */}
          <label className="flex items-center gap-2 text-sm font-medium text-gray-900">
            <Building2 size={14} />
            Brand Name
          </label>
          <input
            type="text"
            value={formData.brandName}
            onChange={(e) => handleInputChange('brandName', e.target.value)}
            placeholder="Enter your brand name"
            className="w-full px-3 py-2 text-sm border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent"
            disabled={isLoading}
          />

          {/* Category */}
          <label className="flex items-center gap-2 text-sm font-medium text-gray-900">
            <Target size={14} />
            Category
          </label>
          <select
            value={formData.category}
            onChange={(e) => handleInputChange('category', e.target.value)}
            className="w-full px-3 py-2 text-sm border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent"
            disabled={isLoading}
          >
            <option value="">Select a category</option>
            {categories.map((category) => (
              <option key={category} value={category}>
                {category}
              </option>
            ))}
          </select>

          {/* Sub Category */}
          <label className="flex items-center gap-2 text-sm font-medium text-gray-900">
            <Target size={14} />
            Sub Category
          </label>
          <input
            type="text"
            value={formData.subCategory}
            onChange={(e) => handleInputChange('subCategory', e.target.value)}
            placeholder="e.g. Running shoes, Smartphones"
            className="w-full px-3 py-2 text-sm border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent"
            disabled={isLoading}
          />

          {/* Market */}
          <label className="flex items-center gap-2 text-sm font-medium text-gray-900">
            <Globe size={14} />
            Market
          </label>
          <select
            value={formData.market}
            onChange={(e) => handleInputChange('market', e.target.value)}
            className="w-full px-3 py-2 text-sm border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent"
            disabled={isLoading}
          >
            <option value="">Select a market</option>
            {markets.map((market) => (
              <option key={market} value={market}>
                {market}
              </option>
            ))}
          </select>

          {/* Target Audience */}
          <label className="flex items-center gap-2 text-sm font-medium text-gray-900">
            <Users size={14} />
            Target Audience
          </label>
          <input
            type="text"
            value={formData.targetAudience}
            onChange={(e) => handleInputChange('targetAudience', e.target.value)}
            placeholder="e.g. Young professionals aged 25-35"
            className="w-full px-3 py-2 text-sm border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent"
            disabled={isLoading}
          />

          {/* USP */}
          <label className="flex items-center gap-2 text-sm font-medium text-gray-900">
            <Lightbulb size={14} />
            USP
          </label>
          <input
            type="text"
            value={formData.usp}
            onChange={(e) => handleInputChange('usp', e.target.value)}
            placeholder="What makes your product unique?"
            className="w-full px-3 py-2 text-sm border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent"
            disabled={isLoading}
          />
        </div>

        {/* Content Description - full width */}
        <div className="pt-2">
          <label className="flex items-center gap-2 text-sm font-medium text-gray-900 mb-2">
            <MessageSquare size={14} />
            Describe your {contentType} content
          </label>
          <textarea
            value={formData.videoDescription}
            onChange={(e) => handleInputChange('videoDescription', e.target.value)}
            placeholder={
              contentType === 'video' 
                ? "What kind of video ad do you want? (e.g., 'Focus on product benefits', 'Make it fun and energetic', 'Professional tone for B2B audience')"
                : contentType === 'image'
                ? "What kind of image ad do you want? (e.g., 'Modern and minimalist', 'Bold and eye-catching', 'Professional product showcase')"
                : contentType === 'audio'
                ? "What kind of audio content do you want? (e.g., 'Upbeat radio ad', 'Professional voiceover', 'Catchy jingle with melody')"
                : "What kind of text content do you want? (e.g., 'Compelling ad copy', 'Social media posts', 'Email campaign content')"
            }
            rows={2}
            className="w-full px-3 py-2 text-sm border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent resize-none"
            disabled={isLoading}
          />
        </div>

        {/* Submit Button */}
        <div className="pt-2">
          <button
            type="submit"
            disabled={isLoading}
            className="w-full px-4 py-3 bg-purple-600 text-white rounded-lg hover:bg-purple-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors font-medium"
          >
            {isLoading ? 'Generating Ideas...' : `Generate ${contentType === 'video' ? 'Video' : contentType === 'image' ? 'Image' : contentType === 'audio' ? 'Audio' : 'Text'} Ideas`}
          </button>
        </div>
      </form>
    </div>
  );
};

export default UnifiedInputForm;