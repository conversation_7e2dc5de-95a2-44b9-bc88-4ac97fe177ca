'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { PlayCircle, ChevronRight, Crown, BarChart3, AlertTriangle } from 'lucide-react'
import Link from 'next/link'
import Image from 'next/image'

interface Analysis {
  id: string
  slug?: string | null
  youtube_url: string
  status: string
  title: string
  inferred_brand: string
  duration: number
  duration_seconds: number
  thumbnail_url: string
  video_thumbnail_url?: string
  created_at: string
  analysis_completed_at?: string
  youtube_video_id: string
  is_public: boolean
  view_count?: number
  like_count?: number
  featured?: boolean
  author_name?: string
}

interface PublicAnalysisCardProps {
  creditsRemaining: number
}

export default function PublicAnalysisCard({ creditsRemaining }: PublicAnalysisCardProps) {
  const [loading, setLoading] = useState(false)
  const [publicAnalyses, setPublicAnalyses] = useState<Analysis[]>([])
  const [analysesLoading, setAnalysesLoading] = useState(true)
  const [youtubeUrl, setYoutubeUrl] = useState('')
  const [error, setError] = useState('')

  useEffect(() => {
    const fetchPublicAnalyses = async () => {
      try {
        setAnalysesLoading(true)
        const response = await fetch('/api/analyses?page=1&limit=5')
        if (response.ok) {
          const data = await response.json()
          setPublicAnalyses(data.analyses || [])
        }
      } catch (error) {
        console.error('Error fetching public analyses:', error)
      } finally {
        setAnalysesLoading(false)
      }
    }

    fetchPublicAnalyses()
  }, [])

  const handleAnalyzeVideo = async (youtubeUrl: string) => {
    try {
      setLoading(true)
      setError('')
      console.log('🔍 Starting public analysis:', { youtubeUrl })

      const response = await fetch('/api/analyses', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ youtubeUrl })
      })

      const data = await response.json()

      if (!response.ok) {
        throw new Error(data.error || 'Failed to create analysis')
      }

      console.log('✅ Public analysis created:', data)

      // Redirect to analysis page
      if (data.slug) {
        window.location.href = `/ad/${data.slug}`
      } else {
        window.location.href = `/ad/${data.analysis_id}`
      }

    } catch (error: any) {
      console.error('❌ Error creating analysis:', error)
      setError(error.message || 'An error occurred while processing your request')
    } finally {
      setLoading(false)
    }
  }

  const handleSubmit = async () => {
    // Prevent multiple submissions
    if (loading) {
      return
    }

    if (!youtubeUrl.trim()) {
      setError('Please enter a YouTube URL')
      return
    }

    // Check credits before proceeding
    if (creditsRemaining <= 0) {
      setError('Insufficient credits. Please upgrade your plan.')
      return
    }

    // Validate YouTube URL format
    const youtubeRegex = /^(https?:\/\/)?(www\.)?(youtube\.com\/(watch\?v=|shorts\/)|youtu\.be\/)([\w-]+)/
    if (!youtubeRegex.test(youtubeUrl)) {
      setError('Please enter a valid YouTube URL')
      return
    }

    await handleAnalyzeVideo(youtubeUrl)
    setYoutubeUrl('')
  }

  return (
    <div className="h-full bg-gradient-to-br from-indigo-400 via-purple-500 to-indigo-500 rounded-2xl p-6 text-white">
      <div className="h-full flex flex-col">
        {/* Header */}
        <div className="flex-none mb-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-white/20 rounded-lg backdrop-blur-sm">
                <PlayCircle className="h-5 w-5 text-white" />
              </div>
              <div>
                <h3 className="text-lg font-semibold text-white">Public Ad Breakdown</h3>
                <p className="text-white/80 text-sm">
            💡 Works with any public YouTube video - ads, commercials, or promotional content (less than 3 mins)
                </p>
                                                          

              </div>
            </div>
            <div className="flex items-center gap-2">
              <div className="px-3 py-1 bg-white/20 backdrop-blur-sm text-white rounded-full text-xs font-medium border border-white/30">
                <Crown className="h-3 w-3 mr-1 inline" />
                {creditsRemaining}
              </div>
            </div>
          </div>
        </div>
        
        {/* Input Section */}
        <div className="flex-none space-y-3">
          <div className="flex gap-3">
            <input
              type="url"
              placeholder="https://www.youtube.com/watch?v=... or https://youtube.com/shorts/..."
              value={youtubeUrl}
              onChange={(e) => {
                setYoutubeUrl(e.target.value)
                setError('')
              }}
              onKeyDown={(e) => {
                if (e.key === 'Enter') {
                  e.preventDefault()
                  handleSubmit()
                }
              }}
              className="flex-1 h-10 px-4 text-sm border-2 border-white/30 rounded-xl focus:outline-none focus:border-white/80 bg-white/90 text-gray-800 placeholder-gray-500 transition-all duration-300"
              disabled={loading}
            />
            <button 
              onClick={handleSubmit}
              disabled={loading || !youtubeUrl.trim()}
              className="bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 disabled:opacity-50 text-white h-10 px-6 rounded-xl font-semibold text-sm transition-all duration-300 hover:-translate-y-0.5 hover:shadow-lg flex items-center justify-center gap-2"
            >
              {loading ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                  Analyzing...
                </>
              ) : (
                <>
                  <BarChart3 className="h-4 w-4" />
                  Analyze Now
                </>
              )}
            </button>
          </div>
          
          {error && (
            <div className="flex items-center gap-2 p-3 bg-red-500/20 border border-red-400/30 rounded-xl text-red-100">
              <AlertTriangle className="h-4 w-4 flex-shrink-0" />
              <p className="text-sm">{error}</p>
            </div>
          )}
          

          <div className="text-white/80 text-xs">
          ✨ As a member, your analyses will be added to the public library for the community to learn from
          </div>
        </div>
        
        {/* Recent Analyses Section */}
        <div className="flex-1 min-h-0 mt-4 flex flex-col overflow-hidden">
          <div className="flex items-center justify-between mb-2 flex-shrink-0">
            <h4 className="font-medium text-white text-sm">Recent Analyses</h4>
            <Link href="/ad-library?view=yours">
              <button className="text-white/80 hover:text-white text-xs bg-white/10 hover:bg-white/20 px-3 py-1 rounded-lg transition-all duration-300 border border-white/20 flex items-center gap-1">
                View All
                <ChevronRight className="h-3 w-3" />
              </button>
            </Link>
          </div>
          
          <div className="flex-1 min-h-0">
            <div className="space-y-2 h-full overflow-y-auto max-h-[120px]">
            {analysesLoading ? (
              <div className="flex items-center justify-center py-4">
                <div className="animate-spin h-4 w-4 border-b-2 border-white"></div>
              </div>
            ) : publicAnalyses.length > 0 ? (
              publicAnalyses.slice(0, 2).map((analysis) => (
                <Link key={analysis.id} href={`/ad/${analysis.slug || analysis.id}`}>
                  <div className="group bg-white/10 backdrop-blur-sm border border-white/20 rounded-lg p-3 hover:bg-white/20 hover:border-white/40 cursor-pointer transition-all duration-200">
                    <div className="flex items-center gap-3">
                      {analysis.thumbnail_url && (
                        <div className="relative flex-shrink-0">
                          <Image 
                            src={analysis.thumbnail_url} 
                            alt={analysis.title}
                            width={48}
                            height={28}
                            className="rounded object-cover border border-white/30"
                          />
                          <div className="absolute -bottom-1 -right-1 p-0.5 bg-green-500 rounded-full">
                            <PlayCircle className="h-2.5 w-2.5 text-white" />
                          </div>
                        </div>
                      )}
                      <div className="flex-1 min-w-0">
                        <p className="font-medium text-sm text-white truncate group-hover:text-white/90">
                          {analysis.title}
                        </p>
                        <div className="flex items-center gap-2 mt-1">
                          <p className="text-xs text-white/80 truncate">{analysis.inferred_brand}</p>
                          <span className={`text-xs px-2 py-0.5 rounded-full font-medium ${
                            analysis.status === 'completed' ? 'bg-green-500/20 text-green-100 border border-green-400/30' :
                            analysis.status === 'processing' ? 'bg-blue-500/20 text-blue-100 border border-blue-400/30' :
                            'bg-amber-500/20 text-amber-100 border border-amber-400/30'
                          }`}>
                            {analysis.status}
                          </span>
                        </div>
                      </div>
                      <ChevronRight className="h-4 w-4 text-white/60 group-hover:text-white/80 transition-colors flex-shrink-0" />
                    </div>
                  </div>
                </Link>
              ))
            ) : (
              <div className="text-center py-4 text-white/80">
                <div className="p-3 bg-white/10 rounded-lg inline-block mb-2 border border-white/20">
                  <PlayCircle className="h-5 w-5 text-white/80" />
                </div>
                <p className="text-sm">No analyses yet</p>
                <p className="text-xs text-white/60 mt-1">Analyze your first video above</p>
              </div>
            )}
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}