'use client'

import React, { useState, useEffect, useCallback } from 'react'
import { useSearch<PERSON><PERSON><PERSON>, useRouter } from 'next/navigation'
import Link from 'next/link'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { ChevronLeft, ChevronRight, Search, Plus, Filter, X, User } from 'lucide-react'
import { ShareButton, ShareableItem } from '@/components/ui/ShareModal'
import { useAuth } from '@/hooks/useAuth'
import AdLibraryTileGrid from './AdLibraryTileGrid'
import SubmitAdModal from './SubmitAdModal'

interface PublicAnalysis {
  id: string
  slug: string
  title: string
  inferred_brand: string
  thumbnail_url: string
  video_thumbnail_url: string
  duration_seconds: number
  duration_formatted: string
  created_at: string
  analysis_completed_at: string
  youtube_video_id: string
  is_public: boolean
  overall_sentiment?: number
  showcase?: boolean
  view_count?: number
}

interface PaginationInfo {
  page: number
  limit: number
  total: number
  totalPages: number
  hasNext: boolean
  hasPrev: boolean
}

interface CollectionData {
  tag: string
  title: string
  description: string
  count: number
  sample_ads: Array<{
    id: string
    title: string
    thumbnail_url: string
    slug: string
  }>
  banner_config?: any
}

interface AdLibraryDashboardProps {
  initialAnalyses: PublicAnalysis[]
  initialPagination: PaginationInfo
  initialFilters: {
    brands: string[]
    productCategories: string[]
    campaignCategories: string[]
    geographies: string[]
    agencies: string[]
    years: string[]
    celebrities: string[]
  }
}

function AdLibraryDashboard({ 
  initialAnalyses, 
  initialPagination, 
  initialFilters 
}: AdLibraryDashboardProps) {
  const { isAuthenticated } = useAuth()
  const searchParams = useSearchParams()
  const router = useRouter()
  
  // Public Analyses State
  const [analyses, setAnalyses] = useState<PublicAnalysis[]>(initialAnalyses)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState('')
  const [pagination, setPagination] = useState<PaginationInfo>(initialPagination)
  
  // Collections State
  const [collections, setCollections] = useState<CollectionData[]>([])
  const [collectionsLoading, setCollectionsLoading] = useState(false)

  // Filters and Search
  const [searchQuery, setSearchQuery] = useState('')
  const [sortBy, setSortBy] = useState<'recent' | 'popular'>('recent')
  const [selectedBrand, setSelectedBrand] = useState<string>('all')
  const [selectedProductCategory, setSelectedProductCategory] = useState<string>('all')
  const [selectedCampaignCategory, setSelectedCampaignCategory] = useState<string>('all')
  const [selectedGeography, setSelectedGeography] = useState<string>('all')
  const [selectedAgency, setSelectedAgency] = useState<string>('all')
  const [selectedYear, setSelectedYear] = useState<string>('all')
  const [selectedCelebrity, setSelectedCelebrity] = useState<string>('all')
  const [selectedDuration, setSelectedDuration] = useState<string>('all')
  const [selectedCollectionTag, setSelectedCollectionTag] = useState<string>('all')
  const [customBanner, setCustomBanner] = useState<any>(null)
  
  // Filter options
  const [availableBrands] = useState<string[]>(initialFilters.brands)
  const [availableYears] = useState<string[]>(initialFilters.years)
  


  // Filter modal and view state
  const [showFilterModal, setShowFilterModal] = useState(false)
  const [showSubmitAdModal, setShowSubmitAdModal] = useState(false)
  const [viewMode, setViewMode] = useState<'all' | 'yours'>(() => {
    const viewParam = searchParams.get('view')
    return (viewParam === 'yours' && isAuthenticated) ? 'yours' : 'all'
  })
  const [availableDurations] = useState([
    { value: 'short', label: 'Short (≤30s)' },
    { value: 'medium', label: 'Medium (31-60s)' },
    { value: 'long', label: 'Long (60s+)' }
  ])

  // Helper functions for filter management
  const getActiveFilterCount = () => {
    let count = 0
    if (selectedBrand !== 'all') count++
    if (selectedProductCategory !== 'all') count++
    if (selectedCampaignCategory !== 'all') count++
    if (selectedGeography !== 'all') count++
    if (selectedAgency !== 'all') count++
    if (selectedCelebrity !== 'all') count++
    if (selectedYear !== 'all') count++
    if (selectedDuration !== 'all') count++
    if (selectedCollectionTag !== 'all') count++
    return count
  }

  const getActiveFilterTags = () => {
    const tags: { key: string; label: string; value: string }[] = []
    
    if (selectedBrand !== 'all') {
      tags.push({ key: 'brand', label: 'Brand', value: selectedBrand })
    }
    if (selectedProductCategory !== 'all') {
      tags.push({ key: 'productCategory', label: 'Product', value: selectedProductCategory })
    }
    if (selectedCampaignCategory !== 'all') {
      tags.push({ key: 'campaignCategory', label: 'Campaign', value: selectedCampaignCategory })
    }
    if (selectedGeography !== 'all') {
      tags.push({ key: 'geography', label: 'Geography', value: selectedGeography })
    }
    if (selectedAgency !== 'all') {
      tags.push({ key: 'agency', label: 'Agency', value: selectedAgency })
    }
    if (selectedCelebrity !== 'all') {
      tags.push({ key: 'celebrity', label: 'Celebrity', value: selectedCelebrity })
    }
    if (selectedYear !== 'all') {
      tags.push({ key: 'year', label: 'Year', value: selectedYear })
    }
    if (selectedDuration !== 'all') {
      const durationLabel = availableDurations.find(d => d.value === selectedDuration)?.label || selectedDuration
      tags.push({ key: 'duration', label: 'Duration', value: durationLabel })
    }
    if (selectedCollectionTag !== 'all') {
      tags.push({ key: 'collectionTag', label: 'Collection', value: selectedCollectionTag })
    }
    
    return tags
  }

  const clearFilter = (filterKey: string) => {
    switch (filterKey) {
      case 'brand':
        setSelectedBrand('all')
        break
      case 'productCategory':
        setSelectedProductCategory('all')
        break
      case 'campaignCategory':
        setSelectedCampaignCategory('all')
        break
      case 'geography':
        setSelectedGeography('all')
        break
      case 'agency':
        setSelectedAgency('all')
        break
      case 'celebrity':
        setSelectedCelebrity('all')
        break
      case 'year':
        setSelectedYear('all')
        break
      case 'duration':
        setSelectedDuration('all')
        break
      case 'collectionTag':
        setSelectedCollectionTag('all')
        setCustomBanner(null) // Clear custom banner when clearing collection tag
        // Clear URL parameters when clearing collection tag
        router.push('/ad-library', { scroll: false })
        break
    }
    // Reset to page 1 when clearing any filter
    setPagination(prev => ({ ...prev, page: 1 }))
  }

  const clearAllFilters = () => {
    setSelectedBrand('all')
    setSelectedProductCategory('all')
    setSelectedCampaignCategory('all')
    setSelectedGeography('all')
    setSelectedAgency('all')
    setSelectedCelebrity('all')
    setSelectedYear('all')
    setSelectedDuration('all')
    setSelectedCollectionTag('all')
    setSearchQuery('')
    setCustomBanner(null) // Clear custom banner when clearing all filters
    // Reset to page 1 when clearing all filters
    setPagination(prev => ({ ...prev, page: 1 }))
    // Clear URL parameters to ensure clean state
    router.push('/ad-library', { scroll: false })
  }

  const fetchAnalyses = useCallback(async (
    page: number = 1, 
    search: string = '', 
    sort: string = 'recent', 
    brand: string = 'all',
    productCategory: string = 'all',
    campaignCategory: string = 'all',
    geography: string = 'all',
    agency: string = 'all',
    celebrity: string = 'all',
    year: string = 'all',
    duration: string = 'all',
    collectionTag: string = 'all'
  ) => {
    setLoading(true)
    setError('')
    
    try {
      const searchParams = new URLSearchParams({
        page: page.toString(),
        limit: '12',
        ...(search && { search }),
        sort,
        ...(brand && brand !== 'all' && { brand }),
        ...(celebrity && celebrity !== 'all' && { celebrity }),
        ...(year && year !== 'all' && { year }),
        ...(duration && duration !== 'all' && { duration }),
        ...(productCategory && productCategory !== 'all' && { product_category: productCategory }),
        ...(campaignCategory && campaignCategory !== 'all' && { campaign_category: campaignCategory }),
        ...(geography && geography !== 'all' && { geography }),
        ...(agency && agency !== 'all' && { agency }),
        ...(collectionTag && collectionTag !== 'all' && { collection_tags: collectionTag })
      })
      
      // Use different endpoint based on view mode
      const endpoint = viewMode === 'yours' ? '/api/analyses' : '/api/analyses/public'
      const response = await fetch(`${endpoint}?${searchParams}`)
      
      if (!response.ok) {
        throw new Error('Failed to fetch analyses')
      }
      
      const data = await response.json()
      setAnalyses(data.analyses || [])
      setPagination(data.pagination)
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load analyses')
    } finally {
      setLoading(false)
    }
  }, [viewMode])

  // Fetch featured collections for banner tiles
  const fetchCollections = useCallback(async () => {
    if (collectionsLoading) return
    
    setCollectionsLoading(true)
    try {
      const response = await fetch('/api/collections/featured?limit=6')
      if (response.ok) {
        const data = await response.json()
        setCollections(data.collections || [])
      }
    } catch (error) {
      console.error('Failed to fetch collections:', error)
    } finally {
      setCollectionsLoading(false)
    }
  }, [collectionsLoading])

  // Initialize from URL parameters
  useEffect(() => {
    const collectionTagParam = searchParams.get('collection_tags') || searchParams.get('playlist_tags')
    if (collectionTagParam) {
      setSelectedCollectionTag(collectionTagParam)
      fetchCustomBanner(collectionTagParam)
    } else {
      setCustomBanner(null)
    }
  }, [searchParams])

  // Fetch collections on component mount (only for main feed, not filtered views)
  useEffect(() => {
    // Only fetch collections for the main feed (no active filters)
    const hasActiveFilters = selectedBrand !== 'all' || selectedProductCategory !== 'all' || 
                            selectedCampaignCategory !== 'all' || selectedGeography !== 'all' || 
                            selectedAgency !== 'all' || selectedYear !== 'all' || selectedCelebrity !== 'all' || 
                            selectedDuration !== 'all' || selectedCollectionTag !== 'all' || 
                            searchQuery.trim() !== '' || viewMode !== 'all'
    
    if (!hasActiveFilters && collections.length === 0) {
      fetchCollections()
    } else if (hasActiveFilters && collections.length > 0) {
      // Clear collections when filters are active to ensure clean state
      setCollections([])
    }
  }, [selectedBrand, selectedProductCategory, selectedCampaignCategory, selectedGeography, 
      selectedAgency, selectedYear, selectedCelebrity, selectedDuration, selectedCollectionTag, 
      searchQuery, viewMode, collections.length, fetchCollections])

  const fetchCustomBanner = async (playlistTag: string) => {
    try {
      const response = await fetch(`/api/admin/playlists/banner-config?playlist_tag=${encodeURIComponent(playlistTag)}`)
      if (response.ok) {
        const data = await response.json()
        if (data.banner_config) {
          setCustomBanner(data.banner_config)
        } else {
          setCustomBanner(null)
        }
      } else {
        setCustomBanner(null)
      }
    } catch (error) {
      console.error('Failed to fetch custom banner:', error)
      setCustomBanner(null)
    }
  }

  useEffect(() => {
    const hasFilters = searchQuery || sortBy !== 'recent' || selectedBrand !== 'all' ||
                      selectedProductCategory !== 'all' || selectedCampaignCategory !== 'all' ||
                      selectedGeography !== 'all' || selectedAgency !== 'all' || selectedCelebrity !== 'all' ||
                      selectedYear !== 'all' || selectedDuration !== 'all' || selectedCollectionTag !== 'all'

    // Always fetch analyses when filters change or when in 'yours' mode
    // This ensures the feed refreshes both when filters are applied AND when they're cleared
    if (hasFilters || viewMode === 'yours') {
      fetchAnalyses(pagination.page, searchQuery, sortBy, selectedBrand, selectedProductCategory, selectedCampaignCategory, selectedGeography, selectedAgency, selectedCelebrity, selectedYear, selectedDuration, selectedCollectionTag)
    } else {
      // When no filters are active (cleared all filters), fetch all analyses (initial state)
      fetchAnalyses(pagination.page, '', 'recent', 'all', 'all', 'all', 'all', 'all', 'all', 'all', 'all', 'all')
    }
  }, [searchQuery, sortBy, selectedBrand, selectedProductCategory, selectedCampaignCategory, selectedGeography, selectedAgency, selectedCelebrity, selectedYear, selectedDuration, selectedCollectionTag, pagination.page, viewMode, fetchAnalyses])

  // Listen for submit ad modal events from header
  useEffect(() => {
    const handleOpenSubmitAdModal = () => {
      setShowSubmitAdModal(true)
    }

    window.addEventListener('openSubmitAdModal', handleOpenSubmitAdModal)
    return () => {
      window.removeEventListener('openSubmitAdModal', handleOpenSubmitAdModal)
    }
  }, [])

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault()
    setPagination(prev => ({ ...prev, page: 1 }))
    fetchAnalyses(1, searchQuery, sortBy, selectedBrand, selectedProductCategory, selectedCampaignCategory, selectedGeography, selectedAgency, selectedCelebrity, selectedYear, selectedDuration, selectedCollectionTag)
  }

  const handlePageChange = (newPage: number) => {
    setPagination(prev => ({ ...prev, page: newPage }))
    fetchAnalyses(newPage, searchQuery, sortBy, selectedBrand, selectedProductCategory, selectedCampaignCategory, selectedGeography, selectedAgency, selectedCelebrity, selectedYear, selectedDuration, selectedCollectionTag)
    window.scrollTo({ top: 0, behavior: 'smooth' })
  }

  // Get playlist share data
  const getPlaylistShareData = (): ShareableItem | null => {
    if (selectedCollectionTag === 'all' || !selectedCollectionTag) return null
    
    return {
      id: selectedCollectionTag,
      playlist_tag: selectedCollectionTag,
      title: customBanner?.title || `${selectedCollectionTag.charAt(0).toUpperCase() + selectedCollectionTag.slice(1)} Collection`,
      subtitle: customBanner?.subtitle,
      type: 'playlist'
    }
  }

  // Helper functions for banner styling
  const getBannerBackgroundStyle = (banner: any) => {
    if (banner.background_type === 'solid') {
      return { backgroundColor: banner.background_color || '#ffffff' }
    } else if (banner.background_gradient_start && banner.background_gradient_middle && banner.background_gradient_end) {
      return {
        background: `linear-gradient(to bottom right, ${banner.background_gradient_start}, ${banner.background_gradient_middle}, ${banner.background_gradient_end})`
      }
    } else {
      // Fallback to old gradient format
      return { background: `linear-gradient(to bottom right, #eef2ff, #f3e8ff, #eff6ff)` }
    }
  }

  const getBannerHeadingStyle = (banner: any) => {
    if (banner.heading_color_type === 'solid') {
      return { color: banner.heading_solid_color || '#1f2937' }
    } else if (banner.heading_gradient_start && banner.heading_gradient_middle && banner.heading_gradient_end) {
      return {
        background: `linear-gradient(to right, ${banner.heading_gradient_start}, ${banner.heading_gradient_middle}, ${banner.heading_gradient_end})`,
        backgroundClip: 'text',
        WebkitBackgroundClip: 'text',
        color: 'transparent'
      }
    } else {
      // Fallback to default gradient
      return {
        background: 'linear-gradient(to right, #4338ca, #7c3aed, #2563eb)',
        backgroundClip: 'text',
        WebkitBackgroundClip: 'text',
        color: 'transparent'
      }
    }
  }

  return (
    <div className="w-full min-w-0 overflow-hidden px-3 md:px-4">
      {/* Value Proposition Banner */}
      <div 
        className="border border-indigo-200 rounded-2xl p-4 md:p-6 mb-6 text-center relative"
        style={customBanner ? getBannerBackgroundStyle(customBanner) : { background: 'linear-gradient(to bottom right,rgb(5, 20, 66),rgb(28, 19, 38),rgb(10, 43, 85))' }}
      >
        
        <div className="max-w-4xl mx-auto">
          <h1 
            className="text-2xl md:text-3xl lg:text-4xl font-bold mb-3 md:mb-4"
            style={customBanner ? getBannerHeadingStyle(customBanner) : {
              background: 'linear-gradient(to right, #4338ca, #7c3aed, #2563eb)',
              backgroundClip: 'text',
              WebkitBackgroundClip: 'text',
              color: 'transparent'
            }}
          >
            {customBanner?.title || 'Ad Library'}
          </h1>
          <p 
            className="text-base md:text-lg mb-4 md:mb-6 max-w-3xl mx-auto leading-relaxed"
            style={{ color: customBanner?.body_text_color || customBanner?.text_color || '#ffffff' }}
          >
            {customBanner?.subtitle || 'Discover AI-powered insights from video ad analyses curated by our community. Learn from successful advertising strategies, creative techniques, and industry context.'}
          </p>
          <div className="flex flex-col sm:flex-row gap-3 justify-center items-center text-sm md:text-base">
            {(customBanner?.features || ['AI-Powered Analysis', 'Community Curated', 'Strategic Insights']).map((feature: string, index: number) => (
              <div key={index} className="flex items-center gap-2">
                <div className={`w-2 h-2 ${index === 0 ? 'bg-green-500' : index === 1 ? 'bg-blue-500' : 'bg-purple-500'} rounded-full`}></div>
                <span style={{ color: customBanner?.body_text_color || customBanner?.text_color || '#ffffff' }}>{feature}</span>
              </div>
            ))}
          </div>
          
          {/* Share Button - bottom right for both desktop and mobile */}
          {getPlaylistShareData() && (
            <div className="mt-4 flex justify-end">
              <ShareButton 
                item={getPlaylistShareData()}
                size="sm"
                variant="outline"
                className="bg-white/90 hover:bg-white border-white/50 text-gray-700 hover:text-gray-900 px-2 py-1"
              >
                Share Collection
              </ShareButton>
            </div>
          )}
        </div>
      </div>

      {/* Content Section Header */}
      <div className="bg-white rounded-2xl p-2 mb-4 border border-gray-200 shadow-sm w-full">
        <div className="flex items-center gap-1 md:gap-4 w-full">
          {/* View Mode Toggle */}
          <div className="flex items-center bg-white rounded-lg p-1 border h-9 flex-shrink-0">
            <Button
              variant={viewMode === 'all' ? 'default' : 'ghost'}
              onClick={() => setViewMode('all')}
              className="text-xs px-2 md:px-3 h-7"
            >
              <span className="hidden sm:inline">All Ads</span>
              <span className="sm:hidden">All</span>
            </Button>
            {isAuthenticated && (
              <Button
                variant={viewMode === 'yours' ? 'default' : 'ghost'}
                onClick={() => setViewMode('yours')}
                className="text-xs px-2 md:px-3 h-7"
              >
                <User className="h-3 w-3 sm:mr-1" />
                <span className="hidden sm:inline">Your Curation</span>
                <span className="sm:hidden sr-only">Yours</span>
              </Button>
            )}
          </div>

          {/* Right-aligned controls on desktop */}
          <div className="flex items-center gap-1 md:gap-2 flex-1 md:justify-end min-w-0">
            {/* Search Bar */}
            <form onSubmit={handleSearch} className="flex gap-1 flex-1 md:flex-initial md:max-w-md min-w-0">
              <Input
                type="text"
                placeholder="Search..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="flex-1 min-w-0 text-sm h-9"
              />
              <Button type="submit" variant="outline" className="px-2 h-9 w-9 flex-shrink-0">
                <Search className="h-4 w-4" />
              </Button>
            </form>

            {/* Sort Dropdown - Hidden on mobile */}
            <div className="hidden md:block flex-shrink-0">
              <Select
                value={sortBy}
                onValueChange={(value) => setSortBy(value as 'recent' | 'popular')}
              >
                <SelectTrigger className="w-[140px] h-9">
                  <SelectValue placeholder="Sort by" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="recent">Most Recent</SelectItem>
                  <SelectItem value="popular">Most Popular</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* Filter Button - Icon only on mobile */}
            <Button
              variant="outline"
              onClick={() => setShowFilterModal(true)}
              className="flex items-center gap-1 px-2 md:px-3 h-9 flex-shrink-0"
            >
              <Filter className="h-4 w-4" />
              <span className="hidden md:inline">Filters</span>
              {/* Active Filter Count */}
              {getActiveFilterCount() > 0 && (
                <Badge variant="destructive" className="ml-1 px-1.5 py-0 text-xs">
                  {getActiveFilterCount()}
                </Badge>
              )}
            </Button>
          </div>
        </div>

        {/* Active Filters Tags */}
        {getActiveFilterTags().length > 0 && (
          <div className="flex flex-wrap gap-2 mt-4">
            <span className="text-sm text-gray-600 mr-2">Active filters:</span>
            {getActiveFilterTags().map((tag) => (
              <Badge key={tag.key} variant="secondary" className="flex items-center gap-1">
                {tag.label}: {tag.value}
                <X
                  className="h-3 w-3 cursor-pointer hover:text-red-600"
                  onClick={() => clearFilter(tag.key)}
                />
              </Badge>
            ))}
            <Button
              variant="ghost"
              size="sm"
              onClick={clearAllFilters}
              className="text-xs text-red-600 hover:text-red-700 h-6 px-2"
            >
              Clear all
            </Button>
          </div>
        )}
      </div>

      {/* Filter Modal */}
      {showFilterModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4">
          <div className="bg-white rounded-lg max-w-2xl w-full max-h-[80vh] overflow-y-auto">
            <div className="p-6">
              <div className="flex items-center justify-between mb-6">
                <h2 className="text-xl font-semibold">Filters</h2>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setShowFilterModal(false)}
                >
                  <X className="h-4 w-4" />
                </Button>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {/* Brand Filter */}
                <div>
                  <label className="block text-sm font-medium mb-2">Brand</label>
                  <Select value={selectedBrand} onValueChange={setSelectedBrand}>
                    <SelectTrigger>
                      <SelectValue placeholder="All brands" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All brands</SelectItem>
                      {availableBrands.map((brand) => (
                        <SelectItem key={brand} value={brand}>
                          {brand}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                {/* Product Category Filter */}
                <div>
                  <label className="block text-sm font-medium mb-2">Product Category</label>
                  <Select value={selectedProductCategory} onValueChange={setSelectedProductCategory}>
                    <SelectTrigger>
                      <SelectValue placeholder="All products" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Products</SelectItem>
                      {initialFilters.productCategories.map((category) => (
                        <SelectItem key={category} value={category}>
                          {category}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                {/* Campaign Category Filter */}
                <div>
                  <label className="block text-sm font-medium mb-2">Campaign Category</label>
                  <Select value={selectedCampaignCategory} onValueChange={setSelectedCampaignCategory}>
                    <SelectTrigger>
                      <SelectValue placeholder="All campaigns" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Campaigns</SelectItem>
                      {initialFilters.campaignCategories.map((category) => (
                        <SelectItem key={category} value={category}>
                          {category}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                {/* Geography Filter */}
                <div>
                  <label className="block text-sm font-medium mb-2">Geography</label>
                  <Select value={selectedGeography} onValueChange={setSelectedGeography}>
                    <SelectTrigger>
                      <SelectValue placeholder="All geographies" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Geographies</SelectItem>
                      {initialFilters.geographies.map((geo) => (
                        <SelectItem key={geo} value={geo}>
                          {geo}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                {/* Agency Filter */}
                <div>
                  <label className="block text-sm font-medium mb-2">Agency</label>
                  <Select value={selectedAgency} onValueChange={setSelectedAgency}>
                    <SelectTrigger>
                      <SelectValue placeholder="All agencies" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Agencies</SelectItem>
                      {initialFilters.agencies.map((agency) => (
                        <SelectItem key={agency} value={agency}>
                          {agency}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                {/* Celebrity Filter */}
                <div>
                  <label className="block text-sm font-medium mb-2">Celebrity</label>
                  <Select value={selectedCelebrity} onValueChange={setSelectedCelebrity}>
                    <SelectTrigger>
                      <SelectValue placeholder="All celebrities" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Celebrities</SelectItem>
                      {initialFilters.celebrities.map((celebrity) => (
                        <SelectItem key={celebrity} value={celebrity}>
                          {celebrity}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                {/* Year Filter */}
                <div>
                  <label className="block text-sm font-medium mb-2">Year</label>
                  <Select value={selectedYear} onValueChange={setSelectedYear}>
                    <SelectTrigger>
                      <SelectValue placeholder="All years" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All years</SelectItem>
                      {availableYears.map((year) => (
                        <SelectItem key={year} value={year}>
                          {year}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                {/* Duration Filter */}
                <div>
                  <label className="block text-sm font-medium mb-2">Duration</label>
                  <Select value={selectedDuration} onValueChange={setSelectedDuration}>
                    <SelectTrigger>
                      <SelectValue placeholder="All durations" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All durations</SelectItem>
                      {availableDurations.map((duration) => (
                        <SelectItem key={duration.value} value={duration.value}>
                          {duration.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="flex justify-between mt-6">
                <Button
                  variant="outline"
                  onClick={clearAllFilters}
                >
                  Reset All Filters
                </Button>
                <Button
                  onClick={() => setShowFilterModal(false)}
                >
                  Apply Filters
                </Button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Results Stats */}
      {!loading && (
        <div className="mb-6 px-2 md:px-0">
          <div className="flex flex-col sm:flex-row sm:items-center justify-between text-sm text-gray-600">
            <span>
              Showing {analyses.length} of {pagination.total} {viewMode === 'yours' ? 'your analyses' : 'analyses'}
            </span>
            {searchQuery && (
              <span>
                Search results for &quot;{searchQuery}&quot;
              </span>
            )}
          </div>
        </div>
      )}

      {/* Error Message */}
      {error && (
        <div className="bg-red-50 border border-red-200 text-red-700 p-4 rounded-lg mb-6 mx-2 md:mx-0">
          {error}
        </div>
      )}

      {/* Analyses Grid */}
      <div className="w-full max-w-full overflow-hidden">
        {loading ? (
          <AdLibraryTileGrid
            analyses={[]}
            collections={[]}
            loading={true}
            showFeatured={false}
            showPrivacyBadge={false}
            showCollectionBanners={false}
            columns="auto"
            className="mb-8 w-full min-w-0"
          />
        ) : analyses.length === 0 ? (
        <div className="text-center py-16 bg-gray-50 rounded-lg">
          <div className="max-w-md mx-auto">
            <h3 className="text-lg font-medium mb-2 text-gray-900">
              {searchQuery ? 'No search results found' : 'No analyses found'}
            </h3>
            <p className="text-gray-600 mb-6">
              {searchQuery
                ? `Try adjusting your search terms or browse all analyses.`
                : 'Be the first to create and share an ad analysis with the community!'
              }
            </p>
            <div className="flex gap-3 justify-center">
              {searchQuery ? (
                <Button
                  variant="outline"
                  onClick={() => {
                    setSearchQuery('')
                    setSelectedBrand('all')
                    setSelectedProductCategory('all')
                    setSelectedCampaignCategory('all')
                    setSelectedGeography('all')
                    setSelectedAgency('all')
                    setSelectedCelebrity('all')
                    setSelectedYear('all')
                    setSelectedDuration('all')
                    setSelectedCollectionTag('all')
                    fetchAnalyses(1, '', sortBy, 'all', 'all', 'all', 'all', 'all', 'all', 'all', 'all', 'all')
                  }}
                >
                  Clear Search
                </Button>
              ) : (
                <Link href={isAuthenticated ? "/studio" : "/sign-up"}>
                  <Button className="bg-blue-600 hover:bg-blue-700">
                    <Plus className="h-4 w-4 mr-1" />
                    {isAuthenticated ? 'Create Analysis' : 'Get Started'}
                  </Button>
                </Link>
              )}
            </div>
          </div>
          </div>
        ) : (
          <>
            <AdLibraryTileGrid
              analyses={analyses}
              collections={collections}
              loading={false}
              showFeatured={false}
              showPrivacyBadge={false}
              showCollectionBanners={
                // Only show collection banners on main feed with no active filters
                selectedBrand === 'all' && 
                selectedProductCategory === 'all' && 
                selectedCampaignCategory === 'all' && 
                selectedGeography === 'all' && 
                selectedAgency === 'all' && 
                selectedYear === 'all' && 
                selectedCelebrity === 'all' && 
                selectedDuration === 'all' && 
                selectedCollectionTag === 'all' && 
                searchQuery.trim() === '' && 
                viewMode === 'all'
              }
              collectionBannerPositions={[3, 6, 9, 12, 15]}
              columns="auto"
              className="mb-8 w-full min-w-0"
            />

            {/* Pagination */}
            {pagination.totalPages > 1 && (
            <div className="flex items-center justify-center space-x-2 mb-8">
              <Button
                variant="outline"
                size="sm"
                onClick={() => handlePageChange(pagination.page - 1)}
                disabled={!pagination.hasPrev}
              >
                <ChevronLeft className="h-4 w-4 mr-1" />
                Previous
              </Button>

              <div className="flex items-center space-x-1">
                {[...Array(Math.min(5, pagination.totalPages))].map((_, i) => {
                  const pageNum = pagination.page <= 3
                    ? i + 1
                    : pagination.page >= pagination.totalPages - 2
                      ? pagination.totalPages - 4 + i
                      : pagination.page - 2 + i

                  if (pageNum < 1 || pageNum > pagination.totalPages) return null

                  return (
                    <Button
                      key={pageNum}
                      variant={pageNum === pagination.page ? "default" : "outline"}
                      size="sm"
                      onClick={() => handlePageChange(pageNum)}
                      className="w-10"
                    >
                      {pageNum}
                    </Button>
                  )
                })}
              </div>

              <Button
                variant="outline"
                size="sm"
                onClick={() => handlePageChange(pagination.page + 1)}
                disabled={!pagination.hasNext}
              >
                Next
                <ChevronRight className="h-4 w-4 ml-1" />
              </Button>
            </div>
          )}
        </>
      )}
      </div>

      {/* Call to Action */}
      {!isAuthenticated && analyses.length > 0 && (
        <div className="bg-gradient-to-r from-blue-50 to-purple-50 border border-blue-200 rounded-lg p-6 md:p-8 text-center mx-2 md:mx-0">
          <h3 className="text-2xl font-bold text-gray-900 mb-3">
            Ready to Analyze Your Own Ads?
          </h3>
          <p className="text-lg text-gray-600 mb-6 max-w-2xl mx-auto">
            Join breakdown.ad to create AI-powered analyses of your video ads,
            gain actionable insights, and share your findings with the community.
          </p>
          <div className="flex gap-3 justify-center">
            <Link href="/sign-up">
              <Button size="lg" className="bg-blue-600 hover:bg-blue-700">
                Start Free Trial
              </Button>
            </Link>
            <Link href="/ad-library">
              <Button size="lg" variant="outline">
                Browse Ad Library
              </Button>
            </Link>
          </div>
        </div>
      )}

      {/* Submit Ad Modal */}
      <SubmitAdModal
        isOpen={showSubmitAdModal}
        onClose={() => setShowSubmitAdModal(false)}
      />
    </div>
  )
}

export default AdLibraryDashboard
