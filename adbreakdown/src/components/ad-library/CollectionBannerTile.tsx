'use client'

import React from 'react'
import Link from 'next/link'
import Image from 'next/image'
import { Card } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Tag, ArrowRight, TrendingUp, Star } from 'lucide-react'

interface CollectionBannerTileProps {
  tag: string
  title: string
  description: string
  adCount: number
  featuredAds?: Array<{
    id: string
    title: string
    thumbnail_url: string
    slug: string
  }>
  gradient?: string
  textColor?: string
  className?: string
}

export default function CollectionBannerTile({
  tag,
  title,
  description,
  adCount,
  featuredAds = [],
  gradient = 'from-indigo-50 via-purple-50 to-blue-50',
  textColor = 'text-gray-700',
  className = ""
}: CollectionBannerTileProps) {
  return (
    <Link href={`/ad-library?collection_tags=${encodeURIComponent(tag)}`}>
      <Card className={`group relative overflow-hidden border-0 shadow-sm hover:shadow-xl transition-all duration-300 hover:-translate-y-1 bg-gradient-to-br ${gradient} cursor-pointer ${className}`}>
        {/* Collection Badge */}
        <div className="absolute top-3 left-3 z-20">
          <Badge className="bg-gradient-to-r from-violet-500 to-purple-500 text-white border-0 shadow-md">
            <Tag className="h-3 w-3 mr-1" />
            Collection
          </Badge>
        </div>

        {/* Ad Count Badge */}
        <div className="absolute top-3 right-3 z-20">
          <Badge variant="secondary" className="bg-white/90 text-gray-700 border-gray-200 shadow-sm">
            <TrendingUp className="h-3 w-3 mr-1" />
            {adCount} ads
          </Badge>
        </div>

        {/* Main Content Area - Same aspect ratio as video thumbnail */}
        <div className="relative aspect-video bg-gradient-to-br from-white/20 to-white/40 overflow-hidden flex flex-col justify-center items-center p-6">
          {/* Decorative Background Pattern */}
          <div className="absolute inset-0 opacity-10">
            <div className="absolute top-4 left-4 w-8 h-8 bg-purple-400 rounded-full"></div>
            <div className="absolute top-12 right-8 w-6 h-6 bg-blue-400 rounded-full"></div>
            <div className="absolute bottom-8 left-8 w-4 h-4 bg-indigo-400 rounded-full"></div>
            <div className="absolute bottom-4 right-4 w-10 h-10 bg-violet-400 rounded-full"></div>
          </div>

          {/* Main Content */}
          <div className="relative z-10 text-center">
            <div className="flex items-center justify-center mb-3">
              <Tag className={`h-8 w-8 ${textColor}`} />
            </div>
            <h3 className={`text-lg font-bold mb-2 ${textColor} group-hover:scale-105 transition-transform duration-300`}>
              {title}
            </h3>
            <p className={`text-sm opacity-80 mb-4 max-w-sm ${textColor}`}>
              {description}
            </p>
            
            {/* Call to Action */}
            <div className={`inline-flex items-center gap-2 text-sm font-medium ${textColor} group-hover:gap-3 transition-all duration-300`}>
              <span>Explore Collection</span>
              <ArrowRight className="h-4 w-4 group-hover:translate-x-1 transition-transform duration-300" />
            </div>
          </div>
        </div>

        {/* Bottom Content Area - Similar to AdTile structure */}
        <div className="p-4 bg-white/80 backdrop-blur-sm">
          <div className="flex items-center justify-between mb-2">
            <span className={`text-sm font-medium ${textColor}`}>
              {title}
            </span>
            <span className="text-xs text-gray-500">
              Collection
            </span>
          </div>
          
          {/* Mini thumbnails if available */}
          {featuredAds.length > 0 && (
            <div className="flex items-center gap-1 mb-2">
              <span className="text-xs text-gray-500 mr-2">Featured:</span>
              <div className="flex -space-x-1">
                {featuredAds.slice(0, 3).map((ad, index) => (
                  <div 
                    key={ad.id} 
                    className="relative w-6 h-6 rounded-full bg-gray-200 border-2 border-white overflow-hidden"
                    style={{ zIndex: 3 - index }}
                  >
                    {ad.thumbnail_url && (
                      <Image 
                        src={ad.thumbnail_url} 
                        alt={ad.title}
                        fill
                        className="object-cover"
                        sizes="24px"
                      />
                    )}
                  </div>
                ))}
                {featuredAds.length > 3 && (
                  <div className="w-6 h-6 rounded-full bg-gray-300 border-2 border-white flex items-center justify-center">
                    <span className="text-xs text-gray-600">+{featuredAds.length - 3}</span>
                  </div>
                )}
              </div>
            </div>
          )}

          {/* Collection Stats */}
          <div className="flex items-center justify-between text-xs text-gray-500">
            <span>{adCount} video{adCount !== 1 ? 's' : ''}</span>
            <div className="flex items-center gap-1">
              <Star className="h-3 w-3" />
              <span>Curated</span>
            </div>
          </div>
        </div>

        {/* Hover overlay effect */}
        <div className="absolute inset-0 bg-gradient-to-t from-purple-600/10 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
      </Card>
    </Link>
  )
}