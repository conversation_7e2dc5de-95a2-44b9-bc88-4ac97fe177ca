'use client'

import React, { useState, useEffect } from 'react'
import Link from 'next/link'
import { Button } from '@/components/ui/button'
import { AlertTriangle, X } from 'lucide-react'
import { useAuth } from '@/hooks/useAuth'

interface SubmitAdModalProps {
  isOpen: boolean
  onClose: () => void
}

function SubmitAdModal({ isOpen, onClose }: SubmitAdModalProps) {
  const { isAuthenticated } = useAuth()
  const [analysisUrl, setAnalysisUrl] = useState('')
  const [analysisLoading, setAnalysisLoading] = useState(false)
  const [analysisError, setAnalysisError] = useState('')

  // Close modal on escape key
  useEffect(() => {
    const handleEscape = (e: KeyboardEvent) => {
      if (e.key === 'Escape') {
        onClose()
      }
    }

    if (isOpen) {
      document.addEventListener('keydown', handleEscape)
      document.body.style.overflow = 'hidden'
    }

    return () => {
      document.removeEventListener('keydown', handleEscape)
      document.body.style.overflow = 'unset'
    }
  }, [isOpen, onClose])

  const handleAnalyzeVideo = async () => {
    if (!analysisUrl.trim()) {
      setAnalysisError('Please enter a YouTube URL')
      return
    }

    const youtubeRegex = /^(https?:\/\/)?(www\.)?(youtube\.com\/(watch\?v=|shorts\/)|youtu\.be\/)([A-Za-z0-9_-]{11})/
    if (!youtubeRegex.test(analysisUrl)) {
      setAnalysisError('Please enter a valid YouTube URL')
      return
    }

    try {
      setAnalysisLoading(true)
      setAnalysisError('')

      const response = await fetch('/api/analyses', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ youtubeUrl: analysisUrl })
      })

      const data = await response.json()

      if (!response.ok) {
        throw new Error(data.error || 'Failed to create analysis')
      }

      if (data.analysis?.slug) {
        window.location.href = `/ad/${data.analysis.slug}`
      } else if (data.slug) {
        window.location.href = `/ad/${data.slug}`
      } else {
        window.location.href = `/ad/${data.analysis_id}`
      }

    } catch (error: any) {
      console.error('Error creating analysis:', error)
      setAnalysisError(error.message || 'An error occurred while processing your request')
    } finally {
      setAnalysisLoading(false)
    }
  }

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4">
      <div className="bg-white rounded-2xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <h2 className="text-2xl font-bold bg-gradient-to-r from-indigo-600 via-purple-600 to-blue-600 bg-clip-text text-transparent">
            Submit Ad for Analysis
          </h2>
          <Button
            variant="ghost"
            size="sm"
            onClick={onClose}
            className="h-8 w-8 p-0"
          >
            <X className="h-4 w-4" />
          </Button>
        </div>

        {/* Content */}
        <div className="p-6">
          <div className="bg-gradient-to-br from-indigo-400 via-purple-500 to-indigo-500 rounded-2xl p-4 md:p-6 text-white mb-6">
            <div className="text-white font-semibold mb-3 md:mb-4 text-lg">
              🔍 Paste any public YouTube ad URL
            </div>
            <div className="flex flex-col sm:flex-row gap-3 mb-3">
              <input
                type="url"
                placeholder="https://www.youtube.com/watch?v=..."
                value={analysisUrl}
                onChange={(e) => {
                  setAnalysisUrl(e.target.value)
                  setAnalysisError('')
                }}
                className="flex-1 h-12 px-4 text-base border-2 border-white/30 rounded-xl focus:outline-none focus:border-white/80 bg-white/90 text-gray-800 placeholder-gray-500 transition-all duration-300"
                disabled={analysisLoading}
              />
              <button
                onClick={handleAnalyzeVideo}
                disabled={analysisLoading || !analysisUrl.trim()}
                className="bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 disabled:opacity-50 text-white h-12 px-8 rounded-xl font-semibold text-base transition-all duration-300 hover:-translate-y-0.5 hover:shadow-lg flex items-center justify-center gap-2 min-w-[180px] whitespace-nowrap"
              >
                {analysisLoading ? (
                  <>
                    <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white"></div>
                    <span>Analyzing...</span>
                  </>
                ) : (
                  <>
                    🚀 Breakdown Ad
                  </>
                )}
              </button>
            </div>
            <div className="text-white/80 text-sm">
              💡 Works with any public YouTube video - ads, commercials, or promotional content (less than 3 mins)
            </div>
            {analysisError && (
              <div className="flex items-center gap-2 p-3 bg-red-500/20 border border-red-400/30 rounded-xl text-red-100 mt-3">
                <AlertTriangle className="h-4 w-4 flex-shrink-0" />
                <p className="text-sm">{analysisError}</p>
              </div>
            )}
          </div>

          {/* Additional Info for Authenticated Users */}
          {isAuthenticated && (
            <div className="bg-blue-50 border border-blue-200 rounded-xl p-4 mb-4">
              <p className="text-blue-800 text-sm">
                ✨ As a member, ads you curate will be public and visible in the Ad Library. For private ad analysis, go to your <Link href="/studio" className="underline font-medium">Studio</Link>.
              </p>
            </div>
          )}

          {/* Benefits */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
            <div className="text-center p-4 bg-gray-50 rounded-xl">
              <div className="text-2xl mb-2">🤖</div>
              <h3 className="font-semibold text-gray-900 mb-1">AI Analysis</h3>
              <p className="text-sm text-gray-600">Get detailed insights powered by advanced AI</p>
            </div>
            <div className="text-center p-4 bg-gray-50 rounded-xl">
              <div className="text-2xl mb-2">📊</div>
              <h3 className="font-semibold text-gray-900 mb-1">Marketing Insights</h3>
              <p className="text-sm text-gray-600">Understand what makes ads effective</p>
            </div>
            <div className="text-center p-4 bg-gray-50 rounded-xl">
              <div className="text-2xl mb-2">🌟</div>
              <h3 className="font-semibold text-gray-900 mb-1">Community</h3>
              <p className="text-sm text-gray-600">Share insights with fellow marketers</p>
            </div>
          </div>

          {/* Call to Action for Non-Authenticated Users */}
          {!isAuthenticated && (
            <div className="bg-gradient-to-r from-blue-50 to-purple-50 border border-blue-200 rounded-xl p-6 text-center">
              <h3 className="text-xl font-bold text-gray-900 mb-2">
                Join breakdown.ad
              </h3>
              <p className="text-gray-600 mb-4">
                Sign up to submit ads, save analyses, and access advanced features.
              </p>
              <div className="flex gap-3 justify-center">
                <Link href="/sign-up">
                  <Button className="bg-blue-600 hover:bg-blue-700">
                    Sign Up Free
                  </Button>
                </Link>
                <Link href="/sign-in">
                  <Button variant="outline">
                    Sign In
                  </Button>
                </Link>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}

export default SubmitAdModal
