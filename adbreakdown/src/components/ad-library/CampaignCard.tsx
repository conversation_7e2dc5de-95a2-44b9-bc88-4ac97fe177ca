'use client'

import React from 'react'
import Link from 'next/link'
import Image from 'next/image'
import { Play, Eye } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { 
  PublicAnalysis, 
  getCelebrityWithContext, 
  getCampaignCategoriesDisplay 
} from '@/types/analysis'

interface CampaignCardProps {
  analysis: PublicAnalysis
  size?: "small" | "normal" | "large"
  showMetrics?: boolean
  variant?: "default" | "trending" | "featured"
  theme?: "light" | "dark"
}

export default function CampaignCard({ 
  analysis, 
  size = "normal", 
  showMetrics = false,
  variant = "default",
  theme = "light"
}: CampaignCardProps) {
  const cardSizes = {
    small: "w-full sm:w-64 flex-shrink-0",
    normal: "w-full sm:w-72 lg:w-80 xl:w-96", 
    large: "w-full sm:w-80 lg:w-96 xl:w-[26rem]"
  }

  const cardHeights = {
    small: showMetrics ? "h-84" : "h-80",
    normal: showMetrics ? "h-100" : "h-92", 
    large: showMetrics ? "h-[27rem]" : "h-100"
  }

  // Truncate text helper
  const truncateText = (text: string, maxLength: number) => {
    if (!text) return '';
    return text.length > maxLength ? text.substring(0, maxLength) + '...' : text;
  }

  // Parse marketing analysis to extract gut reaction (using SharedAnalysisPage logic)
  const parseMarketingAnalysis = (data: any) => {
    if (!data) return null
    try {
      if (typeof data === 'string') {
        let cleanData = data
          .replace(/```json\s*/g, '')
          .replace(/```\s*/g, '')
          .replace(/^\s*```.*$/gm, '')
          .trim()
        
        // Find the first { and last } to extract the JSON object
        const firstBrace = cleanData.indexOf('{')
        const lastBrace = cleanData.lastIndexOf('}')
        
        if (firstBrace !== -1 && lastBrace !== -1 && lastBrace > firstBrace) {
          cleanData = cleanData.substring(firstBrace, lastBrace + 1)
        }
        
        // Try to parse the cleaned data
        const parsed = JSON.parse(cleanData)
        return parsed
      }
      return data
    } catch (error) {
      console.error('Failed to parse marketing analysis:', error)
      return null
    }
  }

  const parsedData = parseMarketingAnalysis(analysis.marketing_analysis)
  const gutReaction = parsedData?.executive_briefing?.gut_reaction

  // Debug logging (can be removed in production)
  if (process.env.NODE_ENV === 'development') {
    console.log('CampaignCard Analysis Data:', {
      id: analysis.id,
      title: analysis.title,
      hasMarketingAnalysis: !!analysis.marketing_analysis,
      marketingAnalysisType: typeof analysis.marketing_analysis,
      marketingAnalysisLength: analysis.marketing_analysis?.length,
      parsedData: parsedData ? Object.keys(parsedData) : null,
      gutReaction: gutReaction
    })
  }

  // Theme-specific styling
  const getThemeClasses = () => {
    if (theme === "dark") {
      return {
        card: "bg-black border-gray-800",
        titleSection: "bg-black border-gray-700",
        title: "text-white group-hover:text-yellow-400",
        brand: "text-gray-300 group-hover:text-yellow-300",
        gutReaction: "text-white",
        button: "bg-yellow-500 hover:bg-yellow-400 text-black font-medium"
      };
    } else {
      return {
        card: "bg-white border-gray-200",
        titleSection: "bg-gray-50 border-gray-100", 
        title: "text-gray-800 group-hover:text-blue-600",
        brand: "text-blue-600 group-hover:text-blue-700",
        gutReaction: "text-gray-700",
        button: "bg-blue-600 hover:bg-blue-700 text-white"
      };
    }
  };

  // Variant-specific styling
  const getVariantClasses = () => {
    switch (variant) {
      case "trending":
        return "ring-2 ring-orange-200 hover:ring-orange-300";
      case "featured":
        return "ring-2 ring-yellow-200 hover:ring-yellow-300";
      default:
        return "";
    }
  };

  const themeStyles = getThemeClasses();

  return (
    <div className={`${cardSizes[size]} ${cardHeights[size]} ${themeStyles.card} rounded-lg shadow-sm border overflow-hidden transition-all duration-300 hover:shadow-lg hover:scale-105 flex flex-col ${getVariantClasses()}`}>
      {/* Title and Brand Section - Above Video */}
      <div className={`p-3 border-b ${themeStyles.titleSection}`}>
        <Link href={`/ad/${analysis.slug}`} className="group">
          <h3 className={`font-medium ${themeStyles.title} line-clamp-1 transition-colors leading-tight text-sm mb-1`}>
            {truncateText(analysis.title, 55)}
          </h3>
          <span className={`text-xs font-normal ${themeStyles.brand} truncate block`}>
            {truncateText(analysis.brand, 35)}
          </span>
        </Link>
      </div>

      {/* Video Section - 16:9 Aspect Ratio */}
      <Link href={`/ad/${analysis.slug}`} className="group block">
        <div className="relative aspect-video overflow-hidden bg-gray-100 flex-shrink-0">
          <Image
            src={analysis.thumbnail_url || analysis.video_thumbnail_url}
            alt={analysis.title}
            width={384}
            height={216}
            className="w-full h-full object-cover transition-transform duration-300 group-hover:scale-110"
            loading="lazy"
          />
          <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 transition-all duration-300 flex items-center justify-center">
            <Play className="w-8 h-8 text-white opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
          </div>
          
          <div className="absolute bottom-2 right-2 bg-black bg-opacity-75 text-white text-xs px-2 py-1 rounded">
            {analysis.duration_formatted}
          </div>
        </div>
      </Link>

      {/* Content Section - Gut Reaction and Button */}
      <div className={`p-3 flex-1 flex flex-col justify-between ${theme === "dark" ? "bg-black" : "bg-white"}`}>
        <div className="mb-3">
          {gutReaction ? (
            <p className={`text-xs ${themeStyles.gutReaction} line-clamp-5 leading-relaxed italic`}>
              &ldquo;{truncateText(gutReaction, 220)}&rdquo;
            </p>
          ) : (
            <div className="text-xs text-gray-500 italic">
              Analysis in progress...
            </div>
          )}
        </div>
        
        {/* Full Breakdown Button */}
        <Link href={`/ad/${analysis.slug}`} className="w-full">
          <Button 
            size="sm" 
            className={`w-full ${themeStyles.button} text-xs h-8`}
          >
            <Eye className="w-3 h-3 mr-1" />
            Full Breakdown
          </Button>
        </Link>
      </div>
    </div>
  )
}