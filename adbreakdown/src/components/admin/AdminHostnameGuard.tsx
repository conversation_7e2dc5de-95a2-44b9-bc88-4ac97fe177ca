import { redirect } from 'next/navigation'
import { headers } from 'next/headers'

interface AdminHostnameGuardProps {
  children: React.ReactNode
}

export default async function AdminHostnameGuard({ children }: AdminHostnameGuardProps) {
  const headersList = await headers()
  const host = headersList.get('host') || ''

  // If not on admin subdomain, redirect to main site
  if (!host.startsWith('admin.')) {
    redirect('/')
  }

  return <>{children}</>
}