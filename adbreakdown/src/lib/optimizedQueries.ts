import { createServerSupabaseClient } from '@/lib/supabase'

export interface OptimizedAnalysisData {
  id: string
  slug: string
  title: string
  inferred_brand: string
  youtube_url: string
  thumbnail_url: string
  duration_seconds: number
  overall_sentiment: number
  status: string
  is_public: boolean
  showcase?: boolean
  created_at: string
  updated_at: string
  
  // Core analysis fields
  transcript?: string
  summary?: string
  marketing_analysis?: string
  
  // Computed fields
  video_url: string
  video_title: string
  video_thumbnail_url: string
  video_duration: number
}

export class OptimizedAnalysisQueries {
  private static supabase = createServerSupabaseClient()

  /**
   * Fast basic query - only essential fields for above-the-fold content
   */
  static async getBasicAnalysis(id: string): Promise<OptimizedAnalysisData | null> {
    const isUUID = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i.test(id)
    
    const { data, error } = await this.supabase
      .from('ad_analyses')
      .select(`
        id,
        slug,
        title,
        inferred_brand,
        youtube_url,
        thumbnail_url,
        duration_seconds,
        overall_sentiment,
        status,
        is_public,
        created_at,
        updated_at
      `)
      .eq(isUUID ? 'id' : 'slug', id)
      .eq('is_public', true)
      .single()

    if (error || !data) return null

    return {
      ...data,
      video_url: data.youtube_url,
      video_title: data.title,
      video_thumbnail_url: data.thumbnail_url,
      video_duration: data.duration_seconds
    }
  }

  /**
   * Optimized full analysis query with selective field loading
   */
  static async getFullAnalysis(id: string, includeReports = true): Promise<any | null> {
    const isUUID = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i.test(id)
    
    // Base analysis query
    const analysisQuery = this.supabase
      .from('ad_analyses')
      .select('*')
      .eq(isUUID ? 'id' : 'slug', id)
      .eq('is_public', true)
      .single()

    if (!includeReports) {
      const { data, error } = await analysisQuery
      return error ? null : data
    }

    // Get analysis first to get the actual UUID
    const analysisResult = await analysisQuery
    
    if (analysisResult.error || !analysisResult.data) return null

    // Then get reports using the analysis UUID
    const reportsResult = await this.supabase
      .from('analysis_reports')
      .select(`
        id,
        report_type_id,
        status,
        content,
        generated_at,
        created_at,
        report_types (
          name,
          description
        )
      `)
      .eq('analysis_id', analysisResult.data.id)
      .eq('status', 'generated')

    return {
      ...analysisResult.data,
      reports: reportsResult.data || []
    }
  }

  /**
   * Get public analyses list with pagination, search, and filters
   */
  static async getPublicAnalysesList(page = 1, limit = 10, options: {
    search?: string
    sort?: 'recent' | 'popular'
    brand?: string
    celebrity?: string
    year?: string
    duration?: string
    productCategory?: string
    campaignCategory?: string
    geography?: string
    agency?: string
    collectionTags?: string
  } = {}) {
    const offset = (page - 1) * limit
    const { search, sort = 'recent', brand, celebrity, year, duration, productCategory, campaignCategory, geography, agency, collectionTags } = options

    let query = this.supabase
      .from('ad_analyses')
      .select(`
        id,
        slug,
        title,
        brand,
        inferred_brand,
        youtube_url,
        thumbnail_url,
        duration_seconds,
        is_public,
        created_at,
        gut_reaction
      `, { count: 'exact' })
      .eq('is_public', true)
      .eq('status', 'completed')

    // Apply search filter
    if (search && search.trim()) {
      query = query.or(`title.ilike.%${search}%,brand.ilike.%${search}%`)
    }

    // Apply brand filter
    if (brand && brand.trim() && brand !== 'all') {
      query = query.eq('brand', brand)
    }

    // Apply product category filter
    if (productCategory && productCategory.trim() && productCategory !== 'all') {
      query = query.eq('product_category', productCategory)
    }

    // Apply campaign category filter  
    if (campaignCategory && campaignCategory.trim() && campaignCategory !== 'all') {
      query = query.eq('campaign_category', campaignCategory)
    }

    // Apply geography filter
    if (geography && geography.trim() && geography !== 'all') {
      query = query.eq('geography', geography)
    }

    // Apply agency filter
    if (agency && agency.trim() && agency !== 'all') {
      query = query.eq('agency', agency)
    }

    // Apply celebrity filter
    if (celebrity && celebrity.trim() && celebrity !== 'all') {
      query = query.eq('celebrity', celebrity)
    }

    // Apply year filter
    if (year && year.trim() && year !== 'all') {
      const startDate = `${year}-01-01`
      const endDate = `${year}-12-31`
      query = query.gte('launch_date', startDate).lte('launch_date', endDate)
    }

    // Apply duration filter
    if (duration && duration.trim() && duration !== 'all') {
      switch (duration) {
        case 'short': // 0-30 seconds
          query = query.lte('duration_seconds', 30)
          break
        case 'medium': // 31-60 seconds
          query = query.gte('duration_seconds', 31).lte('duration_seconds', 60)
          break
        case 'long': // 61+ seconds
          query = query.gte('duration_seconds', 61)
          break
      }
    }

    // Apply collection tags filter
    if (collectionTags && collectionTags.trim() && collectionTags !== 'all') {
      query = query.contains('collection_tags', [collectionTags.trim()])
    }

    // Apply sorting
    switch (sort) {
      case 'popular':
        // TODO: Add view_count column to database, fallback to recent for now
        query = query.order('created_at', { ascending: false })
        break
      default: // recent
        query = query.order('created_at', { ascending: false })
        break
    }

    // Apply pagination
    query = query.range(offset, offset + limit - 1)

    const { data, error, count } = await query

    if (error) return null

    return {
      analyses: data || [],
      pagination: {
        page,
        limit,
        total: count || 0,
        totalPages: Math.ceil((count || 0) / limit),
        hasNext: page < Math.ceil((count || 0) / limit),
        hasPrev: page > 1
      }
    }
  }

  /**
   * Get all unique brands from public analyses for filter dropdown
   */
  static async getAvailableBrands(): Promise<string[]> {
    const { data, error } = await this.supabase
      .from('ad_analyses')
      .select('brand')
      .eq('is_public', true)
      .eq('status', 'completed')
      .not('brand', 'is', null)
      .order('brand')

    if (error || !data) return []

    // Get unique brands and filter out empty strings
    const brands = [...new Set(data.map(item => item.brand?.trim()).filter(Boolean))]
    return brands.sort()
  }

  /**
   * Get all unique celebrities from public analyses for filter dropdown
   */
  static async getAvailableCelebrities(): Promise<string[]> {
    const { data, error } = await this.supabase
      .from('ad_analyses')
      .select('celebrity')
      .eq('is_public', true)
      .eq('status', 'completed')
      .not('celebrity', 'is', null)
      .order('celebrity')

    if (error || !data) return []

    // Get unique celebrities and filter out empty strings
    const celebrities = [...new Set(data.map(item => item.celebrity?.trim()).filter(Boolean))]
    return celebrities.sort()
  }

  /**
   * Get all unique years from public analyses for filter dropdown
   */
  static async getAvailableYears(): Promise<string[]> {
    const { data, error } = await this.supabase
      .from('ad_analyses')
      .select('launch_date')
      .eq('is_public', true)
      .eq('status', 'completed')
      .not('launch_date', 'is', null)
      .order('launch_date')

    if (error || !data) return []

    // Get unique years and filter out null dates
    const years = [...new Set(data.map(item => {
      if (item.launch_date) {
        return new Date(item.launch_date).getFullYear().toString()
      }
      return null
    }).filter(Boolean) as string[])]
    
    return years.sort().reverse() // Most recent first
  }
}