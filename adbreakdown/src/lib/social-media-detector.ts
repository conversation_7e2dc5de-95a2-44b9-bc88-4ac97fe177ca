/**
 * Social Media URL Detection and Validation
 */

export type SocialMediaPlatform = 'youtube' | 'instagram' | 'facebook' | 'unknown'

export interface SocialMediaUrlInfo {
  platform: SocialMediaPlatform
  isValid: boolean
  url: string
  videoId?: string
  error?: string
}

/**
 * Detects the platform from a given URL
 */
export function detectSocialMediaPlatform(url: string): SocialMediaPlatform {
  if (!url) return 'unknown'
  
  // Normalize URL
  const normalizedUrl = url.toLowerCase().trim()
  
  // YouTube detection
  if (normalizedUrl.includes('youtube.com') || normalizedUrl.includes('youtu.be')) {
    return 'youtube'
  }
  
  // Instagram detection
  if (normalizedUrl.includes('instagram.com')) {
    return 'instagram'
  }
  
  // Facebook detection
  if (normalizedUrl.includes('facebook.com') || normalizedUrl.includes('fb.watch')) {
    return 'facebook'
  }
  
  return 'unknown'
}

/**
 * Validates YouTube URLs
 */
export function validateYouTubeUrl(url: string): { isValid: boolean; videoId?: string; error?: string } {
  const youtubeRegex = /^(https?:\/\/)?(www\.)?(youtube\.com\/(watch\?v=|shorts\/)|youtu\.be\/)([\w-]+)/
  const match = url.match(youtubeRegex)
  
  if (!match) {
    return { isValid: false, error: 'Invalid YouTube URL format' }
  }
  
  const videoId = match[5]
  if (!videoId || videoId.length !== 11) {
    return { isValid: false, error: 'Invalid YouTube video ID' }
  }
  
  return { isValid: true, videoId }
}

/**
 * Validates Instagram URLs
 */
export function validateInstagramUrl(url: string): { isValid: boolean; videoId?: string; error?: string } {
  // Instagram post/reel/tv formats
  const instagramRegex = /^https?:\/\/(www\.)?instagram\.com\/(p|reel|tv)\/([\w-]+)/
  const match = url.match(instagramRegex)
  
  if (!match) {
    return { isValid: false, error: 'Invalid Instagram URL format. Supported formats: /p/, /reel/, /tv/' }
  }
  
  const videoId = match[3]
  if (!videoId) {
    return { isValid: false, error: 'Could not extract Instagram post ID' }
  }
  
  return { isValid: true, videoId }
}

/**
 * Validates Facebook URLs
 */
export function validateFacebookUrl(url: string): { isValid: boolean; videoId?: string; error?: string } {
  // Facebook video formats
  const facebookVideoRegex = /^https?:\/\/(www\.)?facebook\.com\/.*\/videos\/(\d+)/
  const fbWatchRegex = /^https?:\/\/(www\.)?fb\.watch\/([\w-]+)/
  
  let match = url.match(facebookVideoRegex)
  if (match) {
    const videoId = match[2]
    return { isValid: true, videoId }
  }
  
  match = url.match(fbWatchRegex)
  if (match) {
    const videoId = match[2]
    return { isValid: true, videoId }
  }
  
  return { isValid: false, error: 'Invalid Facebook URL format. Supported formats: facebook.com/.../videos/ID or fb.watch/ID' }
}

/**
 * Main function to validate any social media URL
 */
export function validateSocialMediaUrl(url: string): SocialMediaUrlInfo {
  if (!url || typeof url !== 'string') {
    return {
      platform: 'unknown',
      isValid: false,
      url: url || '',
      error: 'URL is required'
    }
  }
  
  const platform = detectSocialMediaPlatform(url)
  
  switch (platform) {
    case 'youtube':
      const youtubeValidation = validateYouTubeUrl(url)
      return {
        platform,
        isValid: youtubeValidation.isValid,
        url,
        videoId: youtubeValidation.videoId,
        error: youtubeValidation.error
      }
      
    case 'instagram':
      const instagramValidation = validateInstagramUrl(url)
      return {
        platform,
        isValid: instagramValidation.isValid,
        url,
        // Don't extract videoId for Instagram - pass original URL directly to yt-dlp
        videoId: undefined,
        error: instagramValidation.error
      }
      
    case 'facebook':
      const facebookValidation = validateFacebookUrl(url)
      return {
        platform,
        isValid: facebookValidation.isValid,
        url,
        // Don't extract videoId for Facebook - pass original URL directly to yt-dlp
        videoId: undefined,
        error: facebookValidation.error
      }
      
    default:
      return {
        platform: 'unknown',
        isValid: false,
        url,
        error: 'Unsupported platform. Please use YouTube, Instagram, or Facebook URLs.'
      }
  }
}

/**
 * Check if URL is a supported social media platform
 */
export function isSupportedSocialMediaUrl(url: string): boolean {
  const platform = detectSocialMediaPlatform(url)
  return platform !== 'unknown'
}

/**
 * Get processing strategy for social media platforms
 */
export function getSocialMediaProcessingStrategy(platform: SocialMediaPlatform) {
  switch (platform) {
    case 'youtube':
      // YouTube has complex privacy handling - handled elsewhere
      return null
      
    case 'instagram':
    case 'facebook':
      // Instagram and Facebook always require download since they're not directly accessible by Vertex AI
      return {
        strategy: 'download_and_upload',
        reason: `${platform.charAt(0).toUpperCase() + platform.slice(1)} videos require download for analysis`
      }
      
    default:
      return null
  }
}