/**
 * Metadata Validation and Normalization Utilities
 * 
 * These utilities ensure data consistency before saving to the database.
 * They normalize brand names, convert invalid values to null, and standardize formats.
 */

// Brand name mappings - standardize variations
const BRAND_MAPPINGS: Record<string, string> = {
  'Fenty Beauty / Fenty Skin': 'Fenty Beauty',
  'fenty beauty': 'Fenty Beauty',
  'FENTY BEAUTY': 'Fenty Beauty',
  'Apple Inc': 'Apple',
  'apple': 'Apple',
  'APPLE': 'Apple',
  'Google LLC': 'Google',
  'google': 'Google',
  'GOOGLE': 'Google',
  'Nike Inc': 'Nike',
  'nike': 'Nike',
  'NIKE': 'Nike',
  'McDonald\'s Corporation': 'McDonald\'s',
  'mcdonalds': '<PERSON>\'s',
  'MCDONALDS': 'McDonald\'s',
  'Coca Cola': 'Coca-Cola',
  'coca cola': 'Coca-Cola',
  'COCA COLA': 'Coca-Cola',
  'Pepsi Co': 'Pepsi',
  'pepsico': 'Pepsi',
  'PEPSICO': 'Pepsi',
}

// Geographic standardization
const GEOGRAPHY_MAPPINGS: Record<string, string> = {
  'usa': 'United States',
  'US': 'United States',
  'america': 'United States',
  'united states of america': 'United States',
  'uk': 'United Kingdom',
  'britain': 'United Kingdom',
  'england': 'United Kingdom',
  'great britain': 'United Kingdom',
  'india': 'India',
  'INDIA': 'India',
  'bharat': 'India',
  'china': 'China',
  'CHINA': 'China',
  'japan': 'Japan',
  'JAPAN': 'Japan',
  'germany': 'Germany',
  'GERMANY': 'Germany',
  'france': 'France',
  'FRANCE': 'France',
  'canada': 'Canada',
  'CANADA': 'Canada',
  'australia': 'Australia',
  'AUSTRALIA': 'Australia',
}

// Values that should be converted to NULL
const NULL_VALUES = [
  'Unknown',
  'unknown',
  'UNKNOWN',
  'N/A',
  'n/a',
  'NA',
  'na',
  'Not Available',
  'not available',
  'Not known',
  'not known',
  'TBD',
  'tbd',
  'To be determined',
  '',
  ' ',
  '  ',
  'None',
  'none',
  'NONE',
  'null',
  'NULL',
  'undefined',
  'Not specified',
  'not specified',
  'Not mentioned',
  'not mentioned',
  '—',
  '-',
  '--',
]

// Standard campaign categories
const STANDARD_CAMPAIGN_CATEGORIES = [
  'Brand Building',
  'Brand Awareness',
  'Performance Marketing',
  'Direct Response',
  'Market Launch',
  'Product Launch',
  'Brand Repositioning',
  'Seasonal Campaign',
  'Event Marketing',
  'Influencer Marketing',
  'Social Media Campaign',
  'Digital Campaign',
  'Traditional Media',
  'Integrated Campaign',
  'Awareness Campaign',
  'Conversion Campaign',
  'Retention Campaign',
  'Acquisition Campaign',
]

export interface MetadataValidationResult {
  isValid: boolean
  cleanedMetadata: any
  errors: string[]
  warnings: string[]
}

/**
 * Clean a string value - convert null values to null, apply mappings
 */
export function cleanStringValue(
  value: any, 
  mappings: Record<string, string> = {}
): string | null {
  if (value === null || value === undefined) return null
  if (typeof value !== 'string') return null
  
  const trimmed = value.trim()
  if (NULL_VALUES.includes(trimmed)) return null
  if (trimmed.length === 0) return null
  
  // Apply mappings if provided
  if (mappings[trimmed]) return mappings[trimmed]
  
  return trimmed
}

/**
 * Convert slash-separated or comma-separated categories to arrays
 */
export function normalizeCategoryArray(category: any): string[] | null {
  if (!category) return null
  
  // If already an array, clean each item
  if (Array.isArray(category)) {
    const cleaned = category
      .map(item => cleanStringValue(item))
      .filter((item): item is string => item !== null)
    return cleaned.length > 0 ? cleaned : null
  }
  
  // If string, split by separators
  if (typeof category === 'string') {
    const cleaned = cleanStringValue(category)
    if (!cleaned) return null
    
    // Split by / or , and clean each part
    const parts = cleaned
      .split(/[\/,]/)
      .map(part => part.trim())
      .filter(part => part && !NULL_VALUES.includes(part))
      .map(part => {
        // Try to match to standard categories
        const standardMatch = STANDARD_CAMPAIGN_CATEGORIES.find(
          std => std.toLowerCase() === part.toLowerCase()
        )
        return standardMatch || part
      })
    
    return parts.length > 0 ? parts : null
  }
  
  return null
}

/**
 * Normalize celebrity names to arrays and extract context
 * Handles cases where context exists without actual celebrity presence
 * Also handles malformed array-like strings
 */
export function normalizeCelebrityArray(celebrity: any): { names: string[] | null; context: string | null } {
  if (!celebrity) return { names: null, context: null }
  
  // If already an array, clean each item
  if (Array.isArray(celebrity)) {
    const cleaned = celebrity
      .map(item => cleanStringValue(item))
      .filter((item): item is string => item !== null)
    return { names: cleaned.length > 0 ? cleaned : null, context: null }
  }
  
  // If string, parse for names and context
  if (typeof celebrity === 'string') {
    const cleaned = cleanStringValue(celebrity)
    if (!cleaned) return { names: null, context: null }
    
    // Handle malformed array-like strings: ["Name1, Name2, Name3"]
    const arrayLikeMatch = cleaned.match(/^\["([^"]+)"\]$/)
    if (arrayLikeMatch) {
      const namesString = arrayLikeMatch[1]
      const names = namesString
        .split(',')
        .map(name => name.trim())
        .filter(name => name && !NULL_VALUES.includes(name))
      
      return {
        names: names.length > 0 ? names : null,
        context: null
      }
    }
    
    // Extract context from parentheses - e.g., "Rajinikanth (caricature)"
    const contextMatch = cleaned.match(/^(.+?)\s*\(([^)]+)\)$/)
    if (contextMatch) {
      const [, namesStr, context] = contextMatch
      const contextStr = context.trim()
      
      // Check if this is a reference-only context (not actual celebrity presence)
      const referenceOnlyContexts = [
        'caricature', 'by reference', 'mentioned', 'animation', 'resemblance',
        'referenced', 'parody', 'look-alike', 'cartoon', 'animated'
      ]
      
      // Contexts that indicate ACTUAL celebrity presence (even if in specific roles)
      const actualPresenceContexts = [
        'brand founder', 'voice only', 'archival footage', 'as the', 'as his', 'as her',
        'character', 'spokesperson', 'endorser', 'cameo', 'guest appearance'
      ]
      
      const isReferenceOnly = referenceOnlyContexts.some(ref => 
        contextStr.toLowerCase().includes(ref.toLowerCase())
      )
      
      // Check if context explicitly indicates actual presence
      const isActualPresence = actualPresenceContexts.some(presence => 
        contextStr.toLowerCase().includes(presence.toLowerCase())
      )
      
      if (isReferenceOnly && !isActualPresence) {
        // This is just a reference, not actual celebrity presence
        return {
          names: null,
          context: `${namesStr.trim()} (${contextStr})`
        }
      } else {
        // This is actual celebrity presence with context
        const names = namesStr
          .split(/[,&]/)
          .map(name => name.trim())
          .filter(name => name && !NULL_VALUES.includes(name))
        
        return {
          names: names.length > 0 ? names : null,
          context: contextStr || null
        }
      }
    }
    
    // No context, just split names (assume actual presence)
    const names = cleaned
      .split(/[,&]/)
      .map(name => name.trim())
      .filter(name => name && !NULL_VALUES.includes(name))
    
    return {
      names: names.length > 0 ? names : null,
      context: null
    }
  }
  
  return { names: null, context: null }
}

/**
 * Validate runtime format (M:SS or MM:SS)
 */
export function validateRuntime(runtime: any): string | null {
  const cleaned = cleanStringValue(runtime)
  if (!cleaned) return null
  
  // Check if it matches M:SS or MM:SS format
  const runtimeRegex = /^\d{1,2}:\d{2}$/
  if (runtimeRegex.test(cleaned)) {
    return cleaned
  }
  
  // Try to extract seconds and convert
  const secondsMatch = cleaned.match(/(\d+)\s*(?:seconds?|secs?|s)/i)
  if (secondsMatch) {
    const totalSeconds = parseInt(secondsMatch[1])
    const minutes = Math.floor(totalSeconds / 60)
    const seconds = totalSeconds % 60
    return `${minutes}:${seconds.toString().padStart(2, '0')}`
  }
  
  return null
}

/**
 * Validate and normalize campaign launch date
 */
export function validateCampaignDate(date: any): string | null {
  const cleaned = cleanStringValue(date)
  if (!cleaned) return null
  
  // Try to parse various date formats
  const dateRegex = /^(\d{4})(?:-(\d{1,2})(?:-(\d{1,2}))?)?$/
  const match = cleaned.match(dateRegex)
  
  if (match) {
    const [, year, month, day] = match
    if (month && day) {
      return `${year}-${month.padStart(2, '0')}-${day.padStart(2, '0')}`
    } else if (month) {
      return `${year}-${month.padStart(2, '0')}`
    } else {
      return year
    }
  }
  
  return null
}

/**
 * Main metadata validation and cleaning function
 */
export function validateAndCleanMetadata(metadata: any): MetadataValidationResult {
  const result: MetadataValidationResult = {
    isValid: true,
    cleanedMetadata: {},
    errors: [],
    warnings: []
  }
  
  if (!metadata || typeof metadata !== 'object') {
    result.isValid = false
    result.errors.push('Metadata must be an object')
    return result
  }
  
  try {
    const cleaned: any = {}
    
    // Clean ad_title
    cleaned.ad_title = cleanStringValue(metadata.ad_title)
    if (!cleaned.ad_title) {
      result.warnings.push('Ad title is missing or invalid')
    }
    
    // Clean brand (required field)
    cleaned.brand = cleanStringValue(metadata.brand, BRAND_MAPPINGS)
    if (!cleaned.brand) {
      result.errors.push('Brand name is required')
      result.isValid = false
    }
    
    // Clean product_category
    cleaned.product_category = cleanStringValue(metadata.product_category)
    
    // Clean parent_entity
    cleaned.parent_entity = cleanStringValue(metadata.parent_entity)
    
    // Clean campaign_category (convert to array)
    cleaned.campaign_category = normalizeCategoryArray(metadata.campaign_category)
    cleaned.campaign_categories = normalizeCategoryArray(metadata.campaign_category || metadata.campaign_categories)
    
    // Clean runtime
    cleaned.runtime = validateRuntime(metadata.runtime)
    
    // Clean celebrity (legacy and new fields)
    const celebrityResult = normalizeCelebrityArray(metadata.celebrity_names || metadata.celebrity)
    cleaned.celebrity_names = celebrityResult.names
    cleaned.celebrity_context = celebrityResult.context || cleanStringValue(metadata.celebrity_context)
    cleaned.celebrity = cleanStringValue(metadata.celebrity) // Keep for backward compatibility
    
    // Clean geography
    cleaned.geography = cleanStringValue(metadata.geography, GEOGRAPHY_MAPPINGS)
    
    // Clean agency
    cleaned.agency = cleanStringValue(metadata.agency)
    
    // Clean director
    cleaned.director = cleanStringValue(metadata.director)
    
    // Clean creative_team
    cleaned.creative_team = cleanStringValue(metadata.creative_team)
    
    // Clean production_company
    cleaned.production_company = cleanStringValue(metadata.production_company)
    
    // Clean music_composer
    cleaned.music_composer = cleanStringValue(metadata.music_composer)
    
    // Clean sound_designer
    cleaned.sound_designer = cleanStringValue(metadata.sound_designer)
    
    // Clean campaign_launch_date
    cleaned.campaign_launch_date = validateCampaignDate(metadata.campaign_launch_date)
    
    result.cleanedMetadata = cleaned
    
    // Additional validations
    if (cleaned.runtime && !cleaned.runtime.match(/^\d{1,2}:\d{2}$/)) {
      result.warnings.push('Runtime format should be M:SS or MM:SS')
    }
    
    if (cleaned.campaign_launch_date && !cleaned.campaign_launch_date.match(/^\d{4}(-\d{2}(-\d{2})?)?$/)) {
      result.warnings.push('Campaign launch date format should be YYYY, YYYY-MM, or YYYY-MM-DD')
    }
    
    return result
    
  } catch (error) {
    result.isValid = false
    result.errors.push(`Validation error: ${error instanceof Error ? error.message : 'Unknown error'}`)
    return result
  }
}

/**
 * Validate and clean marketing analysis JSON
 */
export function validateMarketingAnalysis(marketingAnalysis: any): MetadataValidationResult {
  const result: MetadataValidationResult = {
    isValid: true,
    cleanedMetadata: marketingAnalysis,
    errors: [],
    warnings: []
  }
  
  if (!marketingAnalysis) {
    result.errors.push('Marketing analysis is required')
    result.isValid = false
    return result
  }
  
  try {
    let parsed = marketingAnalysis
    
    // If it's a string, try to parse it
    if (typeof marketingAnalysis === 'string') {
      let cleanData = marketingAnalysis
        .replace(/```json\s*/g, '')
        .replace(/```\s*/g, '')
        .replace(/^\s*```.*$/gm, '')
        .trim()
      
      const firstBrace = cleanData.indexOf('{')
      const lastBrace = cleanData.lastIndexOf('}')
      
      if (firstBrace !== -1 && lastBrace !== -1 && lastBrace > firstBrace) {
        cleanData = cleanData.substring(firstBrace, lastBrace + 1)
      }
      
      parsed = JSON.parse(cleanData)
    }
    
    // Validate structure
    if (!parsed.metadata) {
      result.errors.push('Marketing analysis must contain metadata')
      result.isValid = false
    }
    
    if (!parsed.executive_briefing) {
      result.errors.push('Marketing analysis must contain executive_briefing')
      result.isValid = false
    }
    
    // Clean metadata within marketing analysis
    if (parsed.metadata) {
      const metadataValidation = validateAndCleanMetadata(parsed.metadata)
      parsed.metadata = metadataValidation.cleanedMetadata
      
      // Merge validation results
      result.errors.push(...metadataValidation.errors)
      result.warnings.push(...metadataValidation.warnings)
      result.isValid = result.isValid && metadataValidation.isValid
    }
    
    result.cleanedMetadata = parsed
    
    return result
    
  } catch (error) {
    result.isValid = false
    result.errors.push(`JSON parsing error: ${error instanceof Error ? error.message : 'Invalid JSON'}`)
    return result
  }
}

/**
 * Extract and clean top-level fields from marketing analysis
 */
export function extractTopLevelFields(marketingAnalysis: any): {
  brand?: string | null
  campaign_category?: string | null
  product_category?: string | null
  geography?: string | null
  celebrity?: string | null
  agency?: string | null
} {
  try {
    const validation = validateMarketingAnalysis(marketingAnalysis)
    if (!validation.isValid || !validation.cleanedMetadata.metadata) {
      return {}
    }
    
    const metadata = validation.cleanedMetadata.metadata
    
    return {
      brand: metadata.brand,
      campaign_category: Array.isArray(metadata.campaign_category) 
        ? metadata.campaign_category.join(', ') 
        : metadata.campaign_category,
      product_category: metadata.product_category,
      geography: metadata.geography,
      celebrity: metadata.celebrity,
      agency: metadata.agency,
    }
  } catch (error) {
    console.error('Error extracting top-level fields:', error)
    return {}
  }
}