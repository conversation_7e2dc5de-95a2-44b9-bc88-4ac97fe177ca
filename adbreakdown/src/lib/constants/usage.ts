// Subscription configuration
export const SUBSCRIPTION_TIERS = {
  FREE: 'free',
  PRO: 'pro',
  ENTERPRISE: 'enterprise'
} as const;

// Helper to check if user is on paid plan
export const isPaidPlan = (subscriptionStatus: string | null): boolean => {
  if (!subscriptionStatus) return false;
  return subscriptionStatus !== 'free' && subscriptionStatus !== 'cancelled';
};

// NOTE: Usage limits are now handled by the credit system
// Analysis restrictions: handled by credits_remaining in profiles table  
// Library view limits: tracked in library_views_count but not restricted (yet)
// Studio access: controlled by subscription status via isPaidPlan()