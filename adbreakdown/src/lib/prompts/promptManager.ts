import { createServerSupabaseClient } from '@/lib/supabase'

interface PromptTemplate {
  id: string
  name: string
  content: string
  version: number
  is_active: boolean
  created_at: string
  updated_at: string
}

class PromptManager {
  private static cache: Map<string, PromptTemplate> = new Map()
  private static cacheTimeout: Map<string, number> = new Map()
  private static CACHE_DURATION = 5 * 60 * 1000 // 5 minutes

  static async getPrompt(name: string, variables: Record<string, string> = {}): Promise<string> {
    // Development logging
    if (process.env.NODE_ENV === 'development') {
      console.log('\n🔍 PROMPT MANAGER: Getting prompt', {
        name,
        variables,
        cacheSize: this.cache.size
      })
    }
    
    try {
      // Check cache first
      const cached = this.getCachedPrompt(name)
      if (cached) {
        if (process.env.NODE_ENV === 'development') {
          console.log('⚡ PROMPT MANAGER: Using cached prompt from Supabase', {
            name,
            version: cached.version,
            contentLength: cached.content.length
          })
        }
        return this.interpolateVariables(cached.content, variables)
      }

      // Fetch from database
      if (process.env.NODE_ENV === 'development') {
        console.log('🗄️ PROMPT MANAGER: Fetching from Supabase database...')
      }
      
      const supabase = createServerSupabaseClient()
      const { data: prompt, error } = await supabase
        .from('prompts')
        .select('*')
        .eq('name', name)
        .eq('is_active', true)
        .single()

      if (error || !prompt) {
        if (process.env.NODE_ENV === 'development') {
          console.warn('❌ PROMPT MANAGER: Database fetch failed, using fallback', {
            name,
            error: error?.message || 'No data',
            promptExists: !!prompt
          })
        }
        console.error(`Failed to fetch prompt ${name}:`, error)
        return this.getFallbackPrompt(name, variables)
      }
      
      if (process.env.NODE_ENV === 'development') {
        console.log('✅ PROMPT MANAGER: Successfully fetched from Supabase!', {
          name,
          version: prompt.version,
          contentLength: prompt.content.length,
          isActive: prompt.is_active,
          contentPreview: prompt.content.substring(0, 100) + '...'
        })
      }

      // Cache the result
      this.cachePrompt(name, prompt)
      
      const result = this.interpolateVariables(prompt.content, variables)
      
      if (process.env.NODE_ENV === 'development') {
        console.log('💾 PROMPT MANAGER: Cached and processed content', {
          name,
          originalLength: prompt.content.length,
          processedLength: result.length,
          variablesApplied: Object.keys(variables).length,
          variables
        })
      }
      
      return result
    } catch (error) {
      if (process.env.NODE_ENV === 'development') {
        console.warn('⚠️ PROMPT MANAGER: Exception occurred, falling back to local files', {
          name,
          error: error instanceof Error ? error.message : String(error)
        })
      }
      console.error(`Error fetching prompt ${name}:`, error)
      return this.getFallbackPrompt(name, variables)
    }
  }

  private static getCachedPrompt(name: string): PromptTemplate | null {
    const cached = this.cache.get(name)
    const cacheTime = this.cacheTimeout.get(name)
    
    if (cached && cacheTime && Date.now() - cacheTime < this.CACHE_DURATION) {
      return cached
    }
    
    // Clear expired cache
    this.cache.delete(name)
    this.cacheTimeout.delete(name)
    return null
  }

  private static cachePrompt(name: string, prompt: PromptTemplate): void {
    this.cache.set(name, prompt)
    this.cacheTimeout.set(name, Date.now())
  }

  private static interpolateVariables(content: string, variables: Record<string, string>): string {
    let result = content
    for (const [key, value] of Object.entries(variables)) {
      const regex = new RegExp(`\\{\\{${key}\\}\\}`, 'g')
      result = result.replace(regex, value)
    }
    return result
  }

  private static getFallbackPrompt(name: string, variables: Record<string, string>): string {
    if (process.env.NODE_ENV === 'development') {
      console.log('📋 PROMPT MANAGER: Using fallback from /lib/prompts/', {
        name,
        variables
      })
    }
    console.warn(`Using fallback prompt for ${name}`)
    
    // Import the original prompt as fallback
    if (name === 'marketing_analysis_prompt') {
      const { getMarketingAnalysisPrompt } = require('./marketingAnalysisPrompt')
      return getMarketingAnalysisPrompt(variables.videoUrl || '')
    }
    
    return `Fallback prompt for ${name}. Please update the prompts table.`
  }

  // Admin method to update prompts
  static async updatePrompt(name: string, content: string, description?: string): Promise<boolean> {
    try {
      const supabase = createServerSupabaseClient()
      
      // Check if prompt exists
      const { data: existing } = await supabase
        .from('prompts')
        .select('version')
        .eq('name', name)
        .single()

      const newVersion = existing ? existing.version + 1 : 1

      let error
      if (existing) {
        // Update existing prompt
        const result = await supabase
          .from('prompts')
          .update({
            content,
            description,
            version: newVersion,
            is_active: true,
            updated_at: new Date().toISOString()
          })
          .eq('name', name)
        error = result.error
      } else {
        // Insert new prompt
        const result = await supabase
          .from('prompts')
          .insert({
            name,
            content,
            description,
            version: newVersion,
            is_active: true,
            updated_at: new Date().toISOString()
          })
        error = result.error
      }

      if (error) {
        console.error(`Failed to update prompt ${name}:`, error)
        return false
      }

      // Clear cache
      this.cache.delete(name)
      this.cacheTimeout.delete(name)
      
      return true
    } catch (error) {
      console.error(`Error updating prompt ${name}:`, error)
      return false
    }
  }

  // Clear all caches (useful for testing)
  static clearCache(): void {
    this.cache.clear()
    this.cacheTimeout.clear()
  }
}

export default PromptManager

// Helper function to maintain backward compatibility
export const getMarketingAnalysisPrompt = async (videoUrl: string): Promise<string> => {
  return await PromptManager.getPrompt('marketing_analysis_prompt', { videoUrl })
}