import { createServerSupabaseClient } from '@/lib/supabase';
import { isPaidPlan } from '@/lib/constants/usage';

export interface UserUsage {
  analysisCount: number;
  libraryViewsCount: number;
  prelaunchAnalysisCount: number;
  subscriptionStatus: string | null;
  usageResetDate: string;
  creditsRemaining: number;
}

// Removed UsageCheckResult - no longer needed since we don't do restrictions

export class UsageService {
  private supabase = createServerSupabaseClient();

  // Get internal user ID from Clerk ID
  private async getInternalUserId(clerkUserId: string): Promise<string | null> {
    const { data: user, error } = await this.supabase
      .from('users')
      .select('id')
      .eq('clerk_id', clerkUserId)
      .single();

    if (error || !user) {
      console.error('User not found for Clerk ID:', clerkUserId, error);
      return null;
    }

    return user.id;
  }

  // Get user's current usage stats (for analytics/reference only)
  async getUserUsage(clerkUserId: string): Promise<UserUsage | null> {
    const internalUserId = await this.getInternalUserId(clerkUserId);
    if (!internalUserId) return null;

    const { data: profile, error } = await this.supabase
      .from('profiles')
      .select(`
        analysis_count,
        library_views_count,
        prelaunch_analysis_count,
        subscription_status,
        usage_reset_date,
        credits_remaining
      `)
      .eq('user_id', internalUserId)
      .single();

    if (error) {
      console.error('Error fetching user usage:', error);
      return null;
    }

    return {
      analysisCount: profile?.analysis_count || 0,
      libraryViewsCount: profile?.library_views_count || 0,
      prelaunchAnalysisCount: profile?.prelaunch_analysis_count || 0,
      subscriptionStatus: profile?.subscription_status || 'free',
      usageResetDate: profile?.usage_reset_date,
      creditsRemaining: profile?.credits_remaining || 0
    };
  }

  // REMOVED: Analysis restriction checks - credit system handles this
  // UsageService is now only for tracking analytics, not restrictions

  // Check if user has studio access (based on waitlist approval only)
  async hasStudioAccess(clerkUserId: string): Promise<boolean> {
    return await this.isApprovedForStudio(clerkUserId);
  }

  // Increment analysis count
  async incrementAnalysisCount(clerkUserId: string): Promise<boolean> {
    const internalUserId = await this.getInternalUserId(clerkUserId);
    if (!internalUserId) return false;

    const { data, error } = await this.supabase.rpc('increment_analysis_count', {
      p_user_id: internalUserId,
    });

    if (error) {
      console.error('Error incrementing analysis count:', error);
      return false;
    }

    // RPC returns the new count on success, or null if the user wasn't found.
    return data !== null;
  }

  // Increment library views count
  async incrementLibraryViewsCount(clerkUserId: string): Promise<boolean> {
    const internalUserId = await this.getInternalUserId(clerkUserId);
    if (!internalUserId) return false;

    const { data, error } = await this.supabase.rpc('increment_library_views_count', {
      p_user_id: internalUserId,
    });

    if (error) {
      console.error('Error incrementing library views count:', error);
      return false;
    }

    // RPC returns the new count on success, or null if the user wasn't found.
    return data !== null;
  }

  // Increment pre-launch analysis count
  async incrementPrelaunchAnalysisCount(clerkUserId: string): Promise<boolean> {
    const internalUserId = await this.getInternalUserId(clerkUserId);
    if (!internalUserId) return false;

    const { data, error } = await this.supabase.rpc('increment_prelaunch_analysis_count', {
      p_user_id: internalUserId,
    });

    if (error) {
      console.error('Error incrementing prelaunch analysis count:', error);
      return false;
    }

    // RPC returns the new count on success, or null if the user wasn't found.
    return data !== null;
  }

  // Add user to studio waitlist
  async addToStudioWaitlist(clerkUserId: string, email?: string): Promise<boolean> {
    const internalUserId = await this.getInternalUserId(clerkUserId);
    if (!internalUserId) return false;

    const { error } = await this.supabase
      .from('studio_waitlist')
      .upsert({
        user_id: internalUserId,
        clerk_id: clerkUserId,
        email,
        requested_at: new Date().toISOString(),
        status: 'pending',
        updated_at: new Date().toISOString()
      }, {
        onConflict: 'user_id'
      });

    return !error;
  }

  // Check if user is already on studio waitlist
  async isOnStudioWaitlist(clerkUserId: string): Promise<boolean> {
    const internalUserId = await this.getInternalUserId(clerkUserId);
    if (!internalUserId) return false;

    const { data, error } = await this.supabase
      .from('studio_waitlist')
      .select('id')
      .eq('user_id', internalUserId)
      .single();

    return !error && !!data;
  }

  // Check if user is approved for studio access
  async isApprovedForStudio(clerkUserId: string): Promise<boolean> {
    const internalUserId = await this.getInternalUserId(clerkUserId);
    if (!internalUserId) {
      return false;
    }

    // Query waitlist by user_id first
    const { data, error } = await this.supabase
      .from('studio_waitlist')
      .select('status')
      .eq('user_id', internalUserId)
      .single();

    if (error) {
      // If user_id query fails, try by clerk_id as fallback
      const { data: fallbackData, error: fallbackError } = await this.supabase
        .from('studio_waitlist')
        .select('status')
        .eq('clerk_id', clerkUserId)
        .single();
      
      if (fallbackError || !fallbackData) {
        return false;
      }
      
      return fallbackData.status === 'approved';
    }

    if (!data) {
      return false;
    }

    return data.status === 'approved';
  }

  // Admin methods for waitlist management
  
  // Get all waitlist entries
  async getAllWaitlistEntries(): Promise<any[]> {
    const { data, error } = await this.supabase
      .from('studio_waitlist')
      .select(`
        id,
        user_id,
        clerk_id,
        email,
        requested_at,
        status,
        notes,
        updated_at
      `)
      .order('requested_at', { ascending: false });

    if (error) {
      console.error('Error fetching waitlist entries:', error);
      return [];
    }

    return data || [];
  }

  // Update waitlist entry status
  async updateWaitlistStatus(entryId: string, status: 'approved' | 'rejected' | 'pending', notes?: string): Promise<boolean> {
    const { error } = await this.supabase
      .from('studio_waitlist')
      .update({
        status,
        notes,
        updated_at: new Date().toISOString()
      })
      .eq('id', entryId);

    if (error) {
      console.error('Error updating waitlist status:', error);
      return false;
    }

    return true;
  }
}

export const usageService = new UsageService();