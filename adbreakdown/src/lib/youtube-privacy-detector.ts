// YouTube video privacy detection utility
import { parseYouTubeUrl } from '@/lib/slug-utils'

export interface VideoPrivacyStatus {
  isPublic: boolean
  isPrivate: boolean  
  isUnlisted: boolean
  privacyStatus: 'public' | 'private' | 'unlisted' | 'unknown'
  requiresAuth: boolean
  error?: string
}

/**
 * Detect if a YouTube video is public, private, or unlisted
 * Uses YouTube Data API v3 to check video privacy status
 */
export async function detectVideoPrivacy(
  youtubeUrl: string,
  apiKey?: string
): Promise<VideoPrivacyStatus> {
  
  try {
    // Parse YouTube URL to get video ID
    const urlInfo = parseYouTubeUrl(youtubeUrl)
    if (!urlInfo) {
      return {
        isPublic: false,
        isPrivate: false,
        isUnlisted: false,
        privacyStatus: 'unknown',
        requiresAuth: false,
        error: 'Invalid YouTube URL'
      }
    }

    const videoId = urlInfo.videoId
    const ytApiKey = apiKey || process.env.YOUTUBE_API_KEY || process.env.NEXT_PUBLIC_YOUTUBE_API_KEY

    if (!ytApiKey) {
      console.warn('⚠️ No YouTube API key available for privacy detection')
      return {
        isPublic: false,
        isPrivate: false,
        isUnlisted: false,
        privacyStatus: 'unknown',
        requiresAuth: true,
        error: 'YouTube API key not configured'
      }
    }

    console.log(`🔍 Checking privacy status for video: ${videoId}`)

    // Make API request to get video details
    const apiUrl = `https://www.googleapis.com/youtube/v3/videos?id=${videoId}&part=status,snippet&key=${ytApiKey}`
    
    const response = await fetch(apiUrl, {
      headers: {
        'Accept': 'application/json'
      }
    })

    if (!response.ok) {
      const errorText = await response.text()
      console.error('❌ YouTube API error:', response.status, errorText)
      
      // If 403 or 404, likely private/deleted
      if (response.status === 403 || response.status === 404) {
        return {
          isPublic: false,
          isPrivate: true,
          isUnlisted: false,
          privacyStatus: 'private',
          requiresAuth: true,
          error: 'Video is private or requires authentication'
        }
      }
      
      return {
        isPublic: false,
        isPrivate: false,
        isUnlisted: false,
        privacyStatus: 'unknown',
        requiresAuth: true,
        error: `YouTube API error: ${response.status}`
      }
    }

    const data = await response.json()
    
    // Check if video exists
    if (!data.items || data.items.length === 0) {
      console.log('📹 Video not found or not accessible with API key')
      return {
        isPublic: false,
        isPrivate: true,
        isUnlisted: false,
        privacyStatus: 'private',
        requiresAuth: true,
        error: 'Video not found or private'
      }
    }

    const video = data.items[0]
    const privacyStatus = video.status?.privacyStatus
    
    console.log(`✅ Privacy status detected: ${privacyStatus}`)

    // Determine privacy level
    const isPublic = privacyStatus === 'public'
    const isPrivate = privacyStatus === 'private'  
    const isUnlisted = privacyStatus === 'unlisted'
    const requiresAuth = !isPublic

    return {
      isPublic,
      isPrivate,
      isUnlisted,
      privacyStatus: privacyStatus || 'unknown',
      requiresAuth
    }

  } catch (error) {
    console.error('❌ Error detecting video privacy:', error)
    return {
      isPublic: false,
      isPrivate: false,
      isUnlisted: false,
      privacyStatus: 'unknown',
      requiresAuth: true,
      error: error instanceof Error ? error.message : 'Unknown error'
    }
  }
}

/**
 * Determine the processing strategy based on privacy status
 */
export function getProcessingStrategy(privacyStatus: VideoPrivacyStatus): {
  strategy: 'direct_youtube' | 'download_and_upload' | 'error'
  reason: string
} {
  
  if (privacyStatus.error) {
    return {
      strategy: 'error',
      reason: privacyStatus.error
    }
  }

  if (privacyStatus.isPublic) {
    return {
      strategy: 'direct_youtube',
      reason: 'Video is public - can use direct YouTube URL'
    }
  }

  if (privacyStatus.isPrivate || privacyStatus.isUnlisted) {
    return {
      strategy: 'download_and_upload',
      reason: `Video is ${privacyStatus.privacyStatus} - requires download and GCS upload`
    }
  }

  return {
    strategy: 'download_and_upload',
    reason: 'Unknown privacy status - using safe download approach'
  }
}