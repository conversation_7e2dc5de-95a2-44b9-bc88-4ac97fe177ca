import { redirect } from 'next/navigation'

/**
 * Creates a sign-in URL with a redirect parameter
 * @param returnUrl - The URL to redirect to after successful sign-in
 * @returns The sign-in URL with redirect parameter
 */
export function createSignInUrl(returnUrl: string): string {
  const signInUrl = new URL('/sign-in', process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000')
  signInUrl.searchParams.set('redirect_url', returnUrl)
  return signInUrl.toString()
}

/**
 * Creates a sign-up URL with a redirect parameter
 * @param returnUrl - The URL to redirect to after successful sign-up
 * @returns The sign-up URL with redirect parameter
 */
export function createSignUpUrl(returnUrl: string): string {
  const signUpUrl = new URL('/sign-up', process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000')
  signUpUrl.searchParams.set('redirect_url', returnUrl)
  return signUpUrl.toString()
}

/**
 * Gets the current URL for use as a redirect parameter
 * @param pathname - The current pathname
 * @param search - The current search string (optional)
 * @returns The full URL string
 */
export function getCurrentUrl(pathname: string, search?: string): string {
  const baseUrl = process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000'
  return `${baseUrl}${pathname}${search || ''}`
}

/**
 * Safely redirects to a URL, handling both client and server-side redirects
 * @param url - The URL to redirect to
 */
export function safeRedirect(url: string) {
  if (typeof window !== 'undefined') {
    // Client-side redirect
    window.location.href = url
  } else {
    // Server-side redirect
    redirect(url)
  }
} 