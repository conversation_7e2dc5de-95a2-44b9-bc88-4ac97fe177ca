// Private YouTube URL validation with OAuth token support
import { createServerSupabaseClient } from '@/lib/supabase'
import { parseYouTubeUrl } from '@/lib/slug-utils'
import { 
  YouTubeValidationResult, 
  YouTubeAPIResponse, 
  parseDurationToSeconds, 
  formatDuration, 
  isLikelyAdvertisement 
} from '@/lib/youtube-validator'

export interface PrivateYouTubeValidationResult extends YouTubeValidationResult {
  requiresAuth?: boolean
  isUnlisted?: boolean
}

// Validate YouTube URL with optional OAuth token for private/unlisted videos
export async function validateYouTubeUrlWithAuth(
  url: string,
  oauthToken?: string | null,
  maxDurationSeconds: number = 300 // 5 minutes for private analysis
): Promise<PrivateYouTubeValidationResult> {
  try {
    // Step 1: Basic URL validation
    const urlInfo = parseYouTubeUrl(url)
    if (!urlInfo) {
      return {
        isValid: false,
        error: 'Invalid YouTube URL format'
      }
    }

    // Step 2: Check for existing private analysis (user-specific)
    // This will be implemented when we have proper user context
    // For now, we skip this check

    // Step 3: Try to fetch video metadata with OAuth token if available
    const ytApiKey = process.env.YOUTUBE_API_KEY || process.env.NEXT_PUBLIC_YOUTUBE_API_KEY
    if (!ytApiKey && !oauthToken) {
      return {
        isValid: false,
        error: 'YouTube API key not configured and no OAuth token provided'
      }
    }

    // For now, always use API key since we don't have YouTube scope in OAuth
    // This allows testing private analysis flow with public videos
    const apiUrl = `https://www.googleapis.com/youtube/v3/videos?id=${urlInfo.videoId}&part=snippet,contentDetails,statistics&key=${ytApiKey}`
    const headers: Record<string, string> = {
      'Accept': 'application/json'
    }

    console.log('🔑 Using API key for video access (YouTube OAuth scope not available yet)')

    const response = await fetch(apiUrl, { headers })

    if (!response.ok) {
      console.error('❌ YouTube API error:', response.status, response.statusText)
      return {
        isValid: false,
        error: 'Failed to fetch video information from YouTube'
      }
    }

    const data: YouTubeAPIResponse = await response.json()
    
    if (!data.items || data.items.length === 0) {
      return {
        isValid: false,
        error: 'Video not found or is private/unlisted. Full YouTube OAuth support coming soon.',
        videoId: urlInfo.videoId,
        normalizedUrl: urlInfo.normalizedUrl
      }
    }

    const videoData = data.items[0]
    
    // Step 4: Parse and validate duration
    const durationSeconds = parseDurationToSeconds(videoData.contentDetails.duration)
    const durationFormatted = formatDuration(durationSeconds)
    
    // Private analysis allows longer videos (5 minutes vs 3 minutes for public)
    if (durationSeconds > maxDurationSeconds) {
      return {
        isValid: false,
        error: `Video duration (${durationFormatted}) exceeds the ${formatDuration(maxDurationSeconds)} limit for private analysis`,
        videoId: urlInfo.videoId,
        normalizedUrl: urlInfo.normalizedUrl,
        duration: {
          seconds: durationSeconds,
          formatted: durationFormatted
        },
        metadata: {
          title: videoData.snippet.title,
          channelTitle: videoData.snippet.channelTitle,
          channelId: videoData.snippet.channelId,
          channelUrl: null, // Will be populated later if needed
          channelCustomUrl: null, // Will be populated later if needed
          description: videoData.snippet.description,
          publishedAt: videoData.snippet.publishedAt,
          thumbnailUrl: videoData.snippet.thumbnails.high?.url || 
                        videoData.snippet.thumbnails.medium?.url || 
                        videoData.snippet.thumbnails.default?.url,
          viewCount: videoData.statistics?.viewCount || '0',
          likeCount: videoData.statistics?.likeCount || '0',
          commentCount: videoData.statistics?.commentCount || '0',
          tags: videoData.snippet.tags || [],
          categoryId: videoData.snippet.categoryId,
          defaultLanguage: videoData.snippet.defaultLanguage,
          duration: durationFormatted,
          definition: videoData.contentDetails?.definition || 'sd',
          fetchedAt: new Date().toISOString()
        }
      }
    }

    // Step 5: Determine if video is unlisted (if accessed via OAuth)
    const isUnlisted = Boolean(oauthToken && !videoData.snippet.categoryId)
    
    // Step 6: Check if it's likely an advertisement (for analytics)
    const isLikelyAd = isLikelyAdvertisement(videoData)
    
    if (!isLikelyAd && !isUnlisted) {
      console.warn('⚠️ Video may not be an advertisement based on metadata analysis')
    }

    // Step 7: Return successful validation result
    return {
      isValid: true,
      videoId: urlInfo.videoId,
      normalizedUrl: urlInfo.normalizedUrl,
      duration: {
        seconds: durationSeconds,
        formatted: durationFormatted
      },
      isUnlisted: isUnlisted,
      requiresAuth: false,
      metadata: {
        title: videoData.snippet.title,
        channelTitle: videoData.snippet.channelTitle,
        channelId: videoData.snippet.channelId,
        channelUrl: null, // Will be populated later if needed
        channelCustomUrl: null, // Will be populated later if needed
        description: videoData.snippet.description,
        publishedAt: videoData.snippet.publishedAt,
        thumbnailUrl: videoData.snippet.thumbnails.high?.url || 
                      videoData.snippet.thumbnails.medium?.url || 
                      videoData.snippet.thumbnails.default?.url,
        viewCount: videoData.statistics?.viewCount || '0',
        likeCount: videoData.statistics?.likeCount || '0',
        commentCount: videoData.statistics?.commentCount || '0',
        tags: videoData.snippet.tags || [],
        categoryId: videoData.snippet.categoryId,
        defaultLanguage: videoData.snippet.defaultLanguage,
        duration: durationFormatted,
        definition: videoData.contentDetails?.definition || 'sd',
        fetchedAt: new Date().toISOString()
      }
    }

  } catch (error) {
    console.error('Error validating private YouTube URL:', error)
    
    // Check if it's a network/auth error that might indicate private video
    if (error instanceof Error && error.message.includes('403')) {
      return {
        isValid: false,
        error: 'OAuth authentication required to access this video',
        requiresAuth: true
      }
    }
    
    return {
      isValid: false,
      error: 'Failed to validate YouTube URL'
    }
  }
}

// Get user's OAuth token from database
export async function getUserYouTubeToken(userDbId: string): Promise<string | null> {
  try {
    const supabase = createServerSupabaseClient()
    
    // Check if oauth_tokens table exists first
    const { data: tokenData, error } = await supabase
      .from('oauth_tokens')
      .select('access_token, refresh_token, expires_at')
      .eq('user_id', userDbId)
      .eq('provider', 'google')
      .single()

    if (error) {
      if (error.code === 'PGRST116' || error.message?.includes('relation "oauth_tokens" does not exist')) {
        console.log('⚠️ oauth_tokens table does not exist - migration not applied yet')
        return null
      }
      console.log('No OAuth token found for user:', userDbId, error.message)
      return null
    }

    if (!tokenData) {
      console.log('No OAuth token found for user:', userDbId)
      return null
    }

    // Check if token is expired and refresh if needed
    const now = new Date()
    const expiresAt = new Date(tokenData.expires_at)
    
    if (now >= expiresAt) {
      console.log('OAuth token expired, refreshing...')
      return await refreshYouTubeToken(tokenData.refresh_token, userDbId)
    }

    return tokenData.access_token
  } catch (error) {
    console.error('Error getting user YouTube token:', error)
    return null
  }
}

// Refresh expired OAuth token
async function refreshYouTubeToken(refreshToken: string, userDbId: string): Promise<string | null> {
  try {
    const response = await fetch('https://oauth2.googleapis.com/token', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      body: new URLSearchParams({
        client_id: process.env.GOOGLE_CLIENT_ID!,
        client_secret: process.env.GOOGLE_CLIENT_SECRET!,
        refresh_token: refreshToken,
        grant_type: 'refresh_token',
      }),
    })

    if (!response.ok) {
      throw new Error('Failed to refresh token')
    }

    const tokenData = await response.json()
    
    // Update token in database
    const supabase = createServerSupabaseClient()
    await supabase
      .from('oauth_tokens')
      .update({
        access_token: tokenData.access_token,
        expires_at: new Date(Date.now() + tokenData.expires_in * 1000).toISOString()
      })
      .eq('user_id', userDbId)
      .eq('provider', 'google')

    return tokenData.access_token
  } catch (error) {
    console.error('Error refreshing YouTube token:', error)
    return null
  }
}