// YouTube video downloader for private/unlisted videos
import ytdl from 'ytdl-core'
import { Storage } from '@google-cloud/storage'
import { Readable } from 'stream'

interface DownloadOptions {
  videoId?: string // Optional when sourceUrl is provided
  userToken?: string
  bucketName: string
  gcsCredentials: any
  projectId: string
  sourceUrl?: string // Optional: original URL for social media platforms
}

interface DownloadResult {
  success: boolean
  gcsUri?: string
  fileName?: string
  error?: string
}

// Initialize GCS storage
function initializeStorage(credentials: any, projectId: string) {
  return new Storage({
    projectId: projectId,
    credentials: credentials,
  })
}

// Download YouTube video and upload to GCS using yt-dlp for better reliability
import YTDlpWrap from 'yt-dlp-wrap'
import path from 'path'
import fs from 'fs'

export async function downloadYouTubeVideoToGcs({
  videoId,
  userToken,
  bucketName,
  gcsCredentials,
  projectId,
  sourceUrl
}: DownloadOptions): Promise<DownloadResult> {
  try {
    const videoUrl = sourceUrl || `https://www.youtube.com/watch?v=${videoId}`
    const platform = sourceUrl ? (
      sourceUrl.includes('instagram.com') ? 'Instagram' : 
      sourceUrl.includes('facebook.com') || sourceUrl.includes('fb.watch') ? 'Facebook' : 
      'Social Media'
    ) : 'YouTube'
    
    // Generate a unique identifier for the file
    const fileId = videoId || `${platform.toLowerCase().substring(0, 2)}_${Date.now().toString().slice(-8)}_${Math.random().toString(36).substring(2, 6)}`
    
    console.log(`🎥 Starting ${platform} video download for:`, fileId)
    console.log(`📋 Using URL:`, videoUrl)

    // Temp file path - if fileId is unknown, create a safe unique id
    const safeFileId = fileId && fileId !== 'unknown' ? fileId : `${platform.toLowerCase()}_${Date.now()}`
    const tmpFile = path.join('/tmp', `${safeFileId}.mp4`)

    // Ensure /tmp exists and is writable (create if missing)
    try {
      fs.mkdirSync('/tmp', { recursive: true })
    } catch (mkdirErr) {
      console.warn('⚠️ Could not create /tmp directory or it already exists:', mkdirErr)
    }

    // Initialize yt-dlp-wrap (Vercel-compatible)
    const ytDlpWrap = new YTDlpWrap()
    
    // Options for yt-dlp-wrap
    const downloadOptions = [
      '-f', 'best[ext=mp4]/best',
      '-o', tmpFile,
      '--no-cache-dir',
      '--no-part',
      '--recode-video', 'mp4'
    ]
    
    console.log('▶ Running yt-dlp-wrap with options:', downloadOptions.join(' '), 'URL:', videoUrl)
    
    try {
      const events = ytDlpWrap.exec([
        ...downloadOptions,
        videoUrl
      ])
      
      // Collect output
      let output = ''
      let errorOutput = ''
      
      events.on('progress', (progress) => {
        console.log('📺 yt-dlp progress:', progress)
        output += progress + '\n'
      })
      
      events.on('error', (error) => {
        console.error('📺 yt-dlp error:', error)
        errorOutput += error + '\n'
      })
      
      events.on('ytDlpEvent', (eventType, eventData) => {
        console.log(`📺 yt-dlp ${eventType}:`, eventData)
        output += `${eventType}: ${eventData}\n`
      })
      
      // Wait for completion
      await new Promise<void>((resolve, reject) => {
        events.on('close', (code) => {
          console.log(`📺 yt-dlp finished with code: ${code}`)
          if (code === 0) {
            resolve()
          } else {
            reject(new Error(`yt-dlp exited with code ${code}: ${errorOutput}`))
          }
        })
        
        events.on('error', (err) => {
          reject(err)
        })
      })
      
      console.log('📺 yt-dlp output:', output)
      
    } catch (ytdlpError: any) {
      console.error('❌ yt-dlp-wrap failed:', ytdlpError.message)
      throw ytdlpError
    }
    
    // Verify file was actually created
    if (!fs.existsSync(tmpFile)) {
      throw new Error(`Video file was not created at ${tmpFile} - yt-dlp may have failed silently`)
    }
    
    console.log('✅ Video file created successfully:', tmpFile)

    // Initialize GCS
    const storage = initializeStorage(gcsCredentials, projectId)
    const bucket = storage.bucket(bucketName)
    const platformPrefix = platform.toLowerCase().replace(/\s+/g, '-')
    const fileName = `${platformPrefix}-downloads/${fileId}_${Date.now()}.mp4`
    console.log('📁 Uploading to GCS as:', fileName)

    await bucket.upload(tmpFile, {
      destination: fileName,
      metadata: {
        contentType: 'video/mp4',
        metadata: {
          source: platform.toLowerCase(),
          fileId: fileId,
          originalUrl: videoUrl,
          uploadedAt: new Date().toISOString()
        }
      }
    })

    // Cleanup
    fs.unlinkSync(tmpFile)

    const gcsUri = `gs://${bucketName}/${fileName}`
    console.log('✅ Video successfully uploaded to:', gcsUri)
    return {
      success: true,
      gcsUri,
      fileName
    }
  } catch (error: any) {
    console.error('❌ yt-dlp download/upload failed:', error)
    return {
      success: false,
      error: `yt-dlp failed: ${error.message}`
    }
  }
}

// Check if video requires download (private/unlisted)
export function shouldDownloadVideo(_videoId: string, hasUserToken: boolean): boolean {
  // For now, always try to download when we have a user token
  // This ensures private/unlisted videos work properly
  return hasUserToken
}

// Get video quality options
export async function getVideoQualityOptions(videoId: string, userToken?: string): Promise<string[]> {
  try {
    const youtubeUrl = `https://www.youtube.com/watch?v=${videoId}`
    const options: ytdl.downloadOptions = {}
    
    if (userToken) {
      options.requestOptions = {
        headers: {
          'Authorization': `Bearer ${userToken}`,
        }
      }
    }

    const info = await ytdl.getInfo(youtubeUrl, options)
    const formats = info.formats
      .filter(format => format.hasVideo && format.hasAudio)
      .map(format => String(format.quality))
      .filter((quality, index, arr) => arr.indexOf(quality) === index) // Remove duplicates

    return formats
  } catch (error) {
    console.error('Error getting video quality options:', error)
    return ['highest'] // Fallback
  }
}