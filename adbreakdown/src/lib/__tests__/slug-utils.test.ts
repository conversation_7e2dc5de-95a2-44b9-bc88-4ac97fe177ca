// Test file for slug generation utilities

import { 
  extractYouTubeVideoId, 
  normalizeYouTubeUrl, 
  slugify, 
  generateAdSlug,
  parseYouTubeUrl 
} from '../slug-utils'

describe('YouTube URL parsing', () => {
  test('extractYouTubeVideoId should handle various URL formats', () => {
    expect(extractYouTubeVideoId('https://www.youtube.com/watch?v=dQw4w9WgXcQ')).toBe('dQw4w9WgXcQ')
    expect(extractYouTubeVideoId('https://youtu.be/dQw4w9WgXcQ')).toBe('dQw4w9WgXcQ')
    expect(extractYouTubeVideoId('https://youtube.com/embed/dQw4w9WgXcQ')).toBe('dQw4w9WgXcQ')
    expect(extractYouTubeVideoId('https://www.youtube.com/watch?v=dQw4w9WgXcQ&t=30s')).toBe('dQw4w9WgXcQ')
    expect(extractYouTubeVideoId('https://www.youtube.com/shorts/dQw4w9WgXcQ')).toBe('dQw4w9WgXcQ')
    expect(extractYouTubeVideoId('https://youtube.com/shorts/dQw4w9WgXcQ')).toBe('dQw4w9WgXcQ')
    expect(extractYouTubeVideoId('invalid-url')).toBe(null)
  })

  test('normalizeYouTubeUrl should convert to canonical format', () => {
    expect(normalizeYouTubeUrl('https://youtu.be/dQw4w9WgXcQ')).toBe('https://www.youtube.com/watch?v=dQw4w9WgXcQ')
    expect(normalizeYouTubeUrl('https://youtube.com/embed/dQw4w9WgXcQ')).toBe('https://www.youtube.com/watch?v=dQw4w9WgXcQ')
    expect(normalizeYouTubeUrl('https://www.youtube.com/shorts/dQw4w9WgXcQ')).toBe('https://www.youtube.com/watch?v=dQw4w9WgXcQ')
    expect(normalizeYouTubeUrl('https://youtube.com/shorts/dQw4w9WgXcQ')).toBe('https://www.youtube.com/watch?v=dQw4w9WgXcQ')
    expect(normalizeYouTubeUrl('invalid-url')).toBe(null)
  })

  test('parseYouTubeUrl should return complete info', () => {
    const result = parseYouTubeUrl('https://youtu.be/dQw4w9WgXcQ')
    expect(result).toEqual({
      videoId: 'dQw4w9WgXcQ',
      normalizedUrl: 'https://www.youtube.com/watch?v=dQw4w9WgXcQ',
      isValid: true
    })
  })

  test('parseYouTubeUrl should handle YouTube Shorts URLs', () => {
    const result = parseYouTubeUrl('https://www.youtube.com/shorts/dQw4w9WgXcQ')
    expect(result).toEqual({
      videoId: 'dQw4w9WgXcQ',
      normalizedUrl: 'https://www.youtube.com/watch?v=dQw4w9WgXcQ',
      isValid: true
    })
  })
})

describe('Slug generation', () => {
  test('slugify should convert text to URL-friendly format', () => {
    expect(slugify('Apple iPhone 15 Pro Commercial')).toBe('apple-iphone-15-pro-commercial')
    expect(slugify('Nike: Just Do It!')).toBe('nike-just-do-it')
    expect(slugify('  Multiple   Spaces  ')).toBe('multiple-spaces')
    expect(slugify('Special@#$%Characters')).toBe('specialcharacters')
    expect(slugify('')).toBe('')
  })

  test('generateAdSlug should create proper slugs', () => {
    expect(generateAdSlug('Apple', 'iPhone 15 Pro Commercial', 'dQw4w9WgXcQ')).toBe('apple-iphone-15-pro-commercial-dQw4w9WgXcQ')
    expect(generateAdSlug(null, 'Amazing Product Launch', 'abc123')).toBe('amazing-product-launch-abc123')
    expect(generateAdSlug('Nike', null, 'xyz789')).toBe('nike-xyz789')
    expect(generateAdSlug(null, null, 'def456')).toBe('video-analysis-def456')
    expect(generateAdSlug('N/A', '', 'ghi789', 'custom-fallback')).toBe('custom-fallback-ghi789')
  })
})

// Example manual test cases for verification
const testCases = [
  {
    brand: 'Apple',
    title: 'iPhone 15 Pro - Action Button | Apple',
    videoId: 'dQw4w9WgXcQ',
    expectedSlug: 'apple-iphone-15-pro-action-button-apple-dQw4w9WgXcQ'
  },
  {
    brand: 'Nike',
    title: 'Just Do It - Dream Crazy',
    videoId: 'abc123',
    expectedSlug: 'nike-just-do-it-dream-crazy-abc123'
  },
  {
    brand: null,
    title: 'Coca-Cola Christmas Commercial 2023',
    videoId: 'xyz789',
    expectedSlug: 'coca-cola-christmas-commercial-2023-xyz789'
  },
  {
    brand: 'N/A',
    title: '',
    videoId: 'def456',
    expectedSlug: 'video-analysis-def456'
  }
]

console.log('Manual test cases:')
testCases.forEach((testCase, index) => {
  const result = generateAdSlug(testCase.brand, testCase.title, testCase.videoId)
  console.log(`Test ${index + 1}: ${result} (expected: ${testCase.expectedSlug})`)
})