import { useState, useCallback, useEffect } from 'react'

export interface YouTubeMetadata {
  title: string
  description: string
  channelTitle: string
  publishedAt: string
  viewCount: string
  likeCount: string
  commentCount: string
  tags: string[]
  categoryId: string
  defaultLanguage?: string
  duration: string
  definition: string
}

export interface VideoMetadata {
  title: string
  thumbnail: string
  duration: string
  inferredBrandName: string
}

export interface AnalysisData {
  analysis: any
  loading: boolean
  error: string
  youtubeVideoId: string | null
  youtubeMetadata: YouTubeMetadata | null
  videoMetadata: VideoMetadata
  isInitialLoading: boolean
  videoInfo: string
  marketingAnalysis: string
  parsedData: any
  enhancedScript: string
}

export function useAnalysisData(analysisId: string, preloadedAnalysis: any = null) {
  const [analysis, setAnalysis] = useState<any>(preloadedAnalysis)
  const [loading, setLoading] = useState(!preloadedAnalysis)
  const [error, setError] = useState('')
  const [youtubeVideoId, setYoutubeVideoId] = useState<string | null>(null)
  const [youtubeMetadata, setYoutubeMetadata] = useState<YouTubeMetadata | null>(null)
  const [isInitialLoading, setIsInitialLoading] = useState(!preloadedAnalysis)
  const [videoInfo, setVideoInfo] = useState('')
  const [marketingAnalysis, setMarketingAnalysis] = useState('')
  const [parsedData, setParsedData] = useState<any>(null)
  const [enhancedScript, setEnhancedScript] = useState('')
  const [videoMetadata, setVideoMetadata] = useState<VideoMetadata>({
    title: 'Loading...',
    thumbnail: 'https://placehold.co/700x400/3B82F6/FFFFFF?text=Video+Thumbnail',
    duration: '0:00',
    inferredBrandName: 'N/A'
  })

  // Helper functions
  const extractYouTubeVideoId = (url: string): string | null => {
    if (!url) return null
    const regex = /(?:youtube\.com\/(?:[^\/]+\/.+\/|(?:v|e(?:mbed)?)\/|.*[?&]v=)|youtu\.be\/)([^"&?\/\s]{11})/
    const match = url.match(regex)
    return match ? match[1] : null
  }

  const getYouTubeThumbnail = (videoId: string, quality: string = 'hqdefault'): string => {
    return `https://img.youtube.com/vi/${videoId}/${quality}.jpg`
  }

  const formatDuration = (seconds: number | string | null): string => {
    if (!seconds) return '0:00'
    const num = typeof seconds === 'string' ? parseInt(seconds) : seconds
    if (isNaN(num)) return '0:00'
    
    const hours = Math.floor(num / 3600)
    const minutes = Math.floor((num % 3600) / 60)
    const remainingSeconds = num % 60
    
    if (hours > 0) {
      return `${hours}:${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`
    }
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`
  }

  const parseDuration = (duration: string): string => {
    if (!duration) return '0:00'
    const match = duration.match(/PT(?:(\d+)H)?(?:(\d+)M)?(?:(\d+)S)?/)
    if (!match) return '0:00'
    
    const hours = parseInt(match[1] || '0')
    const minutes = parseInt(match[2] || '0')
    const seconds = parseInt(match[3] || '0')
    
    if (hours > 0) {
      return `${hours}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`
    }
    return `${minutes}:${seconds.toString().padStart(2, '0')}`
  }

  const fetchYouTubeMetadata = async (videoId: string) => {
    try {
      const response = await fetch(`/api/youtube/metadata?videoId=${videoId}`)
      if (response.ok) {
        const data = await response.json()
        const { snippet, statistics, contentDetails } = data
        
        const metadata: YouTubeMetadata = {
          title: snippet.title || 'Untitled Video',
          description: snippet.description || 'No description available',
          channelTitle: snippet.channelTitle || 'Unknown Channel',
          publishedAt: snippet.publishedAt,
          viewCount: statistics.viewCount || '0',
          likeCount: statistics.likeCount || '0',
          commentCount: statistics.commentCount || '0',
          tags: snippet.tags || [],
          categoryId: snippet.categoryId || '',
          defaultLanguage: snippet.defaultLanguage,
          duration: parseDuration(contentDetails.duration),
          definition: contentDetails.definition || 'sd'
        }
        
        setYoutubeMetadata(metadata)
      }
    } catch (error) {
      console.error('Error fetching YouTube metadata:', error)
    }
  }

  // Centralized data parsing function
  const parseMarketingAnalysis = useCallback((data: any) => {
    if (!data) return null
    try {
      if (typeof data === 'object' && data !== null) {
        return data
      }
      return JSON.parse(data)
    } catch (error) {
      console.error('Error parsing marketing analysis:', error)
      return null
    }
  }, [])

  const parseAndSetData = useCallback((analysisData: any) => {
    console.log('parseAndSetData: Received analysis data', analysisData)
    setAnalysis(analysisData)
    
    // Extract YouTube video ID from URL first
    let videoId: string | null = null
    if (analysisData.video_url) {
      videoId = extractYouTubeVideoId(analysisData.video_url)
      setYoutubeVideoId(videoId)
      
      // Fetch YouTube metadata
      if (videoId) {
        fetchYouTubeMetadata(videoId)
      }
    }
    
    // Also check if we have a direct youtube_video_id field
    if (analysisData.youtube_video_id) {
      setYoutubeVideoId(analysisData.youtube_video_id)
      
      // Fetch YouTube metadata if we don't already have it
      if (!videoId) {
        fetchYouTubeMetadata(analysisData.youtube_video_id)
      }
    }
    
    // Use YouTube thumbnail if we have a video ID, otherwise fallback to stored thumbnail
    const thumbnailUrl = videoId 
      ? getYouTubeThumbnail(videoId, 'hqdefault')
      : analysisData.video_thumbnail_url || analysisData.thumbnail_url || 'https://placehold.co/700x400/3B82F6/FFFFFF?text=Video+Thumbnail'
    
    setVideoMetadata({
      title: analysisData.video_title || analysisData.title || 'No Title',
      thumbnail: thumbnailUrl,
      duration: formatDuration(analysisData.video_duration || analysisData.duration_seconds),
      inferredBrandName: analysisData.inferred_brand || 'N/A'
    })

    // Set video info and marketing analysis from database
    if (analysisData.video_info) {
      setVideoInfo(analysisData.video_info)
    } else if (analysisData.transcript && analysisData.summary) {
      setVideoInfo(analysisData.transcript + "\n\n**Summary:**\n" + analysisData.summary)
    }
    
    if (analysisData.marketing_analysis) {
      setMarketingAnalysis(analysisData.marketing_analysis)
    }

    // Enhanced script handling
    if (analysisData.deciphered_script?.enhanced_analysis) {
      setEnhancedScript(analysisData.deciphered_script.enhanced_analysis)
    }

    // Parse marketing analysis for structured data
    const parsed = parseMarketingAnalysis(analysisData.marketing_analysis)
    setParsedData(parsed)
  }, [parseMarketingAnalysis])

  const fetchAnalysis = useCallback(async () => {
    if (!analysisId) return
    
    try {
      setIsInitialLoading(true)
      
      // First, try to get basic data quickly for immediate display
      const basicTimestamp = Date.now()
      const basicRes = await fetch(`/api/analyses/${analysisId}/basic?_t=${basicTimestamp}`, {
        cache: 'no-store',
        headers: {
          'Cache-Control': 'no-cache, no-store, must-revalidate',
          'Pragma': 'no-cache',
          'Expires': '0'
        }
      })
      
      if (basicRes.ok) {
        const basicData = await basicRes.json()
        parseAndSetData(basicData)
        setIsInitialLoading(false)
      }
      
      // Then fetch full data in background with a small delay to improve perceived performance
      setTimeout(async () => {
        try {
          const fullTimestamp = Date.now()
          const fullRes = await fetch(`/api/analyses/${analysisId}?_t=${fullTimestamp}`, {
            cache: 'no-store',
            headers: {
              'Cache-Control': 'no-cache, no-store, must-revalidate',
              'Pragma': 'no-cache',
              'Expires': '0'
            }
          })
          if (fullRes.ok) {
            const fullData = await fullRes.json()
            parseAndSetData(fullData)
          }
        } catch (err) {
          console.warn('Failed to load full analysis data:', err)
        }
      }, 100)
      
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An unknown error occurred.')
      setIsInitialLoading(false)
      setLoading(false)
    }
  }, [analysisId, parseAndSetData])

  // Load analysis on mount if not preloaded
  useEffect(() => {
    if (!preloadedAnalysis) {
      fetchAnalysis()
    } else {
      parseAndSetData(preloadedAnalysis)
      setIsInitialLoading(false)
    }
  }, [preloadedAnalysis, fetchAnalysis, parseAndSetData])

  return {
    analysis,
    loading,
    error,
    youtubeVideoId,
    youtubeMetadata,
    videoMetadata,
    isInitialLoading,
    videoInfo,
    marketingAnalysis,
    parsedData,
    enhancedScript,
    setAnalysis,
    setLoading,
    setError,
    setEnhancedScript,
    fetchAnalysis,
    parseAndSetData
  }
}
