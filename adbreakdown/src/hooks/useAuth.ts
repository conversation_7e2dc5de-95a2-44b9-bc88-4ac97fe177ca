import { useUser } from '@clerk/nextjs'

// Custom hook for authentication state (leveraging Clerk)
export const useAuth = () => {
  const { isSignedIn, user, isLoaded } = useUser()
  
  // Check if user is admin based on ADMIN_USER_IDS
  // In development, everyone is admin
  const adminUserIds = process.env.NEXT_PUBLIC_ADMIN_USER_IDS?.split(',') || []
  const isAdmin = process.env.NODE_ENV === 'development' || 
                  (user?.id && adminUserIds.includes(user.id))
  
  return {
    isAuthenticated: isSignedIn || false,
    user: user,
    isAdmin: isAdmin || false,
    loading: !isLoaded
  }
}
