import { useState } from 'react'
import { useCredits } from '@/hooks/useCredits'

export function useAnalysisActions(
  analysisId: string,
  analysis: any,
  fetchAnalysis: () => void,
  setError: (error: string) => void,
  setEnhancedScript: (script: string) => void
) {
  const { deductCredits } = useCredits()
  
  // Loading states
  const [isDeleting, setIsDeleting] = useState(false)
  const [enhancedScriptLoading, setEnhancedScriptLoading] = useState(false)
  const [togglePublicLoading, setTogglePublicLoading] = useState(false)
  
  // Modal states
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false)
  const [showShareModal, setShowShareModal] = useState(false)

  const handleDeleteAnalysis = async () => {
    if (!analysis?.id) return

    setIsDeleting(true)
    try {
      const response = await fetch(`/api/analyses/${analysis.id}`, {
        method: 'DELETE'
      })

      if (response.ok) {
        window.location.href = '/ad-library'
      } else {
        const errorData = await response.json()
        setError(errorData.error || 'Failed to delete analysis')
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to delete analysis')
    } finally {
      setIsDeleting(false)
      setShowDeleteConfirm(false)
    }
  }

  const generateEnhancedScript = async () => {
    if (!analysis?.id) return
    
    setEnhancedScriptLoading(true)
    setError('')
    
    try {
      // Deduct 3 credits first
      const creditDeducted = await deductCredits(3)
      if (!creditDeducted) {
        setError('Failed to process credit. Please try again.')
        setEnhancedScriptLoading(false)
        return
      }
      
      // Call the enhanced script API endpoint
      const response = await fetch(`/api/analyses/${analysisId}/generate-enhanced-script`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' }
      })
      
      if (!response.ok) {
        const errorData = await response.json()
        console.error('API Error:', errorData)
        throw new Error(errorData.error || 'Failed to generate production note')
      }
      
      const result = await response.json()
      setEnhancedScript(result.enhanced_analysis)
      fetchAnalysis() // Refresh data
      setEnhancedScriptLoading(false)
      
    } catch (err) {
      console.error('Error generating enhanced script:', err)
      setError(err instanceof Error ? err.message : 'Failed to generate production note')
      setEnhancedScriptLoading(false)
    }
  }

  const togglePublicStatus = async () => {
    if (!analysis?.id) return
    
    setTogglePublicLoading(true)
    setError('')

    try {
      const response = await fetch(`/api/analyses/${analysis.id}/toggle-public`, {
        method: 'PATCH',
        headers: { 'Content-Type': 'application/json' }
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to update visibility')
      }

      // Refresh the analysis data to get the updated public status
      fetchAnalysis()
      setTogglePublicLoading(false)

    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to update visibility.')
      setTogglePublicLoading(false)
    }
  }

  const handleShare = () => {
    setShowShareModal(true)
  }

  const getShareUrl = () => {
    if (!analysis) return ''
    return `${window.location.origin}/ad/${analysis.slug || analysis.id}`
  }

  const getShareText = () => {
    if (!analysis) return ''
    const adTitle = analysis.video_title || analysis.title || `${analysis.inferred_brand || 'Brand'} Ad`
    return `Check out this AI analysis of "${adTitle}" on`
  }

  const shareToTwitter = () => {
    const text = encodeURIComponent(getShareText())
    const url = encodeURIComponent(getShareUrl())
    window.open(`https://twitter.com/intent/tweet?text=${text}&url=${url}`, '_blank')
  }

  const shareToLinkedIn = () => {
    const url = encodeURIComponent(getShareUrl())
    window.open(`https://www.linkedin.com/sharing/share-offsite/?url=${url}`, '_blank')
  }

  const shareToWhatsApp = () => {
    const text = encodeURIComponent(`${getShareText()} ${getShareUrl()}`)
    window.open(`https://wa.me/?text=${text}`, '_blank')
  }

  const shareToInstagram = () => {
    // Instagram doesn't support direct URL sharing, so we'll copy to clipboard
    copyToClipboard()
    alert('Link copied! You can paste it in your Instagram story or bio.')
  }

  const copyToClipboard = async () => {
    try {
      await navigator.clipboard.writeText(getShareUrl())
      alert('Link copied to clipboard!')
    } catch (error) {
      console.error('Clipboard error:', error)
      // Fallback: show the URL to copy manually
      prompt('Copy this link:', getShareUrl())
    }
  }

  return {
    // States
    isDeleting,
    enhancedScriptLoading,
    togglePublicLoading,
    showDeleteConfirm,
    showShareModal,
    
    // Actions
    handleDeleteAnalysis,
    generateEnhancedScript,
    togglePublicStatus,
    handleShare,
    
    // Share functions
    shareToTwitter,
    shareToLinkedIn,
    shareToWhatsApp,
    shareToInstagram,
    copyToClipboard,
    
    // Setters
    setShowDeleteConfirm,
    setShowShareModal,
    
    // Getters
    getShareUrl,
    getShareText
  }
}
