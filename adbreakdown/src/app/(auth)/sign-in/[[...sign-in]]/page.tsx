import { SignIn } from '@clerk/nextjs'
import { redirect } from 'next/navigation'

interface SignInPageProps {
  searchParams: Promise<{ [key: string]: string | string[] | undefined }>
}

export default async function Page({ searchParams }: SignInPageProps) {
  // Get the redirect URL from query parameters
  const params = await searchParams
  const redirectUrl = params.redirect_url as string || '/studio'
  
  return (
    <div className="flex min-h-screen items-center justify-center">
      <SignIn 
        redirectUrl={redirectUrl}
        afterSignInUrl={redirectUrl}
      />
    </div>
  )
}
