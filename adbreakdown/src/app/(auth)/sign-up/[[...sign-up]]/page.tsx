import { SignUp } from '@clerk/nextjs'
import { redirect } from 'next/navigation'

interface SignUpPageProps {
  searchParams: Promise<{ [key: string]: string | string[] | undefined }>
}

export default async function Page({ searchParams }: SignUpPageProps) {
  // Get the redirect URL from query parameters
  const params = await searchParams
  const redirectUrl = params.redirect_url as string || '/studio'
  
  return (
    <div className="flex min-h-screen items-center justify-center">
      <SignUp 
        redirectUrl={redirectUrl}
        afterSignUpUrl={redirectUrl}
      />
    </div>
  )
}
