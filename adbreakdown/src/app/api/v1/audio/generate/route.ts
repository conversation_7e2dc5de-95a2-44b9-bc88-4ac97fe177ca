import { NextRequest, NextResponse } from 'next/server';
import { GoogleGenerativeAI } from '@google/generative-ai';
import { createServerSupabaseClient } from '@/lib/supabase';
import { auth } from '@clerk/nextjs/server';
import { GCSService } from '@/lib/gcs';

export async function POST(request: NextRequest) {
  try {
    // Check authentication
    const { userId } = await auth();
    if (!userId) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { audioType, voice, mood, duration, brandInfo } = await request.json();

    if (!audioType || !mood || !duration) {
      return NextResponse.json(
        { error: 'Audio type, mood, and duration are required' },
        { status: 400 }
      );
    }

    console.log('Generating audio content with:', { audioType, voice, mood, duration });

    // Get user from database
    const supabase = createServerSupabaseClient();
    const { data: user, error: userError } = await supabase
      .from('users')
      .select('id')
      .eq('clerk_id', userId)
      .single();

    if (userError || !user) {
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      );
    }

    // Parse brand info if it's JSON
    let parsedBrandInfo;
    try {
      parsedBrandInfo = JSON.parse(brandInfo);
    } catch {
      parsedBrandInfo = { url: brandInfo };
    }

    // Generate audio content using AI (placeholder implementation)
    const audioUrl = await generateAudioWithAI(audioType, voice, mood, duration, parsedBrandInfo);

    console.log(`✅ Successfully generated ${audioType} audio`);

    // Save to database
    try {
      const { error: saveError } = await supabase
        .from('generated_assets')
        .insert({
          user_id: user.id,
          asset_type: 'audio',
          asset_name: `${audioType.replace('-', ' ').toUpperCase()} - ${new Date().toLocaleDateString()}`,
          asset_url: audioUrl,
          generation_type: 'audio_generation',
          prompt: `Generate ${audioType} with ${voice || 'default'} voice, ${mood} mood, ${duration} duration`,
          generation_parameters: {
            audioType,
            voice,
            mood,
            duration,
            brandInfo
          },
          mime_type: 'audio/mpeg',
          status: 'completed'
        });

      if (saveError) {
        console.error('Failed to save audio asset to database:', saveError);
      } else {
        console.log('✅ Saved audio asset to database');
      }
    } catch (dbError) {
      console.error('Database save error:', dbError);
    }

    return NextResponse.json({
      audioUrl,
      metadata: {
        audioType,
        voice,
        mood,
        duration,
        technology: getTechnologyUsed(audioType),
        fileFormat: 'MP3'
      }
    });

  } catch (error) {
    console.error('Error generating audio content:', error);
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    return NextResponse.json(
      { error: 'Failed to generate audio content: ' + errorMessage },
      { status: 500 }
    );
  }
}

async function generateAudioWithAI(
  audioType: string,
  voice: string | null,
  mood: string,
  duration: string,
  brandInfo: any
): Promise<string> {
  try {
    console.log('Generating audio with AI...');

    // Generate actual audio content (placeholder implementation)
    const audioBuffer = await generateActualAudio(audioType, voice, mood, duration, brandInfo);

    // Upload to GCS
    const timestamp = Date.now();
    const randomId = Math.random().toString(36).substring(2, 11);
    const fileName = `generated-audio/${audioType}-${timestamp}-${randomId}.mp3`;

    try {
      // Upload audio buffer to GCS
      const audioUrl = await GCSService.uploadBase64Image(
        audioBuffer.toString('base64'),
        fileName,
        'audio/mpeg'
      );

      console.log(`✅ Uploaded audio to GCS: ${audioUrl}`);
      return audioUrl;
    } catch (uploadError) {
      console.error('❌ Failed to upload audio to GCS:', uploadError);
      // Fallback to base64 data URL if GCS upload fails
      return `data:audio/mpeg;base64,${audioBuffer.toString('base64')}`;
    }
  } catch (error) {
    console.error('Error with AI audio generation:', error);
    throw error;
  }
}

async function generateActualAudio(
  audioType: string,
  voice: string | null,
  mood: string,
  duration: string,
  brandInfo: string
): Promise<Buffer> {
  try {
    // Generate a simple audio file (placeholder implementation)
    // In production, this would call Chirp AI and/or Lyra AI APIs

    console.log(`Generating ${audioType} audio with ${voice || 'default'} voice, ${mood} mood, ${duration} duration`);

    // Create a simple audio buffer (placeholder)
    // This would be replaced with actual audio generation from AI services
    const audioContent = await generateSimpleAudioBuffer(audioType, voice, mood, duration, brandInfo);

    return audioContent;
  } catch (error) {
    console.error('Error generating actual audio:', error);
    throw error;
  }
}

async function generateSimpleAudioBuffer(
  audioType: string,
  voice: string | null,
  mood: string,
  duration: string,
  brandInfo: any
): Promise<Buffer> {
  // This is a placeholder that creates a minimal MP3 file
  // In production, this would be replaced with actual AI audio generation

  // Create a minimal MP3 header (placeholder)
  const mp3Header = Buffer.from([
    0xFF, 0xFB, 0x90, 0x00, // MP3 frame header
    0x00, 0x00, 0x00, 0x00, // Additional header data
  ]);

  // Format brand info for audio generation context
  const formattedBrandInfo = formatBrandInfoForAudio(brandInfo);
  console.log('Using brand context for audio generation:', formattedBrandInfo);

  // Generate some basic audio data based on duration
  const durationMs = parseDuration(duration);
  const sampleRate = 44100;
  const samples = Math.floor((durationMs / 1000) * sampleRate);

  // Create simple sine wave audio data (placeholder)
  const audioData = Buffer.alloc(samples * 2); // 16-bit audio
  for (let i = 0; i < samples; i++) {
    const frequency = mood === 'energetic' ? 440 : mood === 'calm' ? 220 : 330;
    const amplitude = 0.3;
    const sample = Math.sin(2 * Math.PI * frequency * i / sampleRate) * amplitude * 32767;
    audioData.writeInt16LE(Math.floor(sample), i * 2);
  }

  // Combine header and data
  return Buffer.concat([mp3Header, audioData]);
}

function parseDuration(duration: string): number {
  // Convert duration string to milliseconds
  if (duration.endsWith('s')) {
    return parseInt(duration) * 1000;
  } else if (duration.endsWith('min')) {
    return parseInt(duration) * 60 * 1000;
  }
  return 5000; // Default 5 seconds
}

function getTechnologyUsed(audioType: string): string {
  switch (audioType) {
    case 'radio-ad':
    case 'voiceover':
    case 'podcast-intro':
      return 'Chirp AI + Lyra AI';
    case 'jingle':
      return 'Lyra AI + Chirp AI';
    case 'background-music':
    case 'sound-effect':
      return 'Lyra AI';
    default:
      return 'AI Audio Generation';
  }
}

// Placeholder functions for actual AI integration
// These would be implemented with real Chirp and Lyra API calls

async function callChirpAI(text: string, voice: string, mood: string): Promise<string> {
  // Placeholder for Chirp AI voice synthesis
  // Would make actual API call to Chirp AI service
  console.log('Calling Chirp AI for voice synthesis...');
  return 'chirp-audio-url';
}

async function callLyraAI(style: string, mood: string, duration: string): Promise<string> {
  // Placeholder for Lyra AI music generation
  // Would make actual API call to Lyra AI service
  console.log('Calling Lyra AI for music generation...');
  return 'lyra-audio-url';
}

async function combineAudioElements(elements: string[]): Promise<string> {
  // Placeholder for audio mixing and combining
  // Would use audio processing libraries to combine multiple audio sources
  console.log('Combining audio elements...');
  return 'combined-audio-url';
}

function formatBrandInfoForAudio(brandInfo: any): string {
  if (typeof brandInfo === 'string') {
    return brandInfo;
  }

  if (typeof brandInfo === 'object' && brandInfo !== null) {
    const parts = [];

    if (brandInfo.url) {
      parts.push(`Website: ${brandInfo.url}`);
    }

    if (brandInfo.brandName) {
      parts.push(`Brand Name: ${brandInfo.brandName}`);
    }

    if (brandInfo.category) {
      parts.push(`Category: ${brandInfo.category}`);
    }

    if (brandInfo.subCategory) {
      parts.push(`Sub Category: ${brandInfo.subCategory}`);
    }

    if (brandInfo.targetAudience) {
      parts.push(`Target Audience: ${brandInfo.targetAudience}`);
    }

    if (brandInfo.market) {
      parts.push(`Market: ${brandInfo.market}`);
    }

    if (brandInfo.usp) {
      parts.push(`Unique Selling Proposition: ${brandInfo.usp}`);
    }

    if (brandInfo.videoDescription) {
      parts.push(`Additional Context: ${brandInfo.videoDescription}`);
    }

    return parts.join('\n');
  }

  return 'Brand information not available';
}
