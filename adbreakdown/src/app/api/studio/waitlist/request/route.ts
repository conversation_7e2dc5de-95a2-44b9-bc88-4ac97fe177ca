import { NextRequest, NextResponse } from 'next/server'
import { auth } from '@clerk/nextjs/server'
import { usageService } from '@/lib/services/usageService'

export async function POST(req: NextRequest) {
  try {
    const { userId } = await auth()
    
    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body = await req.json()
    const { email } = body

    // Check if user is already on waitlist
    const isAlreadyOnWaitlist = await usageService.isOnStudioWaitlist(userId)
    if (isAlreadyOnWaitlist) {
      return NextResponse.json(
        { error: 'User already on waitlist' }, 
        { status: 409 }
      )
    }

    // Add user to waitlist
    const success = await usageService.addToStudioWaitlist(userId, email)
    
    if (!success) {
      return NextResponse.json(
        { error: 'Failed to add to waitlist' }, 
        { status: 500 }
      )
    }

    return NextResponse.json({
      success: true,
      message: 'Successfully added to waitlist'
    })

  } catch (error) {
    console.error('Error in studio waitlist request:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}