import { NextRequest, NextResponse } from 'next/server'
import { auth } from '@clerk/nextjs/server'
import { usageService } from '@/lib/services/usageService'

export async function GET(req: NextRequest) {
  try {
    const { userId } = await auth()
    
    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const isOnWaitlist = await usageService.isOnStudioWaitlist(userId)

    return NextResponse.json({
      success: true,
      isOnWaitlist
    })

  } catch (error) {
    console.error('Error checking studio waitlist status:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}