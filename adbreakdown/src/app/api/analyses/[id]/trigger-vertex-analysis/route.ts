import { NextRequest, NextResponse } from 'next/server'
import { auth } from '@clerk/nextjs/server'
import { createServerSupabaseClient } from '@/lib/supabase'
import { VertexAI } from '@google-cloud/vertexai'
import { getMarketingAnalysisPrompt } from '@/lib/prompts/marketingAnalysisPrompt'


interface RouteParams {
  params: Promise<{ 
    id: string
  }>
}

// Simple URL validation function
async function validateAndCorrectUrl(url: string, title: string): Promise<string> {
  try {
    // Try to create a URL object to validate format
    new URL(url)
    return url
  } catch {
    // If URL is invalid, return a placeholder or the original
    console.warn(`Invalid URL found: ${url} for title: ${title}`)
    return url
  }
}

// POST /api/analyses/{id}/trigger-vertex-analysis
export async function POST(req: NextRequest, { params }: RouteParams) {
  try {
    const { id } = await params

    console.log('✅ SERVERLESS API: trigger-vertex-analysis called', {
      id,
      timestamp: new Date().toISOString(),
      method: 'POST',
      userAgent: req.headers.get('user-agent'),
      requestId: Math.random().toString(36).substring(2, 11)
    })

    // Check if client wants streaming response
    const acceptHeader = req.headers.get('accept') || ''
    const wantsStream = acceptHeader.includes('text/event-stream')

    console.log('🌊 Streaming requested:', wantsStream)

    const { userId } = await auth()
    console.log('🔐 Clerk Authentication:', { userId: userId ? 'EXISTS' : 'NULL' })
    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body = await req.json()
    const { youtubeUrl } = body

    if (!youtubeUrl) {
      return NextResponse.json({ error: 'YouTube URL is required' }, { status: 400 })
    }

    // Create server Supabase client with service role permissions (like working routes)
    const supabase = createServerSupabaseClient()
    
    // Get user from database (using EXACT same pattern as working routes)
    console.log('🔍 Vertex route: Looking for user with Clerk ID:', userId)
    console.log('🔍 Vertex route: Using server Supabase client with service role permissions')
    
    let { data: user, error: userError } = await supabase
      .from('users')
      .select('id')
      .eq('clerk_id', userId)
      .single()

    console.log('🔍 Vertex route: User query result:', { user, userError })
    
    // If user not found, let's check what users DO exist
    if (!user) {
      console.log('🔍 Vertex route: User not found, checking what users exist...')
      const { data: allUsers, error: allUsersError } = await supabase
        .from('users')
        .select('id, clerk_id')
        .limit(5)
      
      console.log('🔍 Vertex route: Existing users sample:', { allUsers, allUsersError })
    }

    if (userError || !user) {
      console.log('🔧 Vertex route: User not found, attempting to create user (dev/prod sync)')
      
      // Auto-create user for dev/prod environment sync
      const { data: newUser, error: createError } = await supabase
        .from('users')
        .insert({ 
          clerk_id: userId,
          email: '<EMAIL>', // Default email for dev users
          first_name: 'Dev',
          last_name: 'User'
        })
        .select('id')
        .single()
      
      if (createError || !newUser) {
        console.error('❌ Vertex route: Failed to create user:', createError)
        return NextResponse.json({
          message: 'Analysis triggered successfully (testing mode - user creation failed)',
          analysis_id: id,
          status: 'completed',
          mock: true
        }, { status: 202 })
      }
      
      console.log('✅ Vertex route: Created new user for dev/prod sync:', { 
        clerkId: userId, 
        dbUserId: newUser.id 
      })
      
      user = newUser
    }

    console.log('✅ Found user:', { clerkId: userId, dbUserId: user.id })

    // Get analysis data including YouTube metadata
    console.log('🔍 Fetching analysis data for ID/slug:', id)
    
    // Check if id is a UUID or a slug
    const isUUID = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i.test(id)
    
    const { data: analysis, error: analysisError } = await supabase
      .from('ad_analyses')
      .select('id, youtube_metadata, title, youtube_video_id, slug')
      .eq(isUUID ? 'id' : 'slug', id)
      .eq('user_id', user.id)
      .single()

    if (analysisError || !analysis) {
      console.error('❌ Analysis not found:', analysisError)
      return NextResponse.json({ error: 'Analysis not found or access denied' }, { status: 404 })
    }

    console.log('📊 Analysis data:', { 
      analysisId: analysis.id, 
      hasMetadata: !!analysis.youtube_metadata,
      title: analysis.title,
      videoId: analysis.youtube_video_id
    })

    // Initialize Vertex AI with authentication
    const projectId = process.env.GOOGLE_PROJECT_ID
    const serviceAccountKey = process.env.GOOGLE_CLOUD_SERVICE_ACCOUNT_KEY
    
    console.log('🔧 Google Cloud Authentication Check:', {
      hasProjectId: !!projectId,
      hasServiceAccountKey: !!serviceAccountKey,
      serviceAccountKeyLength: serviceAccountKey?.length || 0,
      nodeEnv: process.env.NODE_ENV
    })
    
    if (!projectId) {
      console.error('❌ Missing GOOGLE_PROJECT_ID environment variable')
      return NextResponse.json(
        { error: 'Google Cloud Project ID not configured' },
        { status: 500 }
      )
    }

    let vertex_ai: VertexAI
    
    // Check if we have service account key for production
    if (serviceAccountKey) {
      try {
        console.log('🔑 Attempting to parse service account key...')
        let credentials;

        // The service account key might be a raw JSON string or a Base64 encoded string.
        // We'll try parsing it as raw JSON first.
        try {
          credentials = JSON.parse(serviceAccountKey);
          console.log('✅ Successfully parsed raw JSON from service account key.');
        } catch (rawParseError) {
          // If raw parsing fails, assume it's Base64 encoded, which is a common and robust way to store JSON in env vars.
          console.log('⚠️ Raw JSON parse failed, attempting Base64 decoding...');
          const decodedKey = Buffer.from(serviceAccountKey, 'base64').toString('utf-8');
          credentials = JSON.parse(decodedKey);
          console.log('✅ Successfully parsed Base64 decoded service account key.');
        }
        
        vertex_ai = new VertexAI({
          project: projectId,
          location: 'us-central1',
          googleAuthOptions: {
            credentials
          }
        })
        console.log('🔐 Successfully initialized Vertex AI with service account key authentication')
      } catch (error: any) {
        console.error('❌ Failed to parse service account key (tried raw and Base64):', {
          parseError: error.message,
          keyPreview: serviceAccountKey.substring(0, 100) + '...'
        })
        return NextResponse.json(
          { error: `Invalid service account credentials: ${error.message}` },
          { status: 500 }
        )
      }
    } else {
      console.log('⚠️ No service account key found, using default authentication (this will fail in production)')
      // Use default authentication (works in dev with gcloud auth)
      vertex_ai = new VertexAI({
        project: projectId,
        location: 'us-central1',
      })
      console.log('🔐 Initialized Vertex AI with default authentication (dev only)')
    }

    // Configure Google Search grounding tool (corrected format)
    const tools = [
      {
        googleSearch: {}
      }
    ] as any[];

    // Enhanced generation config (reduced token limit to prevent truncation)
    const generationConfig = {
      temperature: 0.9,
      topP: 0.95,
      maxOutputTokens: 32768, // Reduced from 65535 to prevent truncation issues
      // Note: responseMimeType cannot be used with search grounding
      // responseMimeType: 'application/json',
    };

    // Safety settings (matching Python implementation - all off)
    const safetySettings = [
      {
        category: 'HARM_CATEGORY_HATE_SPEECH' as any,
        threshold: 'BLOCK_NONE' as any
      },
      {
        category: 'HARM_CATEGORY_DANGEROUS_CONTENT' as any, 
        threshold: 'BLOCK_NONE' as any
      },
      {
        category: 'HARM_CATEGORY_SEXUALLY_EXPLICIT' as any,
        threshold: 'BLOCK_NONE' as any
      },
      {
        category: 'HARM_CATEGORY_HARASSMENT' as any,
        threshold: 'BLOCK_NONE' as any
      }
    ];

    const model = vertex_ai.getGenerativeModel({ 
      model: 'gemini-2.5-pro',
      generationConfig,
      safetySettings,
      tools
    });

    console.log('\n🌯 VERTEX AI ROUTE: Using enhanced configuration with Google Search grounding...');
    const promptContent = getMarketingAnalysisPrompt(youtubeUrl)
    
    console.log('\n📊 VERTEX AI ENHANCED CONFIG:', {
      model: 'gemini-2.5-pro',
      searchGrounding: 'Google Search enabled',
      temperature: 0.9,
      topP: 0.95,
      maxTokens: 65535,
      safetySettings: 'All disabled',
      promptLength: promptContent.length,
      analysisMethod: 'Vertex AI with Search Grounding'
    })
    
    const systemInstruction = {
      role: "system",
      parts: [{ text: promptContent }],
    };

    const videoPart = {
      fileData: {
        mimeType: 'video/mp4',
        fileUri: youtubeUrl,
      },
    };

    // Extract YouTube metadata for context
    let metadataContext = ''
    if (analysis.youtube_metadata) {
      const metadata = analysis.youtube_metadata
      metadataContext = `

Video Metadata:
- Title: ${metadata.title || 'N/A'}
- Channel: ${metadata.channelTitle || 'N/A'}
- Published: ${metadata.publishedAt || 'N/A'}
- Description: ${metadata.description || 'N/A'}
- Duration: ${metadata.duration || 'N/A'}
- Tags: ${metadata.tags ? metadata.tags.join(', ') : 'N/A'}
`
      console.log('📊 Including YouTube metadata in analysis:', {
        title: metadata.title,
        publishedAt: metadata.publishedAt,
        description: metadata.description?.substring(0, 100) + '...',
        channelTitle: metadata.channelTitle
      })
    }

    // Vertex AI requires a text part alongside a file part in multimodal requests.
    const textPart = {
      text: `Please analyze the provided video advertisement based on the detailed system instructions.${metadataContext}`
    };

    // Handle streaming vs non-streaming requests
    if (wantsStream) {
      console.log('🌊 Starting streaming response...')

      // Create SSE response with proper streaming
      const encoder = new TextEncoder()

      const stream = new ReadableStream({
        async start(controller) {
          const sendMessage = (type: string, content?: string, slug?: string) => {
            try {
              // Ensure content is properly escaped for JSON
              const safeContent = content ? content.replace(/\\/g, '\\\\').replace(/"/g, '\\"').replace(/\n/g, '\\n').replace(/\r/g, '\\r') : undefined
              const message = JSON.stringify({ type, content: safeContent, slug })
              const data = `data: ${message}\n\n`
              console.log(`📤 Sending stream message:`, { type, content: content?.substring(0, 50) + '...', slug })
              controller.enqueue(encoder.encode(data))
            } catch (e) {
              console.warn(`❌ Could not send stream message (type: ${type}), client likely disconnected:`, e)
              // Also log the problematic content for debugging
              console.warn(`❌ Problematic message content:`, { type, content: content?.substring(0, 200), slug })
            }
          }

          try {
            console.log('🌊 Sending initial thinking message...')
            sendMessage('thinking', 'Initializing Vertex AI analysis...\n')
            await new Promise(resolve => setTimeout(resolve, 500))

            sendMessage('thinking', 'Loading AI model and processing video...\n')
            await new Promise(resolve => setTimeout(resolve, 300))

            console.log('🤖 Starting Vertex AI generateContent with Google Search grounding...')
            const result = await model.generateContent({
              contents: [{ role: "user", parts: [textPart, videoPart] }],
              systemInstruction
              // generationConfig and tools are already configured in the model
            });

            console.log('🌊 Sending processing message...')
            sendMessage('thinking', 'Analyzing video content and generating insights...\n')
            await new Promise(resolve => setTimeout(resolve, 300))

            const response = result.response;
            console.log('✅ Received response from Vertex AI')

            // Extract grounding metadata (actual source URLs) - STREAMING VERSION
            console.log('🔍 Extracting grounding metadata from streaming response...')
            const groundingMetadata = response.candidates?.[0]?.groundingMetadata
            let actualSourceUrls = []
            
            if (groundingMetadata) {
              console.log('📚 Processing grounding metadata in streaming mode')
              
              // Extract URLs from grounding chunks
              if (groundingMetadata.groundingChunks) {
                console.log(`🔗 Found ${groundingMetadata.groundingChunks.length} grounding chunks (streaming)`)
                for (const chunk of groundingMetadata.groundingChunks) {
                  if (chunk.web && chunk.web.uri) {
                    console.log(`✅ Adding grounded source URL (streaming): ${chunk.web.uri}`)
                    actualSourceUrls.push({
                      url: chunk.web.uri,
                      title: chunk.web.title || 'Grounded Source',
                      reliability_score: 9, // High reliability for grounded sources
                      source_type: 'grounded_source'
                    })
                  }
                }
              }
              
              console.log(`🔗 Extracted ${actualSourceUrls.length} actual source URLs from grounding (streaming)`)
            } else {
              console.log('📚 No grounding metadata available in streaming response')
            }

            if (!response.candidates?.length || !response.candidates[0].content?.parts[0]?.text) {
              sendMessage('error', 'Invalid response structure from Vertex AI')
              controller.close()
              return
            }

            const analysisText = response.candidates[0].content.parts[0].text;
            console.log('🌊 Sending parsing message...')
            sendMessage('thinking', 'Parsing analysis results and structuring data...\n')
            await new Promise(resolve => setTimeout(resolve, 400))

            // Parse the analysis data with enhanced error handling
            let analysisData;
            try {
              console.log('🔍 Parsing analysis text, length:', analysisText.length);
              console.log('🔍 Text preview (first 500 chars):', analysisText.substring(0, 500));
              console.log('🔍 Text preview (last 500 chars):', analysisText.substring(analysisText.length - 500));

              // Check if response appears to be truncated
              const lastChar = analysisText.trim().slice(-1);
              const endsWithValidJson = lastChar === '}' || lastChar === ']';
              if (!endsWithValidJson) {
                console.log('⚠️ Response appears to be truncated - does not end with valid JSON character:', lastChar);
              }
              
              try {
                analysisData = JSON.parse(analysisText);
                console.log('✅ Successfully parsed analysis JSON directly');
              } catch (directParseError) {
                console.log('⚠️ Direct parse failed, attempting to clean...', directParseError instanceof Error ? directParseError.message : 'Unknown error');
                
                // More aggressive cleaning for markdown code blocks
                let cleanJsonText = analysisText
                  .replace(/^```json\s*/g, '')       // Remove opening ```json
                  .replace(/^```\s*/g, '')           // Remove opening ```
                  .replace(/```\s*$/g, '')           // Remove closing ```
                  .replace(/^`json\s*/gm, '')        // Remove single backtick json markers
                  .replace(/`\s*$/gm, '')            // Remove trailing single backticks
                  .trim();

                console.log('🔍 After initial cleaning, length:', cleanJsonText.length);
                
                const firstBrace = cleanJsonText.indexOf('{');
                const lastBrace = cleanJsonText.lastIndexOf('}');

                if (firstBrace !== -1 && lastBrace !== -1 && lastBrace > firstBrace) {
                  cleanJsonText = cleanJsonText.substring(firstBrace, lastBrace + 1);
                  console.log('🔍 After brace extraction, length:', cleanJsonText.length);
                }

                // Additional cleaning for common JSON issues
                cleanJsonText = cleanJsonText
                  .replace(/,(\s*[}\]])/g, '$1') // Remove trailing commas
                  .replace(/([^\\])\\(?!["\\/bfnrt]|u[0-9a-fA-F]{4})/g, '$1\\\\') // Fix unescaped backslashes
                  .replace(/\n/g, '\\n') // Escape newlines
                  .replace(/\r/g, '\\r') // Escape carriage returns
                  .replace(/\t/g, '\\t') // Escape tabs

                console.log('🔍 After additional cleaning, length:', cleanJsonText.length);
                console.log('🔍 Cleaned text preview (around potential error):', cleanJsonText.substring(13000, 13100));

                try {
                  analysisData = JSON.parse(cleanJsonText);
                  console.log('✅ Successfully parsed analysis JSON after cleaning');
                } catch (secondParseError) {
                  console.log('❌ Second parse attempt failed, trying to find valid JSON object...', secondParseError instanceof Error ? secondParseError.message : 'Unknown error');
                  
                  // Try to find the largest valid JSON object within the text
                  let bestJsonMatch = null;
                  let maxLength = 0;
                  
                  // Look for JSON objects with balanced braces
                  for (let i = 0; i < cleanJsonText.length; i++) {
                    if (cleanJsonText[i] === '{') {
                      let braceCount = 1;
                      let j = i + 1;
                      
                      while (j < cleanJsonText.length && braceCount > 0) {
                        if (cleanJsonText[j] === '{') braceCount++;
                        else if (cleanJsonText[j] === '}') braceCount--;
                        j++;
                      }
                      
                      if (braceCount === 0) {
                        const candidate = cleanJsonText.substring(i, j);
                        try {
                          const parsed = JSON.parse(candidate);
                          if (candidate.length > maxLength) {
                            bestJsonMatch = parsed;
                            maxLength = candidate.length;
                          }
                        } catch (e) {
                          // Continue searching
                        }
                      }
                    }
                  }
                  
                  if (bestJsonMatch) {
                    console.log('✅ Found valid JSON object with length:', maxLength);
                    analysisData = bestJsonMatch;
                  } else {
                    throw secondParseError; // Re-throw if we couldn't recover
                  }
                }
              }

              // Validate the final analysis data structure
              console.log('🔍 Validating analysis data structure...');
              if (!analysisData || typeof analysisData !== 'object') {
                throw new Error('Analysis data is not a valid object');
              }
              
              // Ensure minimum required structure exists
              if (!analysisData.summary) {
                analysisData.summary = { overall_sentiment: 5, key_insights: ["Analysis structure incomplete"], emotional_tone: "neutral" };
              }
              if (!analysisData.citations) {
                analysisData.citations = [];
              }
              
              console.log('✅ Analysis data structure validated');

              // Inject actual grounded source URLs into citations - STREAMING VERSION
              if (actualSourceUrls.length > 0) {
                console.log(`📚 Adding ${actualSourceUrls.length} grounded sources to citations (streaming)...`)
                
                // Ensure citations array exists
                if (!analysisData.citations) {
                  analysisData.citations = []
                }
                
                // Add grounded sources to citations (with validation and URL verification - streaming)
                for (const source of actualSourceUrls) {
                  // Validate and potentially correct the URL
                  const validatedUrl = await validateAndCorrectUrl(source.url, source.title)
                  
                  // Check if this URL (or corrected URL) already exists in citations
                  const existingCitation = analysisData.citations.find((citation: any) => 
                    citation.url === validatedUrl || 
                    citation.url === source.url ||
                    citation.title?.toLowerCase().includes(source.title?.toLowerCase().substring(0, 20) || '')
                  )
                  
                  if (!existingCitation) {
                    analysisData.citations.push({
                      url: validatedUrl,
                      title: source.title,
                      publication_date: new Date().toISOString().split('T')[0],
                      validated: validatedUrl !== source.url, // Mark as validated if URL was corrected
                      reliability_score: source.reliability_score,
                      source_type: source.source_type,
                      last_checked: new Date().toISOString(),
                      original_grounded_url: validatedUrl !== source.url ? source.url : undefined
                    })
                    console.log(`✅ Added grounded source (streaming): ${source.title}${validatedUrl !== source.url ? ' (URL corrected)' : ''}`)
                  } else {
                    console.log(`🔄 Updated existing citation with grounding data (streaming): ${source.title}`)
                    existingCitation.validated = true
                    existingCitation.reliability_score = source.reliability_score
                    existingCitation.source_type = source.source_type
                    existingCitation.last_checked = new Date().toISOString()
                    if (validatedUrl !== source.url) {
                      existingCitation.url = validatedUrl
                      existingCitation.original_grounded_url = source.url
                    }
                  }
                }
                
                console.log(`📚 Final citations count (streaming): ${analysisData.citations.length}`)
              } else {
                console.log('📚 No grounded sources to add to citations (streaming)')
              }

            } catch (parseError: any) {
              console.error('❌ JSON parse error in streaming response:', {
                error: parseError.message,
                position: parseError.message.match(/position (\d+)/)?.[1],
                analysisTextLength: analysisText.length,
                textPreview: analysisText.substring(0, 200),
                textAroundError: analysisText.substring(
                  Math.max(0, (parseError.message.match(/position (\d+)/)?.[1] || 0) - 50),
                  Math.min(analysisText.length, (parseError.message.match(/position (\d+)/)?.[1] || 0) + 50)
                ),
                rawAnalysisText: analysisText // Full text for debugging
              });
              
              // Try to salvage any valid JSON from the response
              console.log('🔧 Attempting to salvage JSON from malformed response...');
              try {
                // Look for the largest JSON object in the response
                let bestMatch = null;
                let maxLength = 0;
                
                // Try to find the largest complete JSON object (greedy approach)
                const firstBrace = analysisText.indexOf('{');
                const lastBrace = analysisText.lastIndexOf('}');
                
                if (firstBrace !== -1 && lastBrace !== -1 && lastBrace > firstBrace) {
                  const potentialJson = analysisText.substring(firstBrace, lastBrace + 1);
                  try {
                    const parsed = JSON.parse(potentialJson);
                    if (typeof parsed === 'object' && parsed !== null) {
                      bestMatch = parsed;
                      maxLength = potentialJson.length;
                      console.log(`✅ Found complete JSON object with length: ${maxLength}`);
                    }
                  } catch (e) {
                    console.log('⚠️ Complete JSON extraction failed, trying smaller objects...');
                    
                    // Fallback to the original approach but with greedy matching
                    const jsonMatches = analysisText.match(/\{[\s\S]*\}/g) || [];
                    console.log(`🔍 Found ${jsonMatches.length} potential JSON objects`);
                    
                    for (const match of jsonMatches) {
                      try {
                        const parsed = JSON.parse(match);
                        if (match.length > maxLength && typeof parsed === 'object' && parsed !== null) {
                          bestMatch = parsed;
                          maxLength = match.length;
                        }
                      } catch (e) {
                        // Continue searching
                      }
                    }
                  }
                } else {
                  console.log('⚠️ No JSON braces found in response');
                }
                
                if (bestMatch) {
                  console.log('✅ Salvaged valid JSON object, proceeding with analysis');
                  analysisData = bestMatch;
                } else {
                  console.log('❌ Could not salvage any valid JSON, creating enhanced fallback analysis');

                  // Try to extract gut_reaction from the raw text for better fallback
                  let extractedGutReaction = "Analysis could not be fully processed due to formatting issues";
                  const gutReactionMatch = analysisText.match(/"gut_reaction":\s*"([^"]+)"/);
                  if (gutReactionMatch) {
                    extractedGutReaction = gutReactionMatch[1];
                    console.log('✅ Extracted gut_reaction from partial response:', extractedGutReaction.substring(0, 100));
                  }

                  // Create a more comprehensive fallback analysis
                  analysisData = {
                    metadata: {
                      ad_title: "Analysis Incomplete",
                      brand: "Unknown",
                      parsing_status: "partial"
                    },
                    executive_briefing: {
                      gut_reaction: extractedGutReaction,
                      the_one_thing_that_matters: "This analysis was partially processed. Please retry the analysis for complete results.",
                      scorecard: {
                        overall_impact_score: { score: 5, justification: "Analysis incomplete" },
                        clarity_of_message: { score: 5, justification: "Analysis incomplete" },
                        brand_distinctiveness: { score: 5, justification: "Analysis incomplete" },
                        memorability_and_hook: { score: 5, justification: "Analysis incomplete" },
                        emotional_resonance: { score: 5, justification: "Analysis incomplete" },
                        business_objective_fit: { score: 5, justification: "Analysis incomplete" },
                        call_to_action: { score: 5, justification: "Analysis incomplete" }
                      }
                    },
                    core_analysis: {
                      essay: "The analysis was truncated due to response length limits. The AI response was " + analysisText.length + " characters long and appears to have been cut off during generation."
                    },
                    diagnosis: {
                      primary_pitfall: {
                        category: "Technical Issue",
                        description: "Analysis response was truncated",
                        improvement_signal: "Retry analysis or contact support"
                      }
                    },
                    citations: [],
                    parsing_note: "This analysis was generated with an enhanced fallback due to JSON parsing issues. Original response length: " + analysisText.length + " characters."
                  };
                  console.log('✅ Created enhanced fallback analysis structure with extracted content');
                }
              } catch (salvageError) {
                console.error('❌ Failed to salvage JSON:', salvageError);
                sendMessage('error', `Failed to parse analysis data: ${parseError.message}`)
                controller.close()
                return
              }
            }

            console.log('🌊 Sending saving message...')
            sendMessage('thinking', 'Saving analysis to database...\n')
            await new Promise(resolve => setTimeout(resolve, 300))

            // Save to database (same logic as before)
            // isUUID already declared above, reuse it

            console.log('🔍 [STREAMING] Fetching analysis for validation and status check...', { id, isUUID, matchField: isUUID ? 'id' : 'slug' })

            const { data: analysisForUpdate, error: fetchError } = await supabase
              .from('ad_analyses')
              .select('id, user_id, slug, status, analysis_completed_at')
              .eq(isUUID ? 'id' : 'slug', id)
              .eq('user_id', user.id)
              .single()

            if (fetchError || !analysisForUpdate) {
              console.error('❌ [STREAMING] Failed to fetch analysis for update:', fetchError)
              sendMessage('error', 'Analysis not found or access denied')
              controller.close()
              return
            }

            console.log('✅ [STREAMING] Found analysis for update:', { 
              analysisId: analysisForUpdate.id, 
              slug: analysisForUpdate.slug, 
              currentStatus: analysisForUpdate.status, 
              completedAt: analysisForUpdate.analysis_completed_at 
            })

            // Prevent re-triggering if analysis is already completed
            if (analysisForUpdate.status === 'completed' || analysisForUpdate.status === 'generated') {
              console.log('⚠️ [STREAMING] Analysis already completed, preventing re-trigger:', {
                currentStatus: analysisForUpdate.status,
                completedAt: analysisForUpdate.analysis_completed_at
              })
              sendMessage('done', 'Analysis already completed', analysisForUpdate.slug)
              controller.close()
              return
            }

            // Determine if content should be public based on suitability check
            const shouldPublish = (() => {
              // Backward compatibility: if no suitability field, default to public
              if (!analysisData?.suitability) {
                console.log('📝 No suitability field found - defaulting to public (backward compatibility)')
                return true
              }
              
              // If suitability indicates it's advertising content, make it public
              if (analysisData.suitability.is_advertising === true) {
                console.log('📝 Content is advertising - making public')
                return true
              } else {
                console.log('📝 Content is not advertising - keeping private')  
                return false
              }
            })()

            const { data: updatedData, error: updateError } = await supabase
            .from('ad_analyses')
            .update({
              marketing_analysis: analysisData,
              status: 'completed',
              analysis_completed_at: new Date().toISOString(),
              is_public: shouldPublish
            })
            .eq('id', analysisForUpdate.id)
            .select()
            .single();

            if (updateError || !updatedData) {
              sendMessage('error', 'Failed to save analysis to database')
              controller.close()
              return
            }

            console.log('🌊 Sending completion message...')
            sendMessage('thinking', 'Analysis completed successfully!\n')
            await new Promise(resolve => setTimeout(resolve, 200))
            
            console.log('✅ Streaming complete, sending done message with slug:', updatedData.slug)
            sendMessage('done', 'Finalizing...', updatedData.slug)
            
            // Give client time to process the done message
            await new Promise(resolve => setTimeout(resolve, 100))
            controller.close()

          } catch (error: any) {
            console.error('❌ Streaming error:', error)
            sendMessage('error', error.message || 'An unknown error occurred')
            controller.close()
          }
        }
      })

      // Return streaming response
      return new Response(stream, {
        headers: {
          'Content-Type': 'text/event-stream',
          'Cache-Control': 'no-cache',
          'Connection': 'keep-alive',
          'Access-Control-Allow-Origin': '*',
          'Access-Control-Allow-Methods': 'POST',
          'Access-Control-Allow-Headers': 'Content-Type',
        },
      })
    }

    // Non-streaming path with Google Search grounding
    console.log('🤖 Calling Vertex AI with Google Search grounding...')
    const result = await model.generateContent({
        contents: [{ role: "user", parts: [textPart, videoPart] }],
        systemInstruction
        // generationConfig and tools are already configured in the model
    });

    const response = result.response;

    // Extract grounding metadata (actual source URLs) if search grounding was used
    console.log('🔍 Extracting grounding metadata from Vertex AI response...')
    const groundingMetadata = response.candidates?.[0]?.groundingMetadata
    let actualSourceUrls = []
    
    if (groundingMetadata) {
      console.log('📚 Processing grounding metadata:', JSON.stringify(groundingMetadata, null, 2))
      
      // Extract URLs from grounding chunks
      if (groundingMetadata.groundingChunks) {
        console.log(`🔗 Found ${groundingMetadata.groundingChunks.length} grounding chunks`)
        for (const chunk of groundingMetadata.groundingChunks) {
          console.log('🔍 Processing chunk:', JSON.stringify(chunk, null, 2))
          if (chunk.web && chunk.web.uri) {
            console.log(`✅ Adding grounded source URL: ${chunk.web.uri}`)
            actualSourceUrls.push({
              url: chunk.web.uri,
              title: chunk.web.title || 'Grounded Source',
              reliability_score: 9, // High reliability for grounded sources
              source_type: 'grounded_source'
            })
          }
        }
      } else {
        console.log('⚠️ No groundingChunks found in metadata')
      }
      
      // Also check web searches if available
      if (groundingMetadata.webSearchQueries) {
        console.log('🔍 Web search queries found:', groundingMetadata.webSearchQueries)
      }
      
      console.log(`🔗 Extracted ${actualSourceUrls.length} actual source URLs from grounding`)
    } else {
      console.log('📚 No grounding metadata available (search grounding may not have been triggered)')
    }

    // The response from the model is in the 'candidates' array.
    // We access the text from the first candidate's content parts.
    if (!response.candidates?.length || !response.candidates[0].content?.parts[0]?.text) {
      console.error("❌ Invalid response structure from Vertex AI:", JSON.stringify(response, null, 2));
      throw new Error("Received an invalid or empty text response from Vertex AI.");
    }

    const analysisText = response.candidates[0].content.parts[0].text;

    console.log('🔵 Raw response from Vertex AI:\n', analysisText);

    let analysisData;
    try {
      console.log('🔧 Original response length:', analysisText.length);
      console.log('🔧 Response starts with:', analysisText.substring(0, 100));
      console.log('🔧 Response ends with:', analysisText.substring(analysisText.length - 100));
      
      // First, try to parse as-is (most likely scenario)
      try {
        analysisData = JSON.parse(analysisText);
        console.log('✅ Successfully parsed JSON without cleaning');
      } catch (directParseError) {
        console.log('⚠️ Direct parse failed, trying with cleaning...');
        
        // More aggressive cleaning for markdown code blocks (non-streaming)
        let cleanJsonText = analysisText
          .replace(/^```json\s*/g, '')       // Remove opening ```json
          .replace(/^```\s*/g, '')           // Remove opening ```
          .replace(/```\s*$/g, '')           // Remove closing ```
          .replace(/^`json\s*/gm, '')        // Remove single backtick json markers
          .replace(/`\s*$/gm, '')            // Remove trailing single backticks
          .trim();

        console.log('🔧 After cleaning length:', cleanJsonText.length);
        console.log('🔧 After cleaning starts with:', cleanJsonText.substring(0, 100));
        console.log('🔧 After cleaning ends with:', cleanJsonText.substring(cleanJsonText.length - 100));

        // Try to find JSON object boundaries more carefully
        const firstBrace = cleanJsonText.indexOf('{');
        const lastBrace = cleanJsonText.lastIndexOf('}');

        if (firstBrace !== -1 && lastBrace !== -1 && lastBrace > firstBrace) {
          cleanJsonText = cleanJsonText.substring(firstBrace, lastBrace + 1);
          console.log('🔧 After brace extraction length:', cleanJsonText.length);
          console.log('🔧 After brace extraction starts with:', cleanJsonText.substring(0, 100));
          console.log('🔧 After brace extraction ends with:', cleanJsonText.substring(cleanJsonText.length - 100));
        }

        analysisData = JSON.parse(cleanJsonText);
        console.log('✅ Successfully parsed JSON after cleaning');
      }

      // Inject actual grounded source URLs into citations
      if (actualSourceUrls.length > 0) {
        console.log(`📚 Adding ${actualSourceUrls.length} grounded sources to citations...`)
        
        // Ensure citations array exists
        if (!analysisData.citations) {
          analysisData.citations = []
        }
        
        // Add grounded sources to citations (with validation and URL verification)
        for (const source of actualSourceUrls) {
          // Validate and potentially correct the URL
          const validatedUrl = await validateAndCorrectUrl(source.url, source.title)
          
          // Check if this URL (or corrected URL) already exists in citations
          const existingCitation = analysisData.citations.find((citation: any) => 
            citation.url === validatedUrl || 
            citation.url === source.url ||
            citation.title?.toLowerCase().includes(source.title?.toLowerCase().substring(0, 20) || '')
          )
          
          if (!existingCitation) {
            analysisData.citations.push({
              url: validatedUrl,
              title: source.title,
              publication_date: new Date().toISOString().split('T')[0],
              validated: validatedUrl !== source.url, // Mark as validated if URL was corrected
              reliability_score: source.reliability_score,
              source_type: source.source_type,
              last_checked: new Date().toISOString(),
              original_grounded_url: validatedUrl !== source.url ? source.url : undefined
            })
            console.log(`✅ Added grounded source: ${source.title}${validatedUrl !== source.url ? ' (URL corrected)' : ''}`)
          } else {
            console.log(`🔄 Updated existing citation with grounding data: ${source.title}`)
            existingCitation.validated = true
            existingCitation.reliability_score = source.reliability_score
            existingCitation.source_type = source.source_type
            existingCitation.last_checked = new Date().toISOString()
            if (validatedUrl !== source.url) {
              existingCitation.url = validatedUrl
              existingCitation.original_grounded_url = source.url
            }
          }
        }
        
        console.log(`📚 Final citations count: ${analysisData.citations.length}`)
      } else {
        console.log('📚 No grounded sources to add to citations')
      }
      
    } catch (parseError: any) {
      console.error('❌ Failed to parse JSON from Vertex AI response (non-streaming):', {
        parseErrorMessage: parseError.message,
        rawResponseLength: analysisText.length,
        rawResponsePreview: analysisText.substring(0, 500),
        rawResponseEnd: analysisText.substring(analysisText.length - 500),
      });

      // Try to extract gut_reaction for better error response
      let extractedGutReaction = "Analysis could not be processed due to formatting issues";
      const gutReactionMatch = analysisText.match(/"gut_reaction":\s*"([^"]+)"/);
      if (gutReactionMatch) {
        extractedGutReaction = gutReactionMatch[1];
        console.log('✅ Extracted gut_reaction from failed non-streaming response');
      }

      return NextResponse.json(
        {
          error: `Failed to parse analysis data from Vertex AI response: ${parseError.message}`,
          responseLength: analysisText.length,
          extractedGutReaction: extractedGutReaction,
          suggestion: "The AI response may have been truncated. Try reducing the analysis scope or retry the request."
        },
        { status: 500 }
      );
    }

    // ✅ CRITICAL: UUID/Slug resolution pattern to prevent database errors
    // isUUID already declared above, reuse it
    console.log('🔧 ID resolution:', { id, isUUID, fieldToMatch: isUUID ? 'id' : 'slug' })
    
    // First get the analysis to ensure it exists and verify ownership
    console.log('🔍 Fetching analysis for validation and status check...', { id, isUUID, matchField: isUUID ? 'id' : 'slug' })
    
    const { data: analysisForValidation, error: fetchError } = await supabase
      .from('ad_analyses')
      .select('id, user_id, slug, status, analysis_completed_at')
      .eq(isUUID ? 'id' : 'slug', id)
      .eq('user_id', user.id)  // ← Verify ownership
      .single()

    if (fetchError || !analysisForValidation) {
      console.error('❌ Failed to fetch analysis for update:', fetchError)
      return NextResponse.json(
        { error: `Analysis not found or access denied: ${fetchError?.message || 'Record not found or user does not own this analysis'}` },
        { status: 404 }
      )
    }

    console.log('✅ Found analysis for update:', { 
      analysisId: analysisForValidation.id, 
      slug: analysisForValidation.slug, 
      currentStatus: analysisForValidation.status, 
      completedAt: analysisForValidation.analysis_completed_at 
    })

    // Prevent re-triggering if analysis is already completed
    if (analysisForValidation.status === 'completed' || analysisForValidation.status === 'generated') {
      console.log('⚠️ Analysis already completed, preventing re-trigger:', {
        currentStatus: analysisForValidation.status,
        completedAt: analysisForValidation.analysis_completed_at
      })
      return NextResponse.json(
        { 
          message: 'Analysis already completed',
          analysis_id: analysisForValidation.id,
          slug: analysisForValidation.slug,
          status: analysisForValidation.status,
          already_completed: true
        },
        { status: 200 }
      )
    }

    // Determine if content should be public based on suitability check
    const shouldPublish = (() => {
      // Backward compatibility: if no suitability field, default to public
      if (!analysisData?.suitability) {
        console.log('📝 No suitability field found - defaulting to public (backward compatibility)')
        return true
      }
      
      // If suitability indicates it's advertising content, make it public
      if (analysisData.suitability.is_advertising === true) {
        console.log('📝 Content is advertising - making public')
        return true
      } else {
        console.log('📝 Content is not advertising - keeping private')  
        return false
      }
    })()

    // Now update using the actual UUID
    const { data: updatedData, error: updateError } = await supabase
      .from('ad_analyses')
      .update({
        marketing_analysis: analysisData,
        status: 'completed',
        analysis_completed_at: new Date().toISOString(),
        is_public: shouldPublish
      })
      .eq('id', analysisForValidation.id)  // ← Use the actual UUID from the fetched analysis
      .select()
      .single();

    if (updateError || !updatedData) {
      console.error('❌ Failed to save analysis to Supabase:', updateError);
      // If the update fails, we must set the status to 'failed' to prevent client-side loops
      await supabase
        .from('ad_analyses')
        .update({ status: 'failed' })
        .eq('id', analysisForValidation.id);  // ← Use the actual UUID from the fetched analysis
      return NextResponse.json(
        { error: `Failed to update database: ${updateError?.message || 'Record not found or update failed.'}` },
        { status: 500 }
      );
    }

    console.log('✅ Successfully saved analysis to database for ID:', updatedData.id);

    return NextResponse.json(
      { 
        message: 'Vertex analysis completed and saved successfully',
        analysis_id: analysis.id,  // ← Use the actual UUID
        slug: analysis.slug,
        status: 'completed',
        data: analysisData
      },
      { status: 200 }
    )
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'An unknown error occurred';
    console.error('❌ Error in trigger-vertex-analysis:', {
      errorMessage,
      errorObject: error,
    });
    
    return NextResponse.json(
      { error: `An internal server error occurred: ${errorMessage}` },
      { status: 500 }
    )
  }
}