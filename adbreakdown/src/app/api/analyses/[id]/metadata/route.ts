import { NextRequest, NextResponse } from 'next/server'
import { auth } from '@clerk/nextjs/server'
import { createServerSupabaseClient } from '@/lib/supabase'

// GET /api/analyses/{id}/metadata - Get YouTube metadata and extended info
export async function GET(req: NextRequest, { params }: { params: Promise<{ id: string }> }) {
  try {
    const { id } = await params
    
    // Get authenticated user
    const { userId } = await auth()
    if (!userId) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const supabase = createServerSupabaseClient()

    // Get user from database
    const { data: user, error: userError } = await supabase
      .from('users')
      .select('id')
      .eq('clerk_id', userId)
      .single()

    if (userError || !user) {
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      )
    }

    // Check if id is a UUID or a slug
    const isUUID = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i.test(id)
    
    // Get analysis with metadata fields
    const { data: analysis, error: analysisError } = await supabase
      .from('ad_analyses')
      .select(`
        id,
        youtube_url,
        youtube_video_id,
        transcript,
        summary,
        video_info,
        marketing_analysis,
        visual_appeal,
        audio_quality,
        color_palette,
        sentiment_timeline,
        target_demographics,
        target_interests,
        target_behaviors,
        music_mood,
        voice_tone
      `)
      .eq(isUUID ? 'id' : 'slug', id)
      .eq('user_id', user.id)
      .single()

    if (analysisError || !analysis) {
      return NextResponse.json(
        { error: 'Analysis not found or access denied' },
        { status: 404 }
      )
    }

    // Fetch YouTube metadata and save it to database
    let youtubeMetadata = null
    
    if (analysis.youtube_video_id) {
      try {
        const ytApiKey = process.env.YOUTUBE_API_KEY || process.env.NEXT_PUBLIC_YOUTUBE_API_KEY
        if (ytApiKey) {
          console.log('🎬 Fetching YouTube metadata for video:', analysis.youtube_video_id)
          
          const response = await fetch(
            `https://www.googleapis.com/youtube/v3/videos?id=${analysis.youtube_video_id}&part=snippet,statistics,contentDetails&key=${ytApiKey}`
          )
          
          if (response.ok) {
            const data = await response.json()
            if (data.items && data.items.length > 0) {
              const video = data.items[0]
              const snippet = video.snippet
              const statistics = video.statistics
              const contentDetails = video.contentDetails
              
              // Parse duration
              const parseDuration = (duration: string) => {
                const match = duration.match(/PT(?:(\d+)H)?(?:(\d+)M)?(?:(\d+)S)?/)
                if (!match) return '0:00'
                
                const hours = parseInt(match[1] || '0')
                const minutes = parseInt(match[2] || '0')
                const seconds = parseInt(match[3] || '0')
                
                if (hours > 0) {
                  return `${hours}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`
                }
                return `${minutes}:${seconds.toString().padStart(2, '0')}`
              }
              
              // Fetch channel information for custom URL
              let channelUrl = null
              let channelCustomUrl = null
              
              if (snippet.channelId) {
                try {
                  const channelResponse = await fetch(
                    `https://www.googleapis.com/youtube/v3/channels?id=${snippet.channelId}&part=snippet&key=${ytApiKey}`
                  )
                  
                  if (channelResponse.ok) {
                    const channelData = await channelResponse.json()
                    if (channelData.items && channelData.items.length > 0) {
                      const channel = channelData.items[0]
                      channelCustomUrl = channel.snippet.customUrl
                      
                      // Construct channel URL - prefer custom URL if available
                      if (channelCustomUrl) {
                        channelUrl = `https://www.youtube.com/@${channelCustomUrl.replace('@', '')}`
                      } else {
                        channelUrl = `https://www.youtube.com/channel/${snippet.channelId}`
                      }
                    }
                  }
                } catch (channelError) {
                  console.warn('Failed to fetch channel information:', channelError)
                }
              }

              youtubeMetadata = {
                title: snippet.title || 'Untitled Video',
                description: snippet.description || '',
                channelTitle: snippet.channelTitle || 'Unknown Channel',
                channelId: snippet.channelId,
                channelUrl: channelUrl,
                channelCustomUrl: channelCustomUrl,
                publishedAt: snippet.publishedAt,
                viewCount: statistics.viewCount || '0',
                likeCount: statistics.likeCount || '0',
                commentCount: statistics.commentCount || '0',
                tags: snippet.tags || [],
                categoryId: snippet.categoryId,
                defaultLanguage: snippet.defaultLanguage,
                duration: parseDuration(contentDetails.duration),
                definition: contentDetails.definition || 'sd',
                fetchedAt: new Date().toISOString()
              }
              
              console.log('✅ Successfully fetched YouTube metadata')
              
              // Save YouTube metadata to database
              try {
                console.log('💾 Saving YouTube metadata to database...')
                const { error: updateError } = await supabase
                  .from('ad_analyses')
                  .update({
                    youtube_metadata: youtubeMetadata,
                    updated_at: new Date().toISOString()
                  })
                  .eq('id', analysis.id)

                if (updateError) {
                  console.warn('⚠️ Failed to save YouTube metadata to database:', updateError)
                  console.warn('⚠️ Update error details:', JSON.stringify(updateError, null, 2))
                  
                  // Check if it's a column not found error
                  if (updateError.message?.includes('column') && updateError.message?.includes('youtube_metadata')) {
                    console.error('❌ Column youtube_metadata does not exist in ad_analyses table')
                  }
                } else {
                  console.log('✅ YouTube metadata saved to database')
                }
              } catch (saveError) {
                console.warn('❌ Error saving YouTube metadata:', saveError)
              }
            }
          } else {
            console.warn('⚠️ YouTube API response not OK:', response.status)
          }
        } else {
          console.warn('⚠️ YouTube API key not configured')
        }
      } catch (error) {
        console.warn('❌ Failed to fetch YouTube metadata:', error)
      }
    }

    return NextResponse.json({
      ...analysis,
      youtubeMetadata
    })
  } catch (error) {
    console.error('Error in GET /api/analyses/[id]/metadata:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// PATCH /api/analyses/{id}/metadata - Update analysis metadata (admin only)
export async function PATCH(
  req: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params
    const { userId } = await auth()
    
    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const supabase = createServerSupabaseClient()

    // Get user to check admin status
    const { data: user, error: userError } = await supabase
      .from('users')
      .select('email, clerk_id, id')
      .eq('clerk_id', userId)
      .single()

    if (userError || !user) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 })
    }

    // Check if user is admin based on ADMIN_USER_IDS
    // In development, everyone is admin
    const adminUserIds = process.env.ADMIN_USER_IDS?.split(',') || []
    const isAdmin = process.env.NODE_ENV === 'development' || 
                    adminUserIds.includes(userId)
    
    if (!isAdmin) {
      return NextResponse.json({ error: 'Admin access required' }, { status: 403 })
    }

    const { field, value } = await req.json()

    if (!field || !value) {
      return NextResponse.json({ error: 'Field and value are required' }, { status: 400 })
    }

    // Check if id is a UUID or a slug
    const isUUID = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i.test(id)

    // Get current analysis data
    const { data: analysis, error: fetchError } = await supabase
      .from('ad_analyses')
      .select('marketing_analysis')
      .eq(isUUID ? 'id' : 'slug', id)
      .single()

    if (fetchError || !analysis) {
      return NextResponse.json({ error: 'Analysis not found' }, { status: 404 })
    }

    // Parse the existing marketing analysis
    let marketingAnalysis
    try {
      marketingAnalysis = typeof analysis.marketing_analysis === 'string' 
        ? JSON.parse(analysis.marketing_analysis)
        : analysis.marketing_analysis || {}
    } catch (parseError) {
      return NextResponse.json({ error: 'Invalid marketing analysis data' }, { status: 500 })
    }

    // Update the metadata field
    if (!marketingAnalysis.metadata) {
      marketingAnalysis.metadata = {}
    }
    
    marketingAnalysis.metadata[field] = value

    // Update the database
    const { error: updateError } = await supabase
      .from('ad_analyses')
      .update({
        marketing_analysis: JSON.stringify(marketingAnalysis),
        updated_at: new Date().toISOString()
      })
      .eq(isUUID ? 'id' : 'slug', id)

    if (updateError) {
      console.error('Database update error:', updateError)
      return NextResponse.json({ error: 'Failed to update metadata' }, { status: 500 })
    }

    return NextResponse.json({ 
      success: true,
      field,
      value,
      message: 'Metadata updated successfully'
    })

  } catch (error) {
    console.error('API error:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}