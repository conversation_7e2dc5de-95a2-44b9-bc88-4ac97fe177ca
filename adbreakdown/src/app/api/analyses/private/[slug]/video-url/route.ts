import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { createServerSupabaseClient } from '@/lib/supabase';
import { Storage } from '@google-cloud/storage';

// Initialize Google Cloud Storage
let storage: Storage;

try {
  const gcsCredentials = JSON.parse(process.env.GOOGLE_CLOUD_SERVICE_ACCOUNT_KEY || '{}');
  storage = new Storage({
    projectId: process.env.GOOGLE_PROJECT_ID,
    credentials: gcsCredentials,
  });
} catch (error) {
  console.error('❌ Failed to initialize GCS storage:', error);
  storage = new Storage({
    projectId: process.env.GOOGLE_PROJECT_ID,
  });
}

export async function GET(req: NextRequest, { params }: { params: Promise<{ slug: string }> }) {
  const { userId } = await auth();
  if (!userId) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }

  try {
    const { slug } = await params;
    const supabase = createServerSupabaseClient();
    
    // Get user from database
    const { data: user, error: userError } = await supabase
      .from('users')
      .select('id')
      .eq('clerk_id', userId)
      .single();

    if (userError || !user) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 });
    }

    // Get the private analysis
    const { data: analysis, error: analysisError } = await supabase
      .from('private_analyses')
      .select('youtube_url, gcs_video_uri, analysis_type, user_id')
      .eq('slug', slug)
      .eq('user_id', user.id)
      .single();

    if (analysisError || !analysis) {
      return NextResponse.json({ error: 'Analysis not found' }, { status: 404 });
    }

    // Determine which URI to use - prioritize gcs_video_uri for downloaded videos
    let gcsUri: string | null = null;
    let fallbackUrl: string = analysis.youtube_url;

    if (analysis.gcs_video_uri) {
      // YouTube video was downloaded to GCS
      gcsUri = analysis.gcs_video_uri;
      console.log('🎥 Using downloaded video GCS URI:', gcsUri);
    } else if (analysis.analysis_type === 'file_upload' && analysis.youtube_url.startsWith('gs://')) {
      // Direct file upload stored in youtube_url field
      gcsUri = analysis.youtube_url;
      console.log('📁 Using file upload GCS URI:', gcsUri);
    }

    // If no GCS URI available, return the original YouTube URL
    if (!gcsUri) {
      return NextResponse.json({ 
        signedUrl: fallbackUrl,
        isGcsFile: false,
        note: 'Using original YouTube URL (no GCS URI available)'
      });
    }

    // Extract the file path from GCS URI
    const pathParts = gcsUri.replace('gs://', '').split('/');
    const bucketName = pathParts[0];
    const fileName = pathParts.slice(1).join('/');

    console.log('🎬 Generating signed URL for video:', {
      gcsUri,
      bucketName,
      fileName,
      userId
    });

    // Generate a signed URL for viewing (valid for 1 hour)
    const file = storage.bucket(bucketName).file(fileName);
    const options = {
      version: 'v4' as 'v4',
      action: 'read' as 'read',
      expires: Date.now() + 60 * 60 * 1000, // 1 hour
    };

    const [signedUrl] = await file.getSignedUrl(options);

    return NextResponse.json({ 
      signedUrl,
      isGcsFile: true,
      expiresIn: '1 hour'
    });

  } catch (error: any) {
    console.error('❌ Error generating video signed URL:', error);
    return NextResponse.json({ 
      error: 'Failed to generate video URL' 
    }, { status: 500 });
  }
}