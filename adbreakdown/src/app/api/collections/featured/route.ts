import { NextRequest, NextResponse } from 'next/server'
import { createServerSupabaseClient } from '@/lib/supabase'

// GET /api/collections/featured - Get featured collections for banner display
export async function GET(req: NextRequest) {
  try {
    const supabase = createServerSupabaseClient()
    
    // Get URL parameters
    const { searchParams } = new URL(req.url)
    const limit = parseInt(searchParams.get('limit') || '6')
    
    // Fetch all analyses with collection_tags to aggregate stats
    const { data: analyses, error } = await supabase
      .from('ad_analyses')
      .select('collection_tags, title, thumbnail_url, slug, created_at, id')
      .eq('status', 'completed')
      .eq('is_public', true)
      .not('collection_tags', 'is', null)

    if (error) {
      throw new Error(error.message)
    }

    // Aggregate collection statistics with sample ads
    const collectionStats: Record<string, { 
      count: number
      latest_date: string
      sample_ads: Array<{ id: string, title: string, thumbnail_url: string, slug: string }>
      total_engagement?: number
    }> = {}

    analyses?.forEach(analysis => {
      if (analysis.collection_tags && Array.isArray(analysis.collection_tags)) {
        analysis.collection_tags.forEach(tag => {
          if (!collectionStats[tag]) {
            collectionStats[tag] = {
              count: 0,
              latest_date: analysis.created_at,
              sample_ads: [],
              total_engagement: 0
            }
          }
          
          collectionStats[tag].count++
          
          // Add to sample ads (max 4 per collection for display)
          if (collectionStats[tag].sample_ads.length < 4) {
            collectionStats[tag].sample_ads.push({
              id: analysis.id,
              title: analysis.title,
              thumbnail_url: analysis.thumbnail_url,
              slug: analysis.slug
            })
          }
          
          // Update latest date if this one is newer
          if (new Date(analysis.created_at) > new Date(collectionStats[tag].latest_date)) {
            collectionStats[tag].latest_date = analysis.created_at
          }
        })
      }
    })

    // Fetch banner configurations for collections
    const { data: bannerConfigs } = await supabase
      .from('playlist_banner_configs')
      .select('playlist_tag, title, subtitle, background_gradient, text_color, features')
      .eq('is_active', true)

    const bannerConfigMap = new Map(
      bannerConfigs?.map(config => [config.playlist_tag, config]) || []
    )

    // Convert to array, add display titles, and sort by count (most popular first)
    const collections = Object.entries(collectionStats)
      .map(([tag, stats]) => {
        const bannerConfig = bannerConfigMap.get(tag)
        
        // Generate display title
        const displayTitle = bannerConfig?.title || 
          tag.split('-')
            .map(word => word.charAt(0).toUpperCase() + word.slice(1))
            .join(' ') + ' Collection'
        
        return {
          tag,
          title: displayTitle,
          description: bannerConfig?.subtitle || `Discover ${stats.count} curated ads in this collection`,
          count: stats.count,
          sample_ads: stats.sample_ads,
          latest_date: stats.latest_date,
          banner_config: bannerConfig || null
        }
      })
      .sort((a, b) => {
        // Prioritize collections with banner configs
        if (a.banner_config && !b.banner_config) return -1
        if (!a.banner_config && b.banner_config) return 1
        
        // Then by count (most popular first)
        return b.count - a.count
      })
      .slice(0, limit) // Limit results

    return NextResponse.json({
      collections,
      total: collections.length
    })

  } catch (error) {
    console.error('Error fetching featured collections:', error)
    return NextResponse.json(
      { error: 'Failed to fetch featured collections' },
      { status: 500 }
    )
  }
}