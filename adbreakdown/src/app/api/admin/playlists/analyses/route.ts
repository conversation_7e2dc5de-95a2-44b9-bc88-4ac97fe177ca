import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@supabase/supabase-js'
import { auth } from '@clerk/nextjs/server'

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!
const supabase = createClient(supabaseUrl, supabaseServiceKey)

// Admin email check for development/production
const isAdmin = (userId: string | null) => {
  if (process.env.NODE_ENV === 'development') {
    return true // Allow all users in development
  }
  
  // In production, check against admin user IDs or implement proper admin check
  const adminIds = process.env.ADMIN_USER_IDS?.split(',') || []
  return userId ? adminIds.includes(userId) : false
}

function formatDuration(seconds: number): string {
  if (!seconds) return '0:00'
  const mins = Math.floor(seconds / 60)
  const secs = Math.round(seconds % 60)
  return `${mins}:${secs.toString().padStart(2, '0')}`
}

export async function GET(request: NextRequest) {
  try {
    // Make playlists public - no auth required

    // Fetch all analyses with playlist_tags
    const { data: analyses, error } = await supabase
      .from('ad_analyses')
      .select(`
        id,
        title,
        slug,
        brand,
        thumbnail_url,
        duration_seconds,
        collection_tags,
        created_at,
        is_public,
        status
      `)
      .eq('status', 'completed')
      .order('created_at', { ascending: false })

    if (error) {
      throw new Error(error.message)
    }

    // Format the analyses
    const formattedAnalyses = analyses?.map(analysis => ({
      ...analysis,
      duration_formatted: formatDuration(analysis.duration_seconds),
      collection_tags: analysis.collection_tags || []
    })) || []

    return NextResponse.json({
      analyses: formattedAnalyses
    })

  } catch (error) {
    console.error('Error fetching analyses for playlists admin:', error)
    return NextResponse.json(
      { error: 'Failed to fetch analyses' },
      { status: 500 }
    )
  }
}