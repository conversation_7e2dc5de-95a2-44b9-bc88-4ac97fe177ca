import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@supabase/supabase-js'
import { auth } from '@clerk/nextjs/server'

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!
const supabase = createClient(supabaseUrl, supabaseServiceKey)

// Admin email check for development/production
const isAdmin = (userId: string | null) => {
  if (process.env.NODE_ENV === 'development') {
    return true // Allow all users in development
  }
  
  // In production, check against admin user IDs or implement proper admin check
  const adminIds = process.env.ADMIN_USER_IDS?.split(',') || []
  return userId ? adminIds.includes(userId) : false
}

export async function POST(request: NextRequest) {
  try {
    const { userId } = await auth()
    
    if (!isAdmin(userId)) {
      return NextResponse.json({ error: 'Admin access required' }, { status: 403 })
    }

    const { analysisId, tags } = await request.json()

    if (!analysisId) {
      return NextResponse.json({ error: 'Analysis ID is required' }, { status: 400 })
    }

    // Validate and clean tags
    const cleanTags = Array.isArray(tags) 
      ? tags
          .map((tag: string) => tag.trim().toLowerCase())
          .filter((tag: string) => tag.length > 0)
          .filter((tag: string, index: number, array: string[]) => array.indexOf(tag) === index) // Remove duplicates
      : []

    // Update the analysis with new playlist tags
    const { error } = await supabase
      .from('ad_analyses')
      .update({ 
        collection_tags: cleanTags,
        updated_at: new Date().toISOString()
      })
      .eq('id', analysisId)

    if (error) {
      throw new Error(error.message)
    }

    return NextResponse.json({
      success: true,
      message: 'Playlist tags updated successfully',
      tags: cleanTags
    })

  } catch (error) {
    console.error('Error updating playlist tags:', error)
    return NextResponse.json(
      { error: 'Failed to update playlist tags' },
      { status: 500 }
    )
  }
}