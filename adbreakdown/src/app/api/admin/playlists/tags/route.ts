import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@supabase/supabase-js'
import { auth } from '@clerk/nextjs/server'

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!
const supabase = createClient(supabaseUrl, supabaseServiceKey)

// Admin email check for development/production
const isAdmin = (userId: string | null) => {
  if (process.env.NODE_ENV === 'development') {
    return true // Allow all users in development
  }
  
  // In production, check against admin user IDs or implement proper admin check
  const adminIds = process.env.ADMIN_USER_IDS?.split(',') || []
  return userId ? adminIds.includes(userId) : false
}

export async function GET(request: NextRequest) {
  try {
    // Make playlists public - no auth required

    // Fetch all analyses with playlist_tags to aggregate stats
    const { data: analyses, error } = await supabase
      .from('ad_analyses')
      .select('collection_tags, title, created_at')
      .eq('status', 'completed')
      .not('collection_tags', 'is', null)

    if (error) {
      throw new Error(error.message)
    }

    // Aggregate tag statistics
    const tagStats: Record<string, { count: number; latest_ad: string; latest_date: string }> = {}

    analyses?.forEach(analysis => {
      if (analysis.collection_tags && Array.isArray(analysis.collection_tags)) {
        analysis.collection_tags.forEach(tag => {
          if (!tagStats[tag]) {
            tagStats[tag] = {
              count: 0,
              latest_ad: analysis.title,
              latest_date: analysis.created_at
            }
          }
          tagStats[tag].count++
          
          // Update latest ad if this one is newer
          if (new Date(analysis.created_at) > new Date(tagStats[tag].latest_date)) {
            tagStats[tag].latest_ad = analysis.title
            tagStats[tag].latest_date = analysis.created_at
          }
        })
      }
    })

    // Convert to array and sort by count
    const tags = Object.entries(tagStats)
      .map(([tag, stats]) => ({
        tag,
        count: stats.count,
        latest_ad: stats.latest_ad
      }))
      .sort((a, b) => b.count - a.count)

    return NextResponse.json({
      tags
    })

  } catch (error) {
    console.error('Error fetching playlist tags:', error)
    return NextResponse.json(
      { error: 'Failed to fetch playlist tags' },
      { status: 500 }
    )
  }
}