import { NextRequest, NextResponse } from 'next/server'
import { auth } from '@clerk/nextjs/server'
import { usageService } from '@/lib/services/usageService'

// Admin user check
const isAdmin = (userId: string | null) => {
  if (process.env.NODE_ENV === 'development') {
    return true // Allow all users in development
  }
  
  const adminIds = process.env.ADMIN_USER_IDS?.split(',') || []
  return userId ? adminIds.includes(userId) : false
}

export async function POST(req: NextRequest) {
  try {
    const { userId } = await auth()
    
    if (!userId || !isAdmin(userId)) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 403 })
    }

    const body = await req.json()
    const { entryId, status, notes } = body

    // Validate status
    if (!['approved', 'rejected', 'pending'].includes(status)) {
      return NextResponse.json(
        { error: 'Invalid status. Must be approved, rejected, or pending' },
        { status: 400 }
      )
    }

    // Update waitlist entry
    const success = await usageService.updateWaitlistStatus(entryId, status, notes)
    
    if (!success) {
      return NextResponse.json(
        { error: 'Failed to update waitlist entry' }, 
        { status: 500 }
      )
    }

    return NextResponse.json({
      success: true,
      message: `Waitlist entry ${status} successfully`
    })

  } catch (error) {
    console.error('Error updating waitlist entry:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}