import { GoogleGenerativeAI, HarmCategory, HarmBlockThreshold } from '@google/generative-ai';
import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { createServerSupabaseClient } from '@/lib/supabase';

// Type definitions for better type safety
interface Message {
  role: 'user' | 'assistant';
  content: string;
  id?: string;
  timestamp?: Date;
}

interface CampaignChatRequest {
  messages: Message[];
  campaignId?: string;
  campaignData?: any;
  assistanceType?: string;
}

// Environment variable validation
function validateEnvironmentVariables(): { isValid: boolean; error?: string } {
  if (!process.env.GEMINI_API_KEY) {
    return { isValid: false, error: 'GEMINI_API_KEY is not configured' };
  }
  
  if (process.env.GEMINI_API_KEY.length < 10) {
    return { isValid: false, error: 'GEMINI_API_KEY appears to be invalid' };
  }
  
  return { isValid: true };
}

function sanitizeInput(input: string): string {
  return input.trim().substring(0, 10000); // Limit input length
}

// Initialize Gemini AI with validation
let genAI: GoogleGenerativeAI;
try {
  const envCheck = validateEnvironmentVariables();
  if (!envCheck.isValid) {
    throw new Error(envCheck.error);
  }
  genAI = new GoogleGenerativeAI(process.env.GEMINI_API_KEY!);
} catch (error) {
  console.error('Failed to initialize Gemini AI:', error);
}

// Build system prompt based on assistance type and campaign data
function buildSystemPrompt(assistanceType: string, campaignData: any): string {
  const basePrompt = `You are an expert AI marketing assistant specializing in campaign development and optimization. You have deep knowledge of:

- Video advertising and script writing
- Brand strategy and positioning
- Audience analysis and targeting
- Creative concept development
- Marketing workflow optimization
- Compliance and regulatory requirements
- Performance optimization strategies

You provide actionable, specific, and professional advice while maintaining a helpful and collaborative tone.`;

  const campaignContext = campaignData ? `

CAMPAIGN CONTEXT:
- Campaign Name: ${campaignData.name || 'Not specified'}
- Objectives: ${campaignData.objectives?.join(', ') || 'Not specified'}
- Target Audience: ${campaignData.target_audience?.join(', ') || 'Not specified'}
- Key Message: ${campaignData.key_message || 'Not specified'}
- Current Stage: ${campaignData.stage || 'Not specified'}
- Progress: ${campaignData.progress || 0}%
- Channels: ${campaignData.channels?.join(', ') || 'Not specified'}
- Budget: ${campaignData.budget_amount ? `$${campaignData.budget_amount}` : 'Not specified'}
- Brand Guidelines: ${campaignData.brand_guidelines || 'Not specified'}` : '';

  const assistanceSpecific = assistanceType ? `

CURRENT FOCUS: ${assistanceType.replace('_', ' ').toUpperCase()}
Please prioritize responses related to ${assistanceType.replace('_', ' ')} while considering the full campaign context.` : '';

  return basePrompt + campaignContext + assistanceSpecific;
}

export async function POST(req: NextRequest) {
  try {
    // Check authentication
    const { userId } = await auth();
    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Validate environment
    const envCheck = validateEnvironmentVariables();
    if (!envCheck.isValid) {
      console.error('Environment validation failed:', envCheck.error);
      return NextResponse.json(
        { error: 'AI service not configured', code: 'ENV_ERROR' },
        { status: 503 }
      );
    }

    // Parse request body
    let body: CampaignChatRequest;
    try {
      body = await req.json();
    } catch (error) {
      return NextResponse.json(
        { error: 'Invalid JSON in request body', code: 'PARSE_ERROR' },
        { status: 400 }
      );
    }

    const { messages, campaignId, campaignData, assistanceType } = body;

    // Validate required fields
    if (!messages || !Array.isArray(messages) || messages.length === 0) {
      return NextResponse.json(
        { error: 'Messages array is required and cannot be empty', code: 'VALIDATION_ERROR' },
        { status: 400 }
      );
    }

    // Get user from database
    const supabase = createServerSupabaseClient();
    const { data: user, error: userError } = await supabase
      .from('users')
      .select('id')
      .eq('clerk_id', userId)
      .single();

    if (userError || !user) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 });
    }

    // Validate campaign access if campaignId is provided
    if (campaignId && campaignId !== 'general') {
      const { data: campaign, error: campaignError } = await supabase
        .from('campaigns')
        .select('id, name')
        .eq('id', campaignId)
        .single();

      if (campaignError) {
        return NextResponse.json({ error: 'Campaign not found or access denied' }, { status: 404 });
      }
    }

    // Sanitize and prepare messages
    const sanitizedMessages = messages.map(msg => ({
      ...msg,
      content: sanitizeInput(msg.content)
    }));

    // Get the latest user message
    const latestMessage = sanitizedMessages[sanitizedMessages.length - 1];
    if (!latestMessage || latestMessage.role !== 'user') {
      return NextResponse.json(
        { error: 'Last message must be from user', code: 'VALIDATION_ERROR' },
        { status: 400 }
      );
    }

    // Build conversation history for Gemini
    const conversationHistory = sanitizedMessages.slice(0, -1).map(msg => ({
      role: msg.role === 'assistant' ? 'model' : 'user',
      parts: [{ text: msg.content }]
    }));

    // Build system prompt
    const systemPrompt = buildSystemPrompt(assistanceType || '', campaignData);

    // Initialize model with proper error handling
    let model;
    try {
      model = genAI.getGenerativeModel({ 
        model: 'gemini-1.5-pro',
        generationConfig: {
          temperature: 0.7,
          topK: 40,
          topP: 0.95,
          maxOutputTokens: 4096,
        },
        systemInstruction: {
          parts: [{ text: systemPrompt }]
        },
        safetySettings: [
          {
            category: HarmCategory.HARM_CATEGORY_HARASSMENT,
            threshold: HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE,
          },
          {
            category: HarmCategory.HARM_CATEGORY_HATE_SPEECH,
            threshold: HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE,
          },
        ],
      });
    } catch (error) {
      console.error('Failed to create model:', error);
      return NextResponse.json(
        { error: 'Failed to initialize AI model', code: 'MODEL_INIT_ERROR' },
        { status: 500 }
      );
    }

    // Start chat session with history
    const chat = model.startChat({
      history: conversationHistory
    });

    // Generate response
    let result;
    try {
      result = await chat.sendMessage(latestMessage.content);
    } catch (error: any) {
      console.error('Gemini API error:', error);
      
      if (error.message?.includes('SAFETY')) {
        return NextResponse.json(
          { error: 'Content filtered for safety. Please rephrase your question.', code: 'SAFETY_ERROR' },
          { status: 400 }
        );
      }
      
      return NextResponse.json(
        { error: 'Failed to generate response', code: 'GENERATION_ERROR' },
        { status: 500 }
      );
    }

    const response = result.response;
    const responseText = response.text();

    if (!responseText) {
      return NextResponse.json(
        { error: 'Empty response from AI', code: 'EMPTY_RESPONSE' },
        { status: 500 }
      );
    }

    // Log the interaction if campaignId is provided
    if (campaignId && campaignId !== 'general') {
      try {
        await supabase
          .from('campaign_ai_interactions')
          .insert({
            campaign_id: campaignId,
            user_id: user.id,
            interaction_type: assistanceType || 'general',
            prompt: latestMessage.content,
            response: responseText,
            model_used: 'gemini-1.5-pro',
            model_version: '1.5-pro',
            status: 'completed'
          });

        // Log activity
        await supabase
          .from('campaign_activities')
          .insert({
            campaign_id: campaignId,
            user_id: user.id,
            activity_type: 'ai_suggestion',
            title: 'AI Assistant Used',
            description: `AI provided ${assistanceType?.replace('_', ' ') || 'general'} assistance`,
            metadata: { assistance_type: assistanceType }
          });
      } catch (error) {
        console.error('Error logging AI interaction:', error);
        // Don't fail the request for logging errors
      }
    }

    return NextResponse.json({
      content: responseText,
      role: 'assistant',
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Unexpected error in campaign chat:', error);
    return NextResponse.json(
      { error: 'Internal server error', code: 'INTERNAL_ERROR' },
      { status: 500 }
    );
  }
}
