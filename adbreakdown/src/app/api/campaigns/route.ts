import { NextRequest, NextResponse } from 'next/server'
import { auth } from '@clerk/nextjs/server'
import { createServerSupabaseClient } from '@/lib/supabase'

// GET /api/campaigns - List user's campaigns
export async function GET(req: NextRequest) {
  try {
    const { userId } = await auth()
    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const supabase = createServerSupabaseClient()
    const { searchParams } = new URL(req.url)
    
    // Query parameters
    const status = searchParams.get('status')
    const stage = searchParams.get('stage')
    const limit = parseInt(searchParams.get('limit') || '20')
    const offset = parseInt(searchParams.get('offset') || '0')
    const search = searchParams.get('search')

    // Get user from database
    const { data: user, error: userError } = await supabase
      .from('users')
      .select('id')
      .eq('clerk_id', userId)
      .single()

    if (userError || !user) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 })
    }

    // Build query - First get campaigns user owns or is a member of
    let query = supabase
      .from('campaigns')
      .select(`
        *,
        campaign_team_members(
          id,
          role,
          user_id,
          users!campaign_team_members_user_id_fkey(first_name, last_name, email)
        ),
        campaign_assets(
          id,
          name,
          asset_type,
          status,
          created_at
        )
      `)
      .order('created_at', { ascending: false })
      .range(offset, offset + limit - 1)

    // Apply filters
    if (status) {
      query = query.eq('status', status)
    }
    if (stage) {
      query = query.eq('stage', stage)
    }
    if (search) {
      query = query.or(`name.ilike.%${search}%,description.ilike.%${search}%`)
    }

    const { data: campaigns, error } = await query

    if (error) {
      console.error('Error fetching campaigns:', error)
      return NextResponse.json({ error: 'Failed to fetch campaigns' }, { status: 500 })
    }

    // Get campaign statistics - simplified for now
    const statistics = {
      total: campaigns?.length || 0,
      in_progress: campaigns?.filter(c => c.status === 'in-progress').length || 0,
      avg_time_to_launch: 28,
      total_budget: campaigns?.reduce((sum, c) => sum + (c.budget_amount || 0), 0) || 0,
      by_status: campaigns?.reduce((acc: any, campaign: any) => {
        acc[campaign.status] = (acc[campaign.status] || 0) + 1
        return acc
      }, {}) || {}
    }

    return NextResponse.json({
      campaigns: campaigns || [],
      statistics,
      pagination: {
        limit,
        offset,
        total: campaigns?.length || 0
      }
    })

  } catch (error) {
    console.error('Error in campaigns GET:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

// POST /api/campaigns - Create new campaign
export async function POST(req: NextRequest) {
  try {
    const { userId } = await auth()
    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const supabase = createServerSupabaseClient()
    const body = await req.json()

    // Get user from database
    const { data: user, error: userError } = await supabase
      .from('users')
      .select('id')
      .eq('clerk_id', userId)
      .single()

    if (userError || !user) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 })
    }

    // Validate required fields
    const { name, description, template_id } = body
    if (!name) {
      return NextResponse.json({ error: 'Campaign name is required' }, { status: 400 })
    }

    // Prepare campaign data
    const campaignData = {
      user_id: user.id,
      name,
      description,
      template_id,
      status: 'draft',
      stage: 'briefing',
      progress: 0,
      objectives: body.objectives || [],
      key_message: body.key_message,
      call_to_action: body.call_to_action,
      target_audience: body.target_audience || [],
      channels: body.channels || [],
      budget_amount: body.budget_amount,
      budget_currency: body.budget_currency || 'USD',
      start_date: body.start_date,
      launch_date: body.launch_date,
      end_date: body.end_date,
      brand_guidelines: body.brand_guidelines,
      compliance_requirements: body.compliance_requirements || [],
      tags: body.tags || []
    }

    // Create campaign
    const { data: campaign, error: campaignError } = await supabase
      .from('campaigns')
      .insert(campaignData)
      .select()
      .single()

    if (campaignError) {
      console.error('Error creating campaign:', campaignError)
      return NextResponse.json({ error: 'Failed to create campaign' }, { status: 500 })
    }

    // Add creator as team member with owner role
    const { error: teamError } = await supabase
      .from('campaign_team_members')
      .insert({
        campaign_id: campaign.id,
        user_id: user.id,
        role: 'owner',
        status: 'active',
        joined_at: new Date().toISOString()
      })

    if (teamError) {
      console.error('Error adding team member:', teamError)
      // Don't fail the request, just log the error
    }

    // Create default workflow stages if template is provided
    if (template_id) {
      const defaultStages = [
        { name: 'Campaign Brief', stage_order: 1, description: 'Define campaign objectives and requirements' },
        { name: 'Ideation', stage_order: 2, description: 'Brainstorm creative concepts and ideas' },
        { name: 'Scriptwriting', stage_order: 3, description: 'Develop scripts and copy' },
        { name: 'Storyboard', stage_order: 4, description: 'Create visual storyboards' },
        { name: 'Production', stage_order: 5, description: 'Produce creative assets' },
        { name: 'Review & Approval', stage_order: 6, description: 'Review and approve final assets' },
        { name: 'Launch', stage_order: 7, description: 'Launch the campaign' }
      ]

      const workflowStages = defaultStages.map(stage => ({
        campaign_id: campaign.id,
        ...stage,
        status: stage.stage_order === 1 ? 'in-progress' : 'pending'
      }))

      await supabase
        .from('campaign_workflow_stages')
        .insert(workflowStages)
    }

    // Log activity
    await supabase
      .from('campaign_activities')
      .insert({
        campaign_id: campaign.id,
        user_id: user.id,
        activity_type: 'campaign_created',
        title: 'Campaign Created',
        description: `Campaign "${name}" was created`
      })

    return NextResponse.json({ campaign }, { status: 201 })

  } catch (error) {
    console.error('Error in campaigns POST:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

// PUT /api/campaigns - Bulk update campaigns (for admin operations)
export async function PUT(req: NextRequest) {
  try {
    const { userId } = await auth()
    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const supabase = createServerSupabaseClient()
    const body = await req.json()
    const { campaign_ids, updates } = body

    if (!campaign_ids || !Array.isArray(campaign_ids) || campaign_ids.length === 0) {
      return NextResponse.json({ error: 'Campaign IDs are required' }, { status: 400 })
    }

    // Get user from database
    const { data: user, error: userError } = await supabase
      .from('users')
      .select('id')
      .eq('clerk_id', userId)
      .single()

    if (userError || !user) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 })
    }

    // Update campaigns (RLS will ensure user can only update accessible campaigns)
    const { data: updatedCampaigns, error } = await supabase
      .from('campaigns')
      .update({
        ...updates,
        updated_at: new Date().toISOString()
      })
      .in('id', campaign_ids)
      .select()

    if (error) {
      console.error('Error updating campaigns:', error)
      return NextResponse.json({ error: 'Failed to update campaigns' }, { status: 500 })
    }

    return NextResponse.json({ 
      updated_campaigns: updatedCampaigns,
      count: updatedCampaigns?.length || 0
    })

  } catch (error) {
    console.error('Error in campaigns PUT:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
