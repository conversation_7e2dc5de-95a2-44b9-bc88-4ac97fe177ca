import { NextRequest, NextResponse } from 'next/server'
import { auth } from '@clerk/nextjs/server'
import { createServerSupabaseClient } from '@/lib/supabase'
import { GoogleGenerativeAI } from '@google/generative-ai'

// Initialize Gemini AI
const genAI = process.env.NEXT_PUBLIC_GEMINI_API_KEY 
  ? new GoogleGenerativeAI(process.env.NEXT_PUBLIC_GEMINI_API_KEY)
  : null

// POST /api/campaigns/[id]/ai-assist - AI assistance for campaigns
export async function POST(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { userId } = await auth()
    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    if (!genAI) {
      return NextResponse.json({ error: 'AI service not configured' }, { status: 503 })
    }

    const supabase = createServerSupabaseClient()
    const campaignId = params.id
    const body = await req.json()
    const { assistance_type, prompt, context, asset_id, stage_id } = body

    // Get user from database
    const { data: user, error: userError } = await supabase
      .from('users')
      .select('id')
      .eq('clerk_id', userId)
      .single()

    if (userError || !user) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 })
    }

    // Get campaign details
    const { data: campaign, error: campaignError } = await supabase
      .from('campaigns')
      .select(`
        *,
        campaign_assets(id, name, asset_type, status),
        campaign_workflow_stages(id, name, status, stage_order)
      `)
      .eq('id', campaignId)
      .single()

    if (campaignError) {
      return NextResponse.json({ error: 'Campaign not found' }, { status: 404 })
    }

    // Build AI prompt based on assistance type
    let aiPrompt = ''
    let modelName = 'gemini-1.5-pro'

    switch (assistance_type) {
      case 'script_generation':
        aiPrompt = buildScriptGenerationPrompt(campaign, prompt, context)
        break
      
      case 'brand_analysis':
        aiPrompt = buildBrandAnalysisPrompt(campaign, prompt, context)
        break
      
      case 'audience_insights':
        aiPrompt = buildAudienceInsightsPrompt(campaign, prompt, context)
        break
      
      case 'creative_suggestions':
        aiPrompt = buildCreativeSuggestionsPrompt(campaign, prompt, context)
        break
      
      case 'workflow_optimization':
        aiPrompt = buildWorkflowOptimizationPrompt(campaign, prompt, context)
        break
      
      case 'compliance_check':
        aiPrompt = buildComplianceCheckPrompt(campaign, prompt, context)
        break
      
      default:
        return NextResponse.json({ error: 'Invalid assistance type' }, { status: 400 })
    }

    // Generate AI response
    const model = genAI.getGenerativeModel({ model: modelName })
    const result = await model.generateContent(aiPrompt)
    const response = result.response
    const aiResponse = response.text()

    // Save AI interaction
    const { data: interaction, error: interactionError } = await supabase
      .from('campaign_ai_interactions')
      .insert({
        campaign_id: campaignId,
        user_id: user.id,
        interaction_type: assistance_type,
        prompt: prompt,
        response: aiResponse,
        asset_id: asset_id || null,
        stage_id: stage_id || null,
        model_used: modelName,
        model_version: '1.5-pro',
        status: 'completed'
      })
      .select()
      .single()

    if (interactionError) {
      console.error('Error saving AI interaction:', interactionError)
      // Don't fail the request, just log the error
    }

    // Log activity
    await supabase
      .from('campaign_activities')
      .insert({
        campaign_id: campaignId,
        user_id: user.id,
        activity_type: 'ai_suggestion',
        title: 'AI Assistance Used',
        description: `AI provided ${assistance_type.replace('_', ' ')} assistance`,
        metadata: { assistance_type, interaction_id: interaction?.id }
      })

    return NextResponse.json({
      response: aiResponse,
      interaction_id: interaction?.id,
      assistance_type
    })

  } catch (error) {
    console.error('Error in AI assist:', error)
    return NextResponse.json({ error: 'AI assistance failed' }, { status: 500 })
  }
}

// Helper functions to build prompts
function buildScriptGenerationPrompt(campaign: any, userPrompt: string, context: any) {
  return `You are an expert marketing copywriter and scriptwriter. Generate a compelling video ad script based on the following campaign information:

Campaign: ${campaign.name}
Objectives: ${campaign.objectives?.join(', ') || 'Not specified'}
Target Audience: ${campaign.target_audience?.join(', ') || 'Not specified'}
Key Message: ${campaign.key_message || 'Not specified'}
Call to Action: ${campaign.call_to_action || 'Not specified'}
Channels: ${campaign.channels?.join(', ') || 'Not specified'}
Brand Guidelines: ${campaign.brand_guidelines || 'Not specified'}

User Request: ${userPrompt}

Additional Context: ${JSON.stringify(context || {})}

Please generate a professional video ad script that:
1. Hooks the audience in the first 3 seconds
2. Clearly communicates the key message
3. Resonates with the target audience
4. Includes a strong call to action
5. Follows brand guidelines
6. Is optimized for the specified channels

Format the script with clear scene descriptions, voiceover text, and visual cues.`
}

function buildBrandAnalysisPrompt(campaign: any, userPrompt: string, context: any) {
  return `You are a brand strategy expert. Analyze the following campaign for brand alignment and consistency:

Campaign: ${campaign.name}
Brand Guidelines: ${campaign.brand_guidelines || 'Not specified'}
Key Message: ${campaign.key_message || 'Not specified'}
Target Audience: ${campaign.target_audience?.join(', ') || 'Not specified'}

User Request: ${userPrompt}

Additional Context: ${JSON.stringify(context || {})}

Please provide:
1. Brand alignment score (1-100)
2. Areas of strong brand consistency
3. Areas needing improvement
4. Specific recommendations for better brand alignment
5. Tone and voice analysis
6. Visual brand element suggestions`
}

function buildAudienceInsightsPrompt(campaign: any, userPrompt: string, context: any) {
  return `You are a consumer insights expert. Provide detailed audience analysis for this campaign:

Campaign: ${campaign.name}
Target Audience: ${campaign.target_audience?.join(', ') || 'Not specified'}
Objectives: ${campaign.objectives?.join(', ') || 'Not specified'}
Channels: ${campaign.channels?.join(', ') || 'Not specified'}

User Request: ${userPrompt}

Additional Context: ${JSON.stringify(context || {})}

Please provide:
1. Detailed audience persona analysis
2. Psychographic insights
3. Media consumption habits
4. Messaging preferences
5. Optimal timing and frequency recommendations
6. Channel-specific audience behavior insights
7. Potential audience expansion opportunities`
}

function buildCreativeSuggestionsPrompt(campaign: any, userPrompt: string, context: any) {
  return `You are a creative director with expertise in advertising and marketing. Provide creative suggestions for this campaign:

Campaign: ${campaign.name}
Objectives: ${campaign.objectives?.join(', ') || 'Not specified'}
Target Audience: ${campaign.target_audience?.join(', ') || 'Not specified'}
Key Message: ${campaign.key_message || 'Not specified'}
Channels: ${campaign.channels?.join(', ') || 'Not specified'}

User Request: ${userPrompt}

Additional Context: ${JSON.stringify(context || {})}

Please provide:
1. Creative concept ideas
2. Visual style recommendations
3. Storytelling approaches
4. Emotional hooks and triggers
5. Channel-specific creative adaptations
6. Interactive elements suggestions
7. Innovative format ideas`
}

function buildWorkflowOptimizationPrompt(campaign: any, userPrompt: string, context: any) {
  return `You are a project management expert specializing in marketing campaigns. Analyze and optimize the workflow for this campaign:

Campaign: ${campaign.name}
Current Stage: ${campaign.stage}
Progress: ${campaign.progress}%
Launch Date: ${campaign.launch_date || 'Not specified'}
Team Size: ${campaign.campaign_team_members?.length || 'Unknown'}

User Request: ${userPrompt}

Additional Context: ${JSON.stringify(context || {})}

Please provide:
1. Workflow efficiency analysis
2. Bottleneck identification
3. Timeline optimization suggestions
4. Resource allocation recommendations
5. Parallel task opportunities
6. Risk mitigation strategies
7. Quality checkpoints recommendations`
}

function buildComplianceCheckPrompt(campaign: any, userPrompt: string, context: any) {
  return `You are a compliance and legal expert for marketing campaigns. Review this campaign for compliance issues:

Campaign: ${campaign.name}
Compliance Requirements: ${campaign.compliance_requirements?.join(', ') || 'Not specified'}
Channels: ${campaign.channels?.join(', ') || 'Not specified'}
Target Audience: ${campaign.target_audience?.join(', ') || 'Not specified'}

User Request: ${userPrompt}

Additional Context: ${JSON.stringify(context || {})}

Please provide:
1. Compliance risk assessment
2. Regulatory requirements by channel
3. Age-appropriate content guidelines
4. Disclosure requirements
5. Accessibility compliance
6. Data privacy considerations
7. Specific recommendations for compliance`
}
