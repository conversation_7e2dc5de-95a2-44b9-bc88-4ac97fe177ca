import { NextRequest, NextResponse } from 'next/server'
import { auth } from '@clerk/nextjs/server'
import { createServerSupabaseClient } from '@/lib/supabase'

// GET /api/campaigns/[id] - Get campaign details
export async function GET(
  req: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { userId } = await auth()
    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const supabase = createServerSupabaseClient()
    const { id: campaignId } = await params

    // Get user from database
    const { data: user, error: userError } = await supabase
      .from('users')
      .select('id')
      .eq('clerk_id', userId)
      .single()

    if (userError || !user) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 })
    }

    // Get campaign with all related data
    const { data: campaign, error: campaignError } = await supabase
      .from('campaigns')
      .select(`
        *,
        campaign_team_members(
          id,
          role,
          status,
          joined_at,
          users!campaign_team_members_user_id_fkey(id, first_name, last_name, email)
        ),
        campaign_assets(
          id,
          name,
          description,
          asset_type,
          file_url,
          status,
          version,
          brand_alignment_score,
          created_at,
          updated_at,
          users!campaign_assets_user_id_fkey(first_name, last_name)
        ),
        campaign_workflow_stages(
          id,
          name,
          description,
          stage_order,
          status,
          due_date,
          started_at,
          completed_at,
          assigned_to,
          users!campaign_workflow_stages_assigned_to_fkey(first_name, last_name)
        ),
        campaign_comments(
          id,
          content,
          comment_type,
          parent_comment_id,
          is_resolved,
          created_at,
          users!campaign_comments_user_id_fkey(first_name, last_name)
        )
      `)
      .eq('id', campaignId)
      .single()

    if (campaignError) {
      if (campaignError.code === 'PGRST116') {
        return NextResponse.json({ error: 'Campaign not found' }, { status: 404 })
      }
      console.error('Error fetching campaign:', campaignError)
      return NextResponse.json({ error: 'Failed to fetch campaign' }, { status: 500 })
    }

    // Get recent activities
    const { data: activities } = await supabase
      .from('campaign_activities')
      .select(`
        *,
        users(first_name, last_name)
      `)
      .eq('campaign_id', campaignId)
      .order('created_at', { ascending: false })
      .limit(20)

    // Get AI interactions
    const { data: aiInteractions } = await supabase
      .from('campaign_ai_interactions')
      .select('*')
      .eq('campaign_id', campaignId)
      .order('created_at', { ascending: false })
      .limit(10)

    return NextResponse.json({
      campaign,
      activities: activities || [],
      ai_interactions: aiInteractions || []
    })

  } catch (error) {
    console.error('Error in campaign GET:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

// PUT /api/campaigns/[id] - Update campaign
export async function PUT(
  req: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { userId } = await auth()
    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const supabase = createServerSupabaseClient()
    const { id: campaignId } = await params
    const body = await req.json()

    // Get user from database
    const { data: user, error: userError } = await supabase
      .from('users')
      .select('id')
      .eq('clerk_id', userId)
      .single()

    if (userError || !user) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 })
    }

    // Prepare update data
    const updateData = {
      ...body,
      updated_at: new Date().toISOString()
    }

    // Remove fields that shouldn't be updated directly
    delete updateData.id
    delete updateData.user_id
    delete updateData.created_at

    // Update campaign (RLS will ensure user has access)
    const { data: updatedCampaign, error: updateError } = await supabase
      .from('campaigns')
      .update(updateData)
      .eq('id', campaignId)
      .select()
      .single()

    if (updateError) {
      if (updateError.code === 'PGRST116') {
        return NextResponse.json({ error: 'Campaign not found or access denied' }, { status: 404 })
      }
      console.error('Error updating campaign:', updateError)
      return NextResponse.json({ error: 'Failed to update campaign' }, { status: 500 })
    }

    // Log activity for significant changes
    const significantFields = ['status', 'stage', 'name', 'launch_date']
    const changedFields = significantFields.filter(field => 
      body[field] !== undefined && body[field] !== updatedCampaign[field]
    )

    if (changedFields.length > 0) {
      await supabase
        .from('campaign_activities')
        .insert({
          campaign_id: campaignId,
          user_id: user.id,
          activity_type: 'campaign_updated',
          title: 'Campaign Updated',
          description: `Updated: ${changedFields.join(', ')}`,
          metadata: { changed_fields: changedFields }
        })
    }

    return NextResponse.json({ campaign: updatedCampaign })

  } catch (error) {
    console.error('Error in campaign PUT:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

// DELETE /api/campaigns/[id] - Delete campaign
export async function DELETE(
  req: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { userId } = await auth()
    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const supabase = createServerSupabaseClient()
    const { id: campaignId } = await params

    // Get user from database
    const { data: user, error: userError } = await supabase
      .from('users')
      .select('id')
      .eq('clerk_id', userId)
      .single()

    if (userError || !user) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 })
    }

    // Check if user is the owner of the campaign
    const { data: campaign, error: campaignError } = await supabase
      .from('campaigns')
      .select('user_id, name')
      .eq('id', campaignId)
      .single()

    if (campaignError) {
      if (campaignError.code === 'PGRST116') {
        return NextResponse.json({ error: 'Campaign not found' }, { status: 404 })
      }
      return NextResponse.json({ error: 'Failed to fetch campaign' }, { status: 500 })
    }

    if (campaign.user_id !== user.id) {
      return NextResponse.json({ error: 'Only campaign owner can delete the campaign' }, { status: 403 })
    }

    // Delete campaign (cascade will handle related records)
    const { error: deleteError } = await supabase
      .from('campaigns')
      .delete()
      .eq('id', campaignId)

    if (deleteError) {
      console.error('Error deleting campaign:', deleteError)
      return NextResponse.json({ error: 'Failed to delete campaign' }, { status: 500 })
    }

    return NextResponse.json({ 
      message: 'Campaign deleted successfully',
      campaign_name: campaign.name
    })

  } catch (error) {
    console.error('Error in campaign DELETE:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

// PATCH /api/campaigns/[id] - Partial update (for specific operations)
export async function PATCH(
  req: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { userId } = await auth()
    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const supabase = createServerSupabaseClient()
    const { id: campaignId } = await params
    const body = await req.json()
    const { operation, data } = body

    // Get user from database
    const { data: user, error: userError } = await supabase
      .from('users')
      .select('id')
      .eq('clerk_id', userId)
      .single()

    if (userError || !user) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 })
    }

    switch (operation) {
      case 'update_progress':
        const { progress } = data
        if (typeof progress !== 'number' || progress < 0 || progress > 100) {
          return NextResponse.json({ error: 'Invalid progress value' }, { status: 400 })
        }

        const { data: updatedCampaign, error: progressError } = await supabase
          .from('campaigns')
          .update({ progress, updated_at: new Date().toISOString() })
          .eq('id', campaignId)
          .select()
          .single()

        if (progressError) {
          return NextResponse.json({ error: 'Failed to update progress' }, { status: 500 })
        }

        return NextResponse.json({ campaign: updatedCampaign })

      case 'change_stage':
        const { stage } = data
        const validStages = ['briefing', 'ideation', 'scriptwriting', 'storyboard', 'production', 'review', 'launched']
        
        if (!validStages.includes(stage)) {
          return NextResponse.json({ error: 'Invalid stage' }, { status: 400 })
        }

        const { data: stageUpdatedCampaign, error: stageError } = await supabase
          .from('campaigns')
          .update({ stage, updated_at: new Date().toISOString() })
          .eq('id', campaignId)
          .select()
          .single()

        if (stageError) {
          return NextResponse.json({ error: 'Failed to update stage' }, { status: 500 })
        }

        // Log activity
        await supabase
          .from('campaign_activities')
          .insert({
            campaign_id: campaignId,
            user_id: user.id,
            activity_type: 'stage_changed',
            title: 'Stage Changed',
            description: `Campaign moved to ${stage} stage`
          })

        return NextResponse.json({ campaign: stageUpdatedCampaign })

      default:
        return NextResponse.json({ error: 'Invalid operation' }, { status: 400 })
    }

  } catch (error) {
    console.error('Error in campaign PATCH:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
