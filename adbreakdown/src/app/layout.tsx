import type { Metadata } from 'next'
import { Inter, Kavoon } from 'next/font/google'
import { Clerk<PERSON>rovider } from '@clerk/nextjs'
import './globals.css'
import { generateMetadata, seoConfigs, structuredData } from '@/lib/seo'
import GoogleAnalytics from '@/components/GoogleAnalytics'
import localFont from 'next/font/local'

const inter = Inter({ 
  subsets: ['latin'], 
  variable: '--font-inter',
  display: 'swap',
  preload: true
})

const kavoon = Kavoon({ 
  subsets: ['latin'], 
  variable: '--font-kavoon',
  display: 'swap',
  preload: true,
  weight: '400'
})

const gegolaDemo = localFont({
  src: '../fonts/gegola-demo.regular.otf',
  variable: '--font-gegola',
  display: 'swap',
  preload: true,
  fallback: ['Inter', 'system-ui', 'sans-serif']
})

export const metadata: Metadata = generateMetadata({
  ...seoConfigs.home,
  structuredData: [
    structuredData.organization,
    structuredData.website,
    structuredData.softwareApplication
  ]
})

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <ClerkProvider
      appearance={{
        elements: {
          formButtonPrimary: 'bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700',
        },
      }}
    >
      <html lang="en" className={`${inter.variable} ${kavoon.variable} ${gegolaDemo.variable}`}>
        <head>
          <GoogleAnalytics />
          {/* Preconnect to external domains */}
          <link rel="preconnect" href="https://img.youtube.com" />
          <link rel="preconnect" href="https://i.ytimg.com" />
          <link rel="preconnect" href="https://fonts.googleapis.com" />
          <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin="anonymous" />
          {/* DNS prefetch for performance */}
          <link rel="dns-prefetch" href="https://www.googletagmanager.com" />
          <link rel="dns-prefetch" href="https://www.google-analytics.com" />
        </head>
        <body className={inter.className}>
          {children}
        </body>
      </html>
    </ClerkProvider>
  )
}
