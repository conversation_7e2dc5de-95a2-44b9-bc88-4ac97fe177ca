'use client'

import { Suspense } from 'react'
import React, { useState } from 'react'
import Link from "next/link"
import Image from "next/image"
import { usePathname } from "next/navigation"
import { useUser, useClerk } from "@clerk/nextjs"
import { 
  Library,
  Search,
  TrendingUp,
  BarChart3,
  User,
  Settings,
  HelpCircle,
  LogOut,
  CreditCard,
  Home,
  ChevronRight,
  Plus,
  Tag,
  ChevronDown,
  ExternalLink,
  VideoIcon,
  Palette,
  Menu,
  Briefcase,
  Users,
  Bot,
  Sparkles,
  Calendar,
  FileText,
  Target,
  Lightbulb,
  CheckCircle
} from "lucide-react"

import { cn } from "@/lib/utils"
import { Button } from "@/components/ui/button"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { 
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import {
  Sidebar,
  SidebarContent,
  Sidebar<PERSON>ooter,
  SidebarGroup,
  SidebarGroupContent,
  SidebarGroupLabel,
  SidebarHeader,
  SidebarInset,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarProvider,
  SidebarRail,
  SidebarTrigger,
  SidebarMenuSub,
  SidebarMenuSubItem,
  SidebarMenuSubButton,
  useSidebar,
} from "@/components/ui/sidebar"
import { Separator } from "@/components/ui/separator"
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb"

// Mobile menu button component
function MobileMenuButton({ className }: { className?: string }) {
  const { toggleSidebar } = useSidebar()
  
  return (
    <Button
      variant="ghost"
      size="icon"
      onClick={toggleSidebar}
      className={cn("w-6 h-6 p-0", className)}
      aria-label="Toggle menu"
    >
      <Menu className="h-4 w-4" />
    </Button>
  )
}

// Studio navigation items
const studioNavItems = [
  {
    title: "Campaign Management",
    href: "/studio/campaign",
    icon: Briefcase,
    description: "Manage marketing campaigns"
  },
  {
    title: "Plan",
    href: "/studio/plan",
    icon: Target,
    description: "Strategic planning tools"
  },
  {
    title: "Create",
    href: "/studio/create",
    icon: Palette,
    description: "Creative content generation"
  },
  {
    title: "Optimize",
    href: "/studio/optimise",
    icon: TrendingUp,
    description: "Performance optimization"
  },
  {
    title: "Execute",
    href: "/studio/execute",
    icon: CheckCircle,
    description: "Campaign execution"
  },
  {
    title: "Analyse",
    href: "/studio/analyse",
    icon: BarChart3,
    description: "Analytics and insights"
  },
  {
    title: "Brand",
    href: "/studio/brand",
    icon: Tag,
    description: "Brand management"
  }
]

// AI Tools
const aiTools = [
  {
    title: "AI Assistant",
    href: "/studio/ai-assistant",
    icon: Bot,
    description: "AI-powered marketing assistant"
  },
  {
    title: "Script Generator",
    href: "/studio/script-generator",
    icon: FileText,
    description: "Generate video ad scripts"
  },
  {
    title: "Creative Ideas",
    href: "/studio/creative-ideas",
    icon: Lightbulb,
    description: "AI-generated creative concepts"
  }
]

function StudioSidebar() {
  const { user } = useUser()
  const { signOut } = useClerk()
  const pathname = usePathname()
  const [showAIToolsSubmenu, setShowAIToolsSubmenu] = useState(false)

  return (
    <Sidebar variant="inset" collapsible="icon">
      <SidebarHeader className="group-data-[collapsible=icon]:p-0">
        <div className="flex items-center justify-start group-data-[collapsible=icon]:justify-center p-2 group-data-[collapsible=icon]:p-0">
          <Link href="/studio/campaign" className="flex items-center gap-2 hover:opacity-80 transition-opacity group-data-[collapsible=icon]:w-12 group-data-[collapsible=icon]:h-12 group-data-[collapsible=icon]:justify-center">
            <Image alt="Breakdown.ad Logo" src="/logo.png" width={20} height={20} className="rounded-sm w-5 h-5" />
            <span className="text-xl font-gegola tracking-wider bg-gradient-to-r from-blue-500 to-purple-500 bg-clip-text text-transparent group-data-[collapsible=icon]:hidden">
              Studio
            </span>
          </Link>
        </div>
      </SidebarHeader>
      
      <SidebarContent>
        {/* Studio Tools */}
        <SidebarGroup>
          <SidebarGroupLabel>Studio Tools</SidebarGroupLabel>
          <SidebarGroupContent>
            <SidebarMenu>
              {studioNavItems.map((item) => {
                const isActive = pathname === item.href || pathname.startsWith(item.href + '/')
                return (
                  <SidebarMenuItem key={item.href}>
                    <SidebarMenuButton
                      asChild
                      isActive={isActive}
                      tooltip={item.description}
                      className="group-data-[collapsible=icon]:w-12 group-data-[collapsible=icon]:h-12 group-data-[collapsible=icon]:p-0 group-data-[collapsible=icon]:justify-center"
                    >
                      <Link href={item.href}>
                        <item.icon className="w-5 h-5" />
                        <span className="group-data-[collapsible=icon]:hidden text-sm">{item.title}</span>
                      </Link>
                    </SidebarMenuButton>
                  </SidebarMenuItem>
                )
              })}
            </SidebarMenu>
          </SidebarGroupContent>
        </SidebarGroup>

        {/* AI Tools */}
        <SidebarGroup>
          <SidebarGroupLabel>AI Tools</SidebarGroupLabel>
          <SidebarGroupContent>
            <SidebarMenu>
              {aiTools.map((tool) => {
                const isActive = pathname === tool.href
                return (
                  <SidebarMenuItem key={tool.href}>
                    <SidebarMenuButton
                      asChild
                      isActive={isActive}
                      tooltip={tool.description}
                      className="group-data-[collapsible=icon]:w-12 group-data-[collapsible=icon]:h-12 group-data-[collapsible=icon]:p-0 group-data-[collapsible=icon]:justify-center"
                    >
                      <Link href={tool.href}>
                        <tool.icon className="w-5 h-5" />
                        <span className="group-data-[collapsible=icon]:hidden text-sm">{tool.title}</span>
                        {tool.title === "AI Assistant" && (
                          <Sparkles className="w-3 h-3 ml-auto text-yellow-500 group-data-[collapsible=icon]:hidden" />
                        )}
                      </Link>
                    </SidebarMenuButton>
                  </SidebarMenuItem>
                )
              })}
            </SidebarMenu>
          </SidebarGroupContent>
        </SidebarGroup>

        {/* Quick Actions */}
        <SidebarGroup>
          <SidebarGroupLabel>Quick Actions</SidebarGroupLabel>
          <SidebarGroupContent>
            <SidebarMenu>
              <SidebarMenuItem>
                <SidebarMenuButton
                  asChild
                  tooltip="Create new campaign"
                  className="group-data-[collapsible=icon]:w-12 group-data-[collapsible=icon]:h-12 group-data-[collapsible=icon]:p-0 group-data-[collapsible=icon]:justify-center"
                >
                  <Link href="/studio/campaign/create">
                    <Plus className="w-5 h-5" />
                    <span className="group-data-[collapsible=icon]:hidden text-sm">New Campaign</span>
                  </Link>
                </SidebarMenuButton>
              </SidebarMenuItem>
              <SidebarMenuItem>
                <SidebarMenuButton
                  asChild
                  tooltip="Back to Ad Library"
                  className="group-data-[collapsible=icon]:w-12 group-data-[collapsible=icon]:h-12 group-data-[collapsible=icon]:p-0 group-data-[collapsible=icon]:justify-center"
                >
                  <Link href="/ad-library">
                    <Library className="w-5 h-5" />
                    <span className="group-data-[collapsible=icon]:hidden text-sm">Ad Library</span>
                  </Link>
                </SidebarMenuButton>
              </SidebarMenuItem>
            </SidebarMenu>
          </SidebarGroupContent>
        </SidebarGroup>
      </SidebarContent>
      
      <SidebarFooter>
        <SidebarMenu>
          <SidebarMenuItem>
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <SidebarMenuButton
                  size="lg"
                  className="data-[state=open]:bg-sidebar-accent data-[state=open]:text-sidebar-accent-foreground"
                >
                  <Avatar className="h-8 w-8 rounded-lg">
                    <AvatarImage src={user?.imageUrl} alt={user?.fullName || "User Avatar"} />
                    <AvatarFallback className="rounded-lg">
                      {user?.fullName?.charAt(0)?.toUpperCase() ?? "U"}
                    </AvatarFallback>
                  </Avatar>
                  <div className="grid flex-1 text-left text-sm leading-tight">
                    <span className="truncate font-semibold">
                      {user?.fullName ?? "User Name"}
                    </span>
                    <span className="truncate text-xs">
                      {user?.primaryEmailAddress?.emailAddress ?? "<EMAIL>"}
                    </span>
                  </div>
                  <ChevronRight className="ml-auto size-4" />
                </SidebarMenuButton>
              </DropdownMenuTrigger>
              <DropdownMenuContent
                className="w-[--radix-dropdown-menu-trigger-width] min-w-56 rounded-lg"
                side="bottom"
                align="end"
                sideOffset={4}
              >
                <DropdownMenuItem asChild>
                  <Link href="/ad-library">
                    <Library className="mr-2 h-4 w-4" />
                    <span>Ad Library</span>
                  </Link>
                </DropdownMenuItem>
                <DropdownMenuItem asChild>
                  <Link href="/billing">
                    <CreditCard className="mr-2 h-4 w-4" />
                    <span>Billing</span>
                  </Link>
                </DropdownMenuItem>
                <DropdownMenuItem asChild>
                  <Link href="/settings">
                    <Settings className="mr-2 h-4 w-4" />
                    <span>Settings</span>
                  </Link>
                </DropdownMenuItem>
                <DropdownMenuItem asChild>
                  <Link href="/help">
                    <HelpCircle className="mr-2 h-4 w-4" />
                    <span>Help</span>
                  </Link>
                </DropdownMenuItem>
                <DropdownMenuSeparator />
                <DropdownMenuItem onClick={() => signOut()}>
                  <LogOut className="mr-2 h-4 w-4" />
                  <span>Log out</span>
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </SidebarMenuItem>
        </SidebarMenu>
      </SidebarFooter>
      <SidebarRail />
    </Sidebar>
  )
}

function StudioHeader() {
  const pathname = usePathname()

  // Generate breadcrumbs based on pathname
  const generateBreadcrumbs = () => {
    const segments = pathname.split('/').filter(Boolean)
    const breadcrumbs = []

    if (segments.length > 1) {
      breadcrumbs.push({
        label: 'Studio',
        href: '/studio/campaign',
        isActive: false
      })

      if (segments[1] === 'campaign') {
        if (segments.length === 2) {
          breadcrumbs.push({
            label: 'Campaigns',
            href: '/studio/campaign',
            isActive: true
          })
        } else if (segments[2] === 'create') {
          breadcrumbs.push({
            label: 'Campaigns',
            href: '/studio/campaign',
            isActive: false
          })
          breadcrumbs.push({
            label: 'Create Campaign',
            href: '/studio/campaign/create',
            isActive: true
          })
        } else {
          breadcrumbs.push({
            label: 'Campaigns',
            href: '/studio/campaign',
            isActive: false
          })
          breadcrumbs.push({
            label: `Campaign ${segments[2]}`,
            href: `/studio/campaign/${segments[2]}`,
            isActive: true
          })
        }
      } else {
        const toolName = segments[1].charAt(0).toUpperCase() + segments[1].slice(1)
        breadcrumbs.push({
          label: toolName,
          href: `/${segments.join('/')}`,
          isActive: true
        })
      }
    }

    return breadcrumbs
  }

  const breadcrumbs = generateBreadcrumbs()

  return (
    <header className="flex h-16 shrink-0 items-center gap-2 transition-[width,height] ease-linear group-has-[[data-collapsible=icon]]/sidebar-wrapper:h-12">
      <div className="flex items-center justify-between w-full px-4">
        {/* Left side - Logo and breadcrumbs */}
        <div className="flex items-center gap-2">
          <Link href="/studio/campaign" className="flex items-center gap-2 hover:opacity-80 transition-opacity md:hidden">
            <Image alt="Breakdown.ad Logo" src="/logo.png" width={40} height={40} className="w-6 h-6 rounded-sm" />
            <span className="text-lg font-gegola tracking-wider bg-gradient-to-r from-blue-500 to-purple-500 bg-clip-text text-transparent">
              Studio
            </span>
          </Link>

          {/* Desktop breadcrumbs */}
          <div className="hidden md:flex items-center gap-2">
            <SidebarTrigger className="-ml-1 w-6 h-6" />
            <Separator orientation="vertical" className="mr-2 h-4" />
            <Breadcrumb>
              <BreadcrumbList>
                {breadcrumbs.map((crumb, index) => (
                  <React.Fragment key={crumb.href}>
                    <BreadcrumbItem>
                      {crumb.isActive ? (
                        <BreadcrumbPage>{crumb.label}</BreadcrumbPage>
                      ) : (
                        <BreadcrumbLink asChild>
                          <Link href={crumb.href}>{crumb.label}</Link>
                        </BreadcrumbLink>
                      )}
                    </BreadcrumbItem>
                    {index < breadcrumbs.length - 1 && <BreadcrumbSeparator />}
                  </React.Fragment>
                ))}
              </BreadcrumbList>
            </Breadcrumb>
          </div>
        </div>

        {/* Right side - Mobile menu */}
        <div className="flex items-center gap-1">
          <MobileMenuButton className="md:hidden" />
        </div>
      </div>
    </header>
  )
}

export default function StudioLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <SidebarProvider defaultOpen={true}>
      <StudioSidebar />
      <SidebarInset className="overflow-hidden">
        <Suspense>
          <StudioHeader />
        </Suspense>
        <div className="flex flex-1 flex-col gap-4 p-2 md:p-4 pt-0 min-w-0 overflow-hidden">
          {children}
        </div>
      </SidebarInset>
    </SidebarProvider>
  )
}
