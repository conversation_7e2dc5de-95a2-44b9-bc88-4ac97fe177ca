'use client'

import { useState } from 'react'
import { useRouter } from 'next/navigation'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Badge } from '@/components/ui/badge'
import { 
  ArrowLeft,
  Calendar,
  DollarSign,
  Target,
  Users,
  Lightbulb,
  FileText,
  Plus,
  X,
  Save,
  Send
} from 'lucide-react'

const campaignTemplates = [
  {
    id: 'product-launch',
    name: 'Product Launch',
    description: 'Launch a new product or service',
    objectives: ['Increase brand awareness', 'Drive product adoption', 'Generate buzz'],
    suggestedBudget: '$150,000 - $500,000'
  },
  {
    id: 'brand-awareness',
    name: 'Brand Awareness',
    description: 'Build recognition and recall',
    objectives: ['Increase brand recognition', 'Improve brand sentiment', 'Expand reach'],
    suggestedBudget: '$100,000 - $300,000'
  },
  {
    id: 'seasonal-campaign',
    name: 'Seasonal Campaign',
    description: 'Holiday or seasonal promotion',
    objectives: ['Drive seasonal sales', 'Capture holiday sentiment', 'Increase foot traffic'],
    suggestedBudget: '$200,000 - $750,000'
  },
  {
    id: 'custom',
    name: 'Custom Campaign',
    description: 'Start from scratch',
    objectives: [],
    suggestedBudget: 'Custom'
  }
]

const targetAudiences = [
  'Gen Z (18-24)',
  'Millennials (25-40)',
  'Gen X (41-56)',
  'Baby Boomers (57+)',
  'Parents with young children',
  'Working professionals',
  'Students',
  'Retirees',
  'Small business owners',
  'Tech enthusiasts',
  'Health & wellness focused',
  'Eco-conscious consumers'
]

const channels = [
  'Television',
  'Digital/Online Video',
  'Social Media',
  'Radio',
  'Print',
  'Out-of-Home',
  'Email',
  'Influencer Marketing',
  'Content Marketing',
  'Search Marketing'
]

interface CampaignBrief {
  template: string
  name: string
  objectives: string[]
  customObjective: string
  targetAudience: string[]
  budget: string
  timeline: {
    startDate: string
    endDate: string
    launchDate: string
  }
  keyMessage: string
  callToAction: string
  channels: string[]
  brandGuidelines: string
  compliance: string[]
  stakeholders: string[]
  successMetrics: string[]
}

export default function CreateCampaign() {
  const router = useRouter()
  const [selectedTemplate, setSelectedTemplate] = useState('')
  const [brief, setBrief] = useState<CampaignBrief>({
    template: '',
    name: '',
    objectives: [],
    customObjective: '',
    targetAudience: [],
    budget: '',
    timeline: {
      startDate: '',
      endDate: '',
      launchDate: ''
    },
    keyMessage: '',
    callToAction: '',
    channels: [],
    brandGuidelines: '',
    compliance: [],
    stakeholders: [],
    successMetrics: []
  })

  const handleTemplateSelect = (templateId: string) => {
    const template = campaignTemplates.find(t => t.id === templateId)
    if (template) {
      setSelectedTemplate(templateId)
      setBrief(prev => ({
        ...prev,
        template: templateId,
        objectives: template.objectives
      }))
    }
  }

  const addCustomObjective = () => {
    if (brief.customObjective.trim()) {
      setBrief(prev => ({
        ...prev,
        objectives: [...prev.objectives, prev.customObjective.trim()],
        customObjective: ''
      }))
    }
  }

  const removeObjective = (index: number) => {
    setBrief(prev => ({
      ...prev,
      objectives: prev.objectives.filter((_, i) => i !== index)
    }))
  }

  const addToArray = (field: keyof CampaignBrief, value: string) => {
    if (value.trim()) {
      setBrief(prev => ({
        ...prev,
        [field]: [...(prev[field] as string[]), value.trim()]
      }))
    }
  }

  const removeFromArray = (field: keyof CampaignBrief, index: number) => {
    setBrief(prev => ({
      ...prev,
      [field]: (prev[field] as string[]).filter((_, i) => i !== index)
    }))
  }

  const handleSaveDraft = () => {
    // TODO: Save to backend
    console.log('Saving draft:', brief)
    alert('Campaign brief saved as draft!')
  }

  const handleSubmitForReview = () => {
    // TODO: Submit to backend and notify reviewers
    console.log('Submitting for review:', brief)
    alert('Campaign brief submitted for review!')
    router.push('/studio/campaign')
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between py-6">
            <div className="flex items-center gap-4">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => router.push('/studio/campaign')}
                className="flex items-center gap-2"
              >
                <ArrowLeft className="w-4 h-4" />
                Back to Campaigns
              </Button>
              <div>
                <h1 className="text-2xl font-bold text-gray-900">Create New Campaign</h1>
                <p className="text-gray-600">Define your campaign brief and objectives</p>
              </div>
            </div>
            <div className="flex items-center gap-3">
              <Button variant="outline" onClick={handleSaveDraft}>
                <Save className="w-4 h-4 mr-2" />
                Save Draft
              </Button>
              <Button onClick={handleSubmitForReview}>
                <Send className="w-4 h-4 mr-2" />
                Submit for Review
              </Button>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="space-y-8">
          {/* Template Selection */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <FileText className="w-5 h-5" />
                Choose Campaign Template
              </CardTitle>
              <CardDescription>
                Select a template to get started quickly, or choose custom for a blank slate
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {campaignTemplates.map((template) => (
                  <div
                    key={template.id}
                    className={`p-4 border-2 rounded-lg cursor-pointer transition-all ${
                      selectedTemplate === template.id
                        ? 'border-blue-500 bg-blue-50'
                        : 'border-gray-200 hover:border-gray-300'
                    }`}
                    onClick={() => handleTemplateSelect(template.id)}
                  >
                    <h3 className="font-medium text-gray-900">{template.name}</h3>
                    <p className="text-sm text-gray-600 mt-1">{template.description}</p>
                    <p className="text-xs text-gray-500 mt-2">{template.suggestedBudget}</p>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {selectedTemplate && (
            <>
              {/* Basic Information */}
              <Card>
                <CardHeader>
                  <CardTitle>Campaign Information</CardTitle>
                  <CardDescription>Basic details about your campaign</CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div>
                    <Label htmlFor="campaign-name">Campaign Name *</Label>
                    <Input
                      id="campaign-name"
                      placeholder="e.g., Summer Product Launch 2024"
                      value={brief.name}
                      onChange={(e) => setBrief(prev => ({ ...prev, name: e.target.value }))}
                    />
                  </div>

                  <div>
                    <Label>Campaign Objectives</Label>
                    <div className="flex flex-wrap gap-2 mt-2 mb-3">
                      {brief.objectives.map((objective, index) => (
                        <Badge key={index} variant="secondary" className="flex items-center gap-1">
                          {objective}
                          <X 
                            className="w-3 h-3 cursor-pointer"
                            onClick={() => removeObjective(index)}
                          />
                        </Badge>
                      ))}
                    </div>
                    <div className="flex gap-2">
                      <Input
                        placeholder="Add custom objective..."
                        value={brief.customObjective}
                        onChange={(e) => setBrief(prev => ({ ...prev, customObjective: e.target.value }))}
                        onKeyPress={(e) => e.key === 'Enter' && addCustomObjective()}
                      />
                      <Button type="button" size="sm" onClick={addCustomObjective}>
                        <Plus className="w-4 h-4" />
                      </Button>
                    </div>
                  </div>

                  <div>
                    <Label htmlFor="key-message">Key Message *</Label>
                    <Textarea
                      id="key-message"
                      placeholder="What is the main message you want to communicate?"
                      rows={3}
                      value={brief.keyMessage}
                      onChange={(e) => setBrief(prev => ({ ...prev, keyMessage: e.target.value }))}
                    />
                  </div>

                  <div>
                    <Label htmlFor="call-to-action">Call to Action</Label>
                    <Input
                      id="call-to-action"
                      placeholder="e.g., Visit our website, Download the app, Call now"
                      value={brief.callToAction}
                      onChange={(e) => setBrief(prev => ({ ...prev, callToAction: e.target.value }))}
                    />
                  </div>
                </CardContent>
              </Card>

              {/* Target Audience */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Target className="w-5 h-5" />
                    Target Audience
                  </CardTitle>
                  <CardDescription>Who are you trying to reach?</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div>
                      <Label>Primary Audiences</Label>
                      <div className="flex flex-wrap gap-2 mt-2 mb-3">
                        {brief.targetAudience.map((audience, index) => (
                          <Badge key={index} variant="outline" className="flex items-center gap-1">
                            {audience}
                            <X 
                              className="w-3 h-3 cursor-pointer"
                              onClick={() => removeFromArray('targetAudience', index)}
                            />
                          </Badge>
                        ))}
                      </div>
                      <Select onValueChange={(value) => addToArray('targetAudience', value)}>
                        <SelectTrigger>
                          <SelectValue placeholder="Select target audiences" />
                        </SelectTrigger>
                        <SelectContent>
                          {targetAudiences.map((audience) => (
                            <SelectItem key={audience} value={audience}>
                              {audience}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Budget & Timeline */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <DollarSign className="w-5 h-5" />
                    Budget & Timeline
                  </CardTitle>
                  <CardDescription>Financial and scheduling constraints</CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div>
                    <Label htmlFor="budget">Total Budget *</Label>
                    <Input
                      id="budget"
                      type="text"
                      placeholder="e.g., $250,000"
                      value={brief.budget}
                      onChange={(e) => setBrief(prev => ({ ...prev, budget: e.target.value }))}
                    />
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div>
                      <Label htmlFor="start-date">Campaign Start</Label>
                      <Input
                        id="start-date"
                        type="date"
                        value={brief.timeline.startDate}
                        onChange={(e) => setBrief(prev => ({
                          ...prev,
                          timeline: { ...prev.timeline, startDate: e.target.value }
                        }))}
                      />
                    </div>
                    <div>
                      <Label htmlFor="launch-date">Launch Date *</Label>
                      <Input
                        id="launch-date"
                        type="date"
                        value={brief.timeline.launchDate}
                        onChange={(e) => setBrief(prev => ({
                          ...prev,
                          timeline: { ...prev.timeline, launchDate: e.target.value }
                        }))}
                      />
                    </div>
                    <div>
                      <Label htmlFor="end-date">Campaign End</Label>
                      <Input
                        id="end-date"
                        type="date"
                        value={brief.timeline.endDate}
                        onChange={(e) => setBrief(prev => ({
                          ...prev,
                          timeline: { ...prev.timeline, endDate: e.target.value }
                        }))}
                      />
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Channels & Distribution */}
              <Card>
                <CardHeader>
                  <CardTitle>Channels & Distribution</CardTitle>
                  <CardDescription>Where will this campaign be deployed?</CardDescription>
                </CardHeader>
                <CardContent>
                  <div>
                    <Label>Marketing Channels</Label>
                    <div className="flex flex-wrap gap-2 mt-2 mb-3">
                      {brief.channels.map((channel, index) => (
                        <Badge key={index} variant="outline" className="flex items-center gap-1">
                          {channel}
                          <X 
                            className="w-3 h-3 cursor-pointer"
                            onClick={() => removeFromArray('channels', index)}
                          />
                        </Badge>
                      ))}
                    </div>
                    <Select onValueChange={(value) => addToArray('channels', value)}>
                      <SelectTrigger>
                        <SelectValue placeholder="Select marketing channels" />
                      </SelectTrigger>
                      <SelectContent>
                        {channels.map((channel) => (
                          <SelectItem key={channel} value={channel}>
                            {channel}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                </CardContent>
              </Card>

              {/* Brand Guidelines & Compliance */}
              <Card>
                <CardHeader>
                  <CardTitle>Brand Guidelines & Compliance</CardTitle>
                  <CardDescription>Important constraints and requirements</CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div>
                    <Label htmlFor="brand-guidelines">Brand Guidelines</Label>
                    <Textarea
                      id="brand-guidelines"
                      placeholder="Include any specific brand guidelines, tone of voice, visual requirements..."
                      rows={4}
                      value={brief.brandGuidelines}
                      onChange={(e) => setBrief(prev => ({ ...prev, brandGuidelines: e.target.value }))}
                    />
                  </div>
                </CardContent>
              </Card>

              {/* Submit Actions */}
              <div className="flex justify-end gap-3 pt-6">
                <Button variant="outline" onClick={handleSaveDraft}>
                  <Save className="w-4 h-4 mr-2" />
                  Save as Draft
                </Button>
                <Button onClick={handleSubmitForReview}>
                  <Send className="w-4 h-4 mr-2" />
                  Submit for Review
                </Button>
              </div>
            </>
          )}
        </div>
      </div>
    </div>
  )
}