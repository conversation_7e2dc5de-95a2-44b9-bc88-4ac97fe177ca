'use client'

import { useState, useEffect } from 'react'
import { use<PERSON><PERSON><PERSON> } from 'next/navigation'
import { <PERSON><PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import {
  Plus,
  Search,
  Filter,
  Calendar,
  Users,
  Target,
  BarChart3,
  Clock,
  CheckCircle,
  AlertCircle,
  PlayCircle,
  MessageSquare,
  Bot,
  Sparkles,
  TrendingUp,
  FileText,
  Eye
} from 'lucide-react'
import { useAuth } from '@/hooks/useAuth'
import CampaignChatInterface from '@/components/campaign/CampaignChatInterface'

// Dummy data for campaigns
const dummyCampaigns = [
  {
    id: 1,
    name: "Summer Product Launch 2024",
    status: "in-progress",
    stage: "scriptwriting",
    progress: 35,
    dueDate: "2024-03-15",
    team: ["<PERSON>", "<PERSON>", "<PERSON>"],
    budget: 250000,
    objective: "Increase brand awareness for new summer collection",
    lastUpdated: "2 hours ago"
  },
  {
    id: 2,
    name: "Holiday Campaign - Black Friday",
    status: "pending-review",
    stage: "creative-review",
    progress: 72,
    dueDate: "2024-02-28",
    team: ["John Smith", "Emma Davis", "Alex Thompson"],
    budget: 450000,
    objective: "Drive sales during Black Friday weekend",
    lastUpdated: "1 day ago"
  },
  {
    id: 3,
    name: "Brand Repositioning Campaign",
    status: "completed",
    stage: "launched",
    progress: 100,
    dueDate: "2024-01-20",
    team: ["Sarah Chen", "David Park", "Jennifer Liu"],
    budget: 320000,
    objective: "Reposition brand for younger demographic",
    lastUpdated: "1 week ago"
  },
  {
    id: 4,
    name: "Q2 Social Media Push",
    status: "draft",
    stage: "briefing",
    progress: 15,
    dueDate: "2024-04-01",
    team: ["Maria Rodriguez", "Tom Wilson"],
    budget: 125000,
    objective: "Boost social media engagement and followers",
    lastUpdated: "3 days ago"
  }
]

const statusConfig: Record<string, { color: string; label: string; icon: any }> = {
  "draft": { color: "bg-gray-500", label: "Draft", icon: Clock },
  "in-progress": { color: "bg-blue-500", label: "In Progress", icon: PlayCircle },
  "pending-review": { color: "bg-yellow-500", label: "Pending Review", icon: AlertCircle },
  "completed": { color: "bg-green-500", label: "Completed", icon: CheckCircle }
}

const stageConfig: Record<string, { label: string; progress: number }> = {
  "briefing": { label: "Briefing", progress: 10 },
  "scriptwriting": { label: "Scriptwriting", progress: 30 },
  "creative-review": { label: "Creative Review", progress: 60 },
  "production": { label: "Production", progress: 80 },
  "launched": { label: "Launched", progress: 100 }
}

export default function CampaignWorkspace() {
  const router = useRouter()
  const { isAuthenticated } = useAuth()
  const [searchTerm, setSearchTerm] = useState('')
  const [statusFilter, setStatusFilter] = useState('all')
  const [campaigns, setCampaigns] = useState(dummyCampaigns)
  const [isLoading, setIsLoading] = useState(false)
  const [showAIAssistant, setShowAIAssistant] = useState(false)
  const [statistics, setStatistics] = useState({
    total: 12,
    in_progress: 5,
    avg_time_to_launch: 28,
    total_budget: 2400000
  })

  const filteredCampaigns = campaigns.filter(campaign => {
    const matchesSearch = campaign.name.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesStatus = statusFilter === 'all' || campaign.status === statusFilter
    return matchesSearch && matchesStatus
  })

  // Load campaigns from API
  useEffect(() => {
    if (isAuthenticated) {
      loadCampaigns()
    }
  }, [isAuthenticated])

  const loadCampaigns = async () => {
    try {
      setIsLoading(true)
      const response = await fetch('/api/campaigns')
      if (response.ok) {
        const data = await response.json()
        setCampaigns(data.campaigns || [])
        setStatistics(data.statistics || statistics)
      }
    } catch (error) {
      console.error('Error loading campaigns:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const handleCreateCampaign = () => {
    router.push('/studio/campaign/create')
  }

  const handleViewCampaign = (campaignId: number) => {
    router.push(`/studio/campaign/${campaignId}`)
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">Campaign Workspace</h1>
              <p className="text-gray-600 mt-1">AI-powered collaborative marketing campaign management</p>
            </div>
            <div className="flex items-center gap-3">
              <Button
                variant="outline"
                onClick={() => setShowAIAssistant(!showAIAssistant)}
                className="flex items-center gap-2"
              >
                <Bot className="w-4 h-4" />
                AI Assistant
                <Sparkles className="w-3 h-3" />
              </Button>
              <Button onClick={handleCreateCampaign} className="flex items-center gap-2">
                <Plus className="w-4 h-4" />
                New Campaign
              </Button>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
          {/* Main Content */}
          <div className="lg:col-span-3">
            {/* Stats Overview */}
            <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Total Campaigns</CardTitle>
                  <BarChart3 className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{statistics.total}</div>
                  <p className="text-xs text-muted-foreground">+2 from last month</p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">In Progress</CardTitle>
                  <PlayCircle className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{statistics.in_progress}</div>
                  <p className="text-xs text-muted-foreground">Active campaigns</p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Avg. Time to Launch</CardTitle>
                  <Clock className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{statistics.avg_time_to_launch}d</div>
                  <p className="text-xs text-muted-foreground">-3 days from last quarter</p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Total Budget</CardTitle>
                  <Target className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">${(statistics.total_budget / 1000000).toFixed(1)}M</div>
                  <p className="text-xs text-muted-foreground">Across all active campaigns</p>
                </CardContent>
              </Card>
            </div>

        {/* Filters and Search */}
        <div className="flex flex-col sm:flex-row justify-between items-center gap-4 mb-6">
          <div className="flex items-center gap-4 w-full sm:w-auto">
            <div className="relative flex-1 sm:w-64">
              <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
              <input
                type="text"
                placeholder="Search campaigns..."
                className="pl-10 pr-4 py-2 border border-gray-300 rounded-md w-full focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>
            <select 
              className="px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value)}
            >
              <option value="all">All Status</option>
              <option value="draft">Draft</option>
              <option value="in-progress">In Progress</option>
              <option value="pending-review">Pending Review</option>
              <option value="completed">Completed</option>
            </select>
          </div>
          <div className="flex items-center gap-2">
            <Button variant="outline" size="sm">
              <Filter className="w-4 h-4 mr-2" />
              More Filters
            </Button>
            <Button variant="outline" size="sm">
              <Calendar className="w-4 h-4 mr-2" />
              Timeline View
            </Button>
          </div>
        </div>

            {/* Campaign Cards */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {filteredCampaigns.map((campaign) => {
                const StatusIcon = statusConfig[campaign.status].icon
                const stageInfo = stageConfig[campaign.stage]

                return (
                  <Card key={campaign.id} className="hover:shadow-md transition-shadow cursor-pointer" onClick={() => handleViewCampaign(campaign.id)}>
                    <CardHeader>
                      <div className="flex justify-between items-start">
                        <div>
                          <CardTitle className="text-lg">{campaign.name}</CardTitle>
                          <CardDescription className="mt-1">{campaign.objective}</CardDescription>
                        </div>
                        <Badge className={`${statusConfig[campaign.status].color} text-white`}>
                          <StatusIcon className="w-3 h-3 mr-1" />
                          {statusConfig[campaign.status].label}
                        </Badge>
                      </div>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-4">
                        {/* Progress */}
                        <div>
                          <div className="flex justify-between items-center mb-2">
                            <span className="text-sm font-medium">Progress</span>
                            <span className="text-sm text-gray-500">{campaign.progress}%</span>
                          </div>
                          <Progress value={campaign.progress} className="h-2" />
                          <p className="text-xs text-gray-500 mt-1">Current stage: {stageInfo.label}</p>
                        </div>

                        {/* Details */}
                        <div className="grid grid-cols-2 gap-4 text-sm">
                          <div>
                            <p className="text-gray-500">Due Date</p>
                            <p className="font-medium">{new Date(campaign.dueDate).toLocaleDateString()}</p>
                          </div>
                          <div>
                            <p className="text-gray-500">Budget</p>
                            <p className="font-medium">${campaign.budget.toLocaleString()}</p>
                          </div>
                        </div>

                        {/* Team */}
                        <div>
                          <p className="text-sm text-gray-500 mb-2">Team</p>
                          <div className="flex items-center gap-2">
                            <Users className="w-4 h-4 text-gray-400" />
                            <div className="flex -space-x-2">
                              {campaign.team.slice(0, 3).map((member, index) => (
                                <div
                                  key={index}
                                  className="w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center text-white text-xs font-medium border-2 border-white"
                                  title={member}
                                >
                                  {member.split(' ').map(n => n[0]).join('')}
                                </div>
                              ))}
                              {campaign.team.length > 3 && (
                                <div className="w-6 h-6 bg-gray-300 rounded-full flex items-center justify-center text-gray-600 text-xs font-medium border-2 border-white">
                                  +{campaign.team.length - 3}
                                </div>
                              )}
                            </div>
                          </div>
                        </div>

                        {/* Last Updated */}
                        <div className="flex justify-between items-center pt-2 border-t">
                          <span className="text-xs text-gray-500">Last updated: {campaign.lastUpdated}</span>
                          <Button variant="ghost" size="sm" onClick={(e) => {
                            e.stopPropagation()
                            handleViewCampaign(campaign.id)
                          }}>
                            <Eye className="w-4 h-4 mr-1" />
                            View Details
                          </Button>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                )
              })}
            </div>

            {/* Empty State */}
            {filteredCampaigns.length === 0 && (
              <Card className="text-center py-12">
                <CardContent>
                  <div className="text-gray-400 mb-4">
                    <BarChart3 className="w-12 h-12 mx-auto" />
                  </div>
                  <h3 className="text-lg font-medium text-gray-900 mb-2">No campaigns found</h3>
                  <p className="text-gray-500 mb-4">
                    {searchTerm || statusFilter !== 'all'
                      ? 'Try adjusting your search or filters'
                      : 'Create your first campaign to get started'
                    }
                  </p>
                  <Button onClick={handleCreateCampaign}>
                    <Plus className="w-4 h-4 mr-2" />
                    Create New Campaign
                  </Button>
                </CardContent>
              </Card>
            )}
          </div>

          {/* AI Assistant Sidebar */}
          {showAIAssistant && (
            <div className="lg:col-span-1">
              <Card className="h-[600px]">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Bot className="w-5 h-5" />
                    AI Campaign Assistant
                    <Sparkles className="w-4 h-4 text-yellow-500" />
                  </CardTitle>
                  <CardDescription>
                    Get AI-powered assistance for campaign strategy, creative ideas, and optimization
                  </CardDescription>
                </CardHeader>
                <CardContent className="p-0 h-[calc(100%-120px)]">
                  <CampaignChatInterface
                    campaignId="general"
                    campaignData={{
                      name: "Campaign Workspace",
                      stage: "planning",
                      progress: 0,
                      objectives: ["Improve campaign efficiency", "Generate better creative ideas"],
                      target_audience: ["Marketing professionals", "Campaign managers"]
                    }}
                  />
                </CardContent>
              </Card>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}