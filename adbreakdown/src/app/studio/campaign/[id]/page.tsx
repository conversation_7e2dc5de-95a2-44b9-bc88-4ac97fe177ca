'use client'

import { useState, useEffect } from 'react'
import { use<PERSON><PERSON><PERSON>, useRouter } from 'next/navigation'
import { <PERSON><PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Textarea } from '@/components/ui/textarea'
import {
  ArrowLeft,
  Edit,
  MessageSquare,
  Clock,
  Users,
  FileText,
  Video,
  Image,
  CheckCircle,
  AlertCircle,
  PlayCircle,
  Plus,
  Send,
  Eye,
  Download,
  Share2,
  MoreHorizontal,
  Bot,
  Sparkles,
  Activity,
  TrendingUp
} from 'lucide-react'
import { useAuth } from '@/hooks/useAuth'
import CampaignChatInterface from '@/components/campaign/CampaignChatInterface'
import CollaborativeScriptEditor from '@/components/campaign/CollaborativeScriptEditor'

// Dummy campaign data
const dummyCampaign = {
  id: 1,
  name: "Summer Product Launch 2024",
  status: "in-progress",
  stage: "scriptwriting",
  progress: 35,
  dueDate: "2024-03-15",
  budget: 250000,
  objective: "Increase brand awareness for new summer collection",
  keyMessage: "Discover the freshest styles for your summer adventures",
  targetAudience: ["Gen Z (18-24)", "Millennials (25-40)"],
  channels: ["Digital/Online Video", "Social Media", "Influencer Marketing"],
  team: [
    { id: 1, name: "Sarah Chen", role: "Brand Manager", avatar: "SC" },
    { id: 2, name: "Mike Johnson", role: "Creative Director", avatar: "MJ" },
    { id: 3, name: "Lisa Wang", role: "Copywriter", avatar: "LW" },
    { id: 4, name: "Alex Thompson", role: "Producer", avatar: "AT" }
  ],
  timeline: {
    startDate: "2024-02-01",
    launchDate: "2024-03-15",
    endDate: "2024-04-30"
  },
  workflow: [
    { stage: "Brief", status: "completed", dueDate: "2024-02-05" },
    { stage: "Ideation", status: "completed", dueDate: "2024-02-10" },
    { stage: "Scriptwriting", status: "in-progress", dueDate: "2024-02-18" },
    { stage: "Storyboard", status: "pending", dueDate: "2024-02-25" },
    { stage: "Production", status: "pending", dueDate: "2024-03-10" },
    { stage: "Review", status: "pending", dueDate: "2024-03-12" },
    { stage: "Launch", status: "pending", dueDate: "2024-03-15" }
  ],
  assets: [
    { id: 1, name: "Campaign Brief v2.pdf", type: "document", status: "approved", uploadedBy: "Sarah Chen", date: "2024-02-05" },
    { id: 2, name: "Creative Concept Board", type: "image", status: "approved", uploadedBy: "Mike Johnson", date: "2024-02-08" },
    { id: 3, name: "Script Draft v1.docx", type: "document", status: "in-review", uploadedBy: "Lisa Wang", date: "2024-02-15" },
    { id: 4, name: "Mood Board Collection", type: "image", status: "pending", uploadedBy: "Mike Johnson", date: "2024-02-16" }
  ]
}

const dummyComments = [
  {
    id: 1,
    author: "Sarah Chen",
    role: "Brand Manager",
    content: "The script looks great overall. Just need to make sure we emphasize the sustainability angle more prominently.",
    timestamp: "2 hours ago",
    replies: [
      {
        id: 2,
        author: "Lisa Wang",
        role: "Copywriter",
        content: "Good point! I'll add a stronger sustainability message in the opening lines.",
        timestamp: "1 hour ago"
      }
    ]
  },
  {
    id: 3,
    author: "Mike Johnson",
    role: "Creative Director",
    content: "Love the energy in this draft. The call-to-action could be more compelling though. Maybe something more action-oriented?",
    timestamp: "4 hours ago",
    replies: []
  }
]

const statusConfig: Record<string, { color: string; label: string; icon: any }> = {
  "completed": { color: "bg-green-500", label: "Completed", icon: CheckCircle },
  "in-progress": { color: "bg-blue-500", label: "In Progress", icon: PlayCircle },
  "in-review": { color: "bg-yellow-500", label: "In Review", icon: AlertCircle },
  "pending": { color: "bg-gray-400", label: "Pending", icon: Clock },
  "approved": { color: "bg-green-500", label: "Approved", icon: CheckCircle }
}

export default function CampaignDetail() {
  const params = useParams()
  const router = useRouter()
  const { isAuthenticated } = useAuth()
  const [campaign, setCampaign] = useState(dummyCampaign)
  const [comments, setComments] = useState(dummyComments)
  const [newComment, setNewComment] = useState('')
  const [isLoading, setIsLoading] = useState(false)
  const [showAIAssistant, setShowAIAssistant] = useState(false)
  const [activities, setActivities] = useState([])

  // Load campaign data from API
  useEffect(() => {
    if (params.id && isAuthenticated) {
      loadCampaignData()
    }
  }, [params.id, isAuthenticated])

  const loadCampaignData = async () => {
    try {
      setIsLoading(true)
      const response = await fetch(`/api/campaigns/${params.id}`)
      if (response.ok) {
        const data = await response.json()
        setCampaign(data.campaign || dummyCampaign)
        setActivities(data.activities || [])
        // Update comments if available
        if (data.campaign?.campaign_comments) {
          setComments(data.campaign.campaign_comments)
        }
      }
    } catch (error) {
      console.error('Error loading campaign:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const addComment = () => {
    if (newComment.trim()) {
      const comment = {
        id: Date.now(),
        author: "Current User",
        role: "Team Member",
        content: newComment.trim(),
        timestamp: "Just now",
        replies: []
      }
      setComments([comment, ...comments])
      setNewComment('')
    }
  }

  const getAssetIcon = (type: string) => {
    switch (type) {
      case 'document': return FileText
      case 'image': return Image
      case 'video': return Video
      default: return FileText
    }
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between py-6">
            <div className="flex items-center gap-4">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => router.push('/studio/campaign')}
                className="flex items-center gap-2"
              >
                <ArrowLeft className="w-4 h-4" />
                Back to Campaigns
              </Button>
              <div>
                <div className="flex items-center gap-3">
                  <h1 className="text-2xl font-bold text-gray-900">{campaign.name}</h1>
                  <Badge className={`${statusConfig[campaign.status].color} text-white`}>
                    {statusConfig[campaign.status].label}
                  </Badge>
                </div>
                <p className="text-gray-600">{campaign.objective}</p>
              </div>
            </div>
            <div className="flex items-center gap-3">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setShowAIAssistant(!showAIAssistant)}
              >
                <Bot className="w-4 h-4 mr-2" />
                AI Assistant
                <Sparkles className="w-3 h-3 ml-1" />
              </Button>
              <Button variant="outline" size="sm">
                <Share2 className="w-4 h-4 mr-2" />
                Share
              </Button>
              <Button variant="outline" size="sm">
                <Download className="w-4 h-4 mr-2" />
                Export
              </Button>
              <Button size="sm">
                <Edit className="w-4 h-4 mr-2" />
                Edit Brief
              </Button>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Main Content */}
          <div className="lg:col-span-2 space-y-6">
            {/* Progress Overview */}
            <Card>
              <CardHeader>
                <CardTitle>Campaign Progress</CardTitle>
                <CardDescription>Current status and upcoming milestones</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div>
                    <div className="flex justify-between items-center mb-2">
                      <span className="text-sm font-medium">Overall Progress</span>
                      <span className="text-sm text-gray-500">{campaign.progress}%</span>
                    </div>
                    <Progress value={campaign.progress} className="h-3" />
                  </div>

                  <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4 mt-6">
                    {campaign.workflow.map((stage, index) => {
                      const StageIcon = statusConfig[stage.status].icon
                      return (
                        <div key={index} className="flex items-center gap-3 p-3 bg-gray-50 rounded-lg">
                          <div className={`p-2 rounded-full ${statusConfig[stage.status].color}`}>
                            <StageIcon className="w-4 h-4 text-white" />
                          </div>
                          <div>
                            <p className="font-medium text-sm">{stage.stage}</p>
                            <p className="text-xs text-gray-500">Due {new Date(stage.dueDate).toLocaleDateString()}</p>
                          </div>
                        </div>
                      )
                    })}
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Tabs for Different Views */}
            <Tabs defaultValue="script" className="space-y-4">
              <TabsList className="grid w-full grid-cols-5">
                <TabsTrigger value="script">Script Editor</TabsTrigger>
                <TabsTrigger value="assets">Assets</TabsTrigger>
                <TabsTrigger value="timeline">Timeline</TabsTrigger>
                <TabsTrigger value="analysis">AI Analysis</TabsTrigger>
                <TabsTrigger value="ai-assistant">
                  <Bot className="w-4 h-4 mr-1" />
                  AI Assistant
                </TabsTrigger>
              </TabsList>

              <TabsContent value="script" className="space-y-4">
                <CollaborativeScriptEditor
                  campaignId={params.id as string}
                  assetId="script-1"
                  initialContent={`SCENE 1: FADE IN

[Young woman in her twenties walks through a vibrant summer market, sunlight filtering through colorful fabric canopies]

VOICEOVER (warm, energetic):
"This summer, don't just follow trends... create your own story."

[Cut to: Close-up of sustainable fabric tag, camera pulls back to reveal flowing summer dress]

VOICEOVER (continues):
"Introducing our new eco-conscious collection. Style that feels as good as it looks."

[Montage: Different people of various ages wearing pieces from the collection in different summer settings - beach, city, festival]

SUPER: "Sustainable. Stylish. Yours."

CALL TO ACTION:
"Discover your summer story. Shop the collection now."`}
                  onSave={(content) => {
                    console.log('Saving script:', content)
                    // Save to backend
                  }}
                  onSubmitForReview={(content) => {
                    console.log('Submitting for review:', content)
                    // Submit for review
                  }}
                />
              </TabsContent>

              <TabsContent value="assets" className="space-y-4">
                <Card>
                  <CardHeader>
                    <div className="flex justify-between items-center">
                      <div>
                        <CardTitle>Campaign Assets</CardTitle>
                        <CardDescription>All files and creative materials for this campaign</CardDescription>
                      </div>
                      <Button size="sm">
                        <Plus className="w-4 h-4 mr-2" />
                        Upload Asset
                      </Button>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-3">
                      {campaign.assets.map((asset) => {
                        const AssetIcon = getAssetIcon(asset.type)
                        const StatusIcon = statusConfig[asset.status].icon
                        return (
                          <div key={asset.id} className="flex items-center justify-between p-3 border rounded-lg hover:bg-gray-50">
                            <div className="flex items-center gap-3">
                              <AssetIcon className="w-5 h-5 text-gray-400" />
                              <div>
                                <p className="font-medium">{asset.name}</p>
                                <p className="text-sm text-gray-500">
                                  Uploaded by {asset.uploadedBy} • {asset.date}
                                </p>
                              </div>
                            </div>
                            <div className="flex items-center gap-3">
                              <Badge className={`${statusConfig[asset.status].color} text-white`}>
                                <StatusIcon className="w-3 h-3 mr-1" />
                                {statusConfig[asset.status].label}
                              </Badge>
                              <Button variant="ghost" size="sm">
                                <MoreHorizontal className="w-4 h-4" />
                              </Button>
                            </div>
                          </div>
                        )
                      })}
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>

              <TabsContent value="timeline" className="space-y-4">
                <Card>
                  <CardHeader>
                    <CardTitle>Project Timeline</CardTitle>
                    <CardDescription>Key milestones and deadlines</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      {campaign.workflow.map((stage, index) => (
                        <div key={index} className="flex items-center gap-4">
                          <div className="flex flex-col items-center">
                            <div className={`w-8 h-8 rounded-full flex items-center justify-center ${
                              stage.status === 'completed' ? 'bg-green-500' :
                              stage.status === 'in-progress' ? 'bg-blue-500' : 'bg-gray-300'
                            }`}>
                              <CheckCircle className="w-4 h-4 text-white" />
                            </div>
                            {index < campaign.workflow.length - 1 && (
                              <div className="w-px h-8 bg-gray-200 mt-2"></div>
                            )}
                          </div>
                          <div className="flex-1">
                            <div className="flex justify-between items-center">
                              <h3 className="font-medium">{stage.stage}</h3>
                              <span className="text-sm text-gray-500">
                                Due {new Date(stage.dueDate).toLocaleDateString()}
                              </span>
                            </div>
                            <p className="text-sm text-gray-600 capitalize">{stage.status.replace('-', ' ')}</p>
                          </div>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>

              <TabsContent value="analysis" className="space-y-4">
                <Card>
                  <CardHeader>
                    <CardTitle>AI Analysis & Recommendations</CardTitle>
                    <CardDescription>Insights and suggestions powered by AI</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                        <h4 className="font-medium text-green-800 mb-2">Brand Alignment Score: 85%</h4>
                        <p className="text-sm text-green-700">
                          Your script aligns well with brand guidelines. The sustainability messaging is strong and consistent with brand values.
                        </p>
                      </div>

                      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                        <h4 className="font-medium text-blue-800 mb-2">Target Audience Appeal</h4>
                        <p className="text-sm text-blue-700 mb-2">
                          Strong appeal to Gen Z and Millennials with authentic messaging and diverse representation.
                        </p>
                        <ul className="text-sm text-blue-700 space-y-1">
                          <li>• Authenticity score: 92%</li>
                          <li>• Emotional engagement: High</li>
                          <li>• Call-to-action clarity: Medium</li>
                        </ul>
                      </div>

                      <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                        <h4 className="font-medium text-yellow-800 mb-2">Optimization Suggestions</h4>
                        <ul className="text-sm text-yellow-700 space-y-2">
                          <li>• Consider making the call-to-action more specific and urgent</li>
                          <li>• Add more concrete sustainability benefits (e.g., "made from 80% recycled materials")</li>
                          <li>• The opening could be more attention-grabbing for social media formats</li>
                        </ul>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>

              <TabsContent value="ai-assistant" className="space-y-4">
                <Card className="h-[600px]">
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Bot className="w-5 h-5" />
                      AI Campaign Assistant
                      <Sparkles className="w-4 h-4 text-yellow-500" />
                    </CardTitle>
                    <CardDescription>
                      Get AI-powered assistance for script generation, creative ideas, workflow optimization, and more
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="p-0 h-[calc(100%-120px)]">
                    <CampaignChatInterface
                      campaignId={params.id as string}
                      campaignData={campaign}
                    />
                  </CardContent>
                </Card>
              </TabsContent>
            </Tabs>
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Campaign Info */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Campaign Details</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <p className="text-sm text-gray-500">Budget</p>
                  <p className="font-medium">${campaign.budget.toLocaleString()}</p>
                </div>
                <div>
                  <p className="text-sm text-gray-500">Launch Date</p>
                  <p className="font-medium">{new Date(campaign.timeline.launchDate).toLocaleDateString()}</p>
                </div>
                <div>
                  <p className="text-sm text-gray-500">Target Audience</p>
                  <div className="flex flex-wrap gap-1 mt-1">
                    {campaign.targetAudience.map((audience, index) => (
                      <Badge key={index} variant="outline" className="text-xs">
                        {audience}
                      </Badge>
                    ))}
                  </div>
                </div>
                <div>
                  <p className="text-sm text-gray-500">Channels</p>
                  <div className="flex flex-wrap gap-1 mt-1">
                    {campaign.channels.map((channel, index) => (
                      <Badge key={index} variant="outline" className="text-xs">
                        {channel}
                      </Badge>
                    ))}
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Team */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg flex items-center gap-2">
                  <Users className="w-4 h-4" />
                  Team Members
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {campaign.team.map((member) => (
                    <div key={member.id} className="flex items-center gap-3">
                      <div className="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center text-white text-sm font-medium">
                        {member.avatar}
                      </div>
                      <div>
                        <p className="font-medium text-sm">{member.name}</p>
                        <p className="text-xs text-gray-500">{member.role}</p>
                      </div>
                    </div>
                  ))}
                  <Button variant="outline" size="sm" className="w-full mt-3">
                    <Plus className="w-4 h-4 mr-2" />
                    Add Member
                  </Button>
                </div>
              </CardContent>
            </Card>

            {/* Activity Feed */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg flex items-center gap-2">
                  <Activity className="w-4 h-4" />
                  Recent Activity
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {activities.length > 0 ? activities.slice(0, 5).map((activity: any) => (
                    <div key={activity.id} className="flex items-start gap-3">
                      <div className="w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center text-white text-xs font-medium">
                        <Activity className="w-3 h-3" />
                      </div>
                      <div className="flex-1">
                        <p className="text-sm font-medium">{activity.title}</p>
                        <p className="text-xs text-gray-500">{activity.description}</p>
                        <p className="text-xs text-gray-400 mt-1">
                          {new Date(activity.created_at).toLocaleString()}
                        </p>
                      </div>
                    </div>
                  )) : (
                    <div className="text-center py-4">
                      <Activity className="w-8 h-8 mx-auto text-gray-400 mb-2" />
                      <p className="text-sm text-gray-500">No recent activity</p>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>

            {/* Comments */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg flex items-center gap-2">
                  <MessageSquare className="w-4 h-4" />
                  Recent Comments
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {comments.slice(0, 3).map((comment) => (
                    <div key={comment.id} className="space-y-2">
                      <div className="flex items-start gap-3">
                        <div className="w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center text-white text-xs font-medium">
                          {comment.author.split(' ').map(n => n[0]).join('')}
                        </div>
                        <div className="flex-1">
                          <div className="flex items-center gap-2 mb-1">
                            <p className="font-medium text-sm">{comment.author}</p>
                            <p className="text-xs text-gray-500">{comment.timestamp}</p>
                          </div>
                          <p className="text-sm text-gray-700">{comment.content}</p>
                        </div>
                      </div>
                      {comment.replies.map((reply) => (
                        <div key={reply.id} className="ml-9 flex items-start gap-3">
                          <div className="w-5 h-5 bg-green-500 rounded-full flex items-center justify-center text-white text-xs font-medium">
                            {reply.author.split(' ').map(n => n[0]).join('')}
                          </div>
                          <div className="flex-1">
                            <div className="flex items-center gap-2 mb-1">
                              <p className="font-medium text-xs">{reply.author}</p>
                              <p className="text-xs text-gray-500">{reply.timestamp}</p>
                            </div>
                            <p className="text-xs text-gray-700">{reply.content}</p>
                          </div>
                        </div>
                      ))}
                    </div>
                  ))}

                  <div className="border-t pt-4">
                    <Textarea
                      placeholder="Add a comment..."
                      className="mb-2"
                      rows={3}
                      value={newComment}
                      onChange={(e) => setNewComment(e.target.value)}
                    />
                    <Button size="sm" onClick={addComment}>
                      <Send className="w-4 h-4 mr-2" />
                      Comment
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  )
}