'use client'

import { Suspense } from 'react'

import React, { useState, useEffect } from 'react'
import Link from "next/link"
import Image from "next/image"
import { usePathname, useSearchParams } from "next/navigation"
import { useUser, useClerk } from "@clerk/nextjs"
import { 
  Library,
  Search,
  TrendingUp,
  BarChart3,
  User,
  Settings,
  HelpCircle,
  LogOut,
  CreditCard,
  Home,
  ChevronRight,
  Plus,
  Tag,
  ChevronDown,
  ExternalLink,
  VideoIcon,
  Palette,
  Menu
} from "lucide-react"

import { cn } from "@/lib/utils"
import { Button } from "@/components/ui/button"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { 
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarGroup,
  SidebarGroupContent,
  SidebarGroupLabel,
  SidebarHeader,
  SidebarInset,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarProvider,
  SidebarRail,
  SidebarTrigger,
  SidebarMenuSub,
  SidebarMenuSubItem,
  SidebarMenuSubButton,
  useSidebar,
} from "@/components/ui/sidebar"
import { Separator } from "@/components/ui/separator"
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb"

// Interface for playlist data
interface PlaylistTag {
  tag: string
  count: number
  latest_ad: string
}

// Mobile menu button component
function MobileMenuButton({ className }: { className?: string }) {
  const { toggleSidebar } = useSidebar()
  
  return (
    <Button
      variant="ghost"
      size="icon"
      onClick={toggleSidebar}
      className={cn("w-6 h-6 p-0", className)}
      aria-label="Toggle menu"
    >
      <Menu className="h-4 w-4" />
    </Button>
  )
}

// Main navigation items
const navItems = [
  {
    title: "Pre-Launch Testing",
    href: "/ad-library",
    icon: Library,
    description: "Browse and analyze ad creatives"
  }
]

// Pro features with sub-items
const proFeatures = [
  {
    title: "Pre-Launch Testing",
    href: "/pre-launch-testing",
    icon: VideoIcon,
    description: "Test your ads before launch"
  },
  {
    title: "Studio",
    icon: Palette,
    description: "Creative studio tools",
    subItems: [
      { title: "Plan", href: "/studio/plan" },
      { title: "Create", href: "/studio/create" },
      { title: "Optimize", href: "/studio/optimise" },
      { title: "Execute", href: "/studio/execute" },
      { title: "Analyse", href: "/studio/analyse" },
      { title: "Brand", href: "/studio/brand" }
    ]
  }
]

function DashboardSidebar() {
  const { user } = useUser()
  const { signOut } = useClerk()
  const pathname = usePathname()
  const [playlists, setPlaylists] = useState<PlaylistTag[]>([])
  const [showPlaylistsSubmenu, setShowPlaylistsSubmenu] = useState(false)
  const [showStudioSubmenu, setShowStudioSubmenu] = useState(false)
  
  // Fetch playlists data
  useEffect(() => {
    async function fetchPlaylists() {
      try {
        const response = await fetch('/api/admin/playlists/tags')
        if (response.ok) {
          const data = await response.json()
          setPlaylists(data.tags || [])
        }
      } catch (error) {
        console.error('Failed to fetch playlists:', error)
      }
    }
    
    fetchPlaylists()
  }, [])
  
  // Get top 6 playlists for dropdown
  const topPlaylists = playlists.slice(0, 6)
  const hasMorePlaylists = playlists.length > 6

  return (
    <Sidebar variant="inset" collapsible="icon">
      <SidebarHeader className="group-data-[collapsible=icon]:p-0">
        <div className="flex items-center justify-start group-data-[collapsible=icon]:justify-center p-2 group-data-[collapsible=icon]:p-0">
          <Link href="/ad-library" className="flex items-center gap-2 hover:opacity-80 transition-opacity group-data-[collapsible=icon]:w-12 group-data-[collapsible=icon]:h-12 group-data-[collapsible=icon]:justify-center">
            <Image alt="Breakdown.ad Logo" src="/logo.png" width={20} height={20} className="rounded-sm w-5 h-5" />
            <span className="text-xl font-gegola tracking-wider bg-gradient-to-r from-blue-500 to-purple-500 bg-clip-text text-transparent group-data-[collapsible=icon]:hidden">
              breakdown
            </span>
          </Link>
        </div>
      </SidebarHeader>
      
      <SidebarContent>
        {/* Main Navigation */}
        <SidebarGroup>
          <SidebarGroupContent>
            <SidebarMenu>
              {/* Ad Library */}
              {navItems.map((item) => {
                const isActive = pathname === item.href && !pathname.includes('playlists')
                return (
                  <SidebarMenuItem key={item.href}>
                    <SidebarMenuButton
                      asChild
                      isActive={isActive}
                      tooltip={item.description}
                      className="group-data-[collapsible=icon]:w-12 group-data-[collapsible=icon]:h-12 group-data-[collapsible=icon]:p-0 group-data-[collapsible=icon]:justify-center"
                    >
                      <Link href={item.href}>
                        <item.icon className="w-5 h-5" />
                        <span className="group-data-[collapsible=icon]:hidden text-sm">{item.title}</span>
                      </Link>
                    </SidebarMenuButton>
                  </SidebarMenuItem>
                )
              })}
              
              {/* Playlists Submenu */}
              <SidebarMenuItem>
                <SidebarMenuButton
                  tooltip="Browse playlists"
                  onClick={() => setShowPlaylistsSubmenu(!showPlaylistsSubmenu)}
                  className="group/playlists group-data-[collapsible=icon]:w-12 group-data-[collapsible=icon]:h-12 group-data-[collapsible=icon]:p-0 group-data-[collapsible=icon]:justify-center"
                >
                  <Tag className="w-5 h-5" />
                  <span className="group-data-[collapsible=icon]:hidden text-sm">Ad Reels</span>
                  <ChevronDown className={`ml-auto h-4 w-4 transition-transform group-data-[collapsible=icon]:hidden ${showPlaylistsSubmenu ? 'rotate-180' : ''}`} />
                </SidebarMenuButton>
                {showPlaylistsSubmenu && (
                  <SidebarMenuSub>
                    {topPlaylists.map((playlist) => (
                      <SidebarMenuSubItem key={playlist.tag}>
                        <SidebarMenuSubButton asChild>
                          <Link href={`/ad-library?playlist_tags=${encodeURIComponent(playlist.tag)}`}>
                            <span className="truncate">{playlist.tag}</span>
                            <span className="ml-auto text-xs text-muted-foreground">{playlist.count}</span>
                          </Link>
                        </SidebarMenuSubButton>
                      </SidebarMenuSubItem>
                    ))}
                    {hasMorePlaylists && (
                      <SidebarMenuSubItem>
                        <SidebarMenuSubButton asChild>
                          <Link href="/ad-library/playlists">
                            <span>See all playlists</span>
                            <ExternalLink className="ml-auto h-3 w-3" />
                          </Link>
                        </SidebarMenuSubButton>
                      </SidebarMenuSubItem>
                    )}
                    {playlists.length === 0 && (
                      <SidebarMenuSubItem>
                        <SidebarMenuSubButton disabled>
                          <span className="text-muted-foreground text-xs">No playlists yet</span>
                        </SidebarMenuSubButton>
                      </SidebarMenuSubItem>
                    )}
                  </SidebarMenuSub>
                )}
              </SidebarMenuItem>
            </SidebarMenu>
          </SidebarGroupContent>
        </SidebarGroup>

        {/* Quick Actions */}
        <SidebarGroup>
          <SidebarGroupLabel>Coming Soon</SidebarGroupLabel>
          <SidebarGroupContent>
            <SidebarMenu>
              {proFeatures.map((feature) => (
                <SidebarMenuItem key={feature.title}>
                  {feature.subItems ? (
                    <>
                      <SidebarMenuButton
                        tooltip={feature.description}
                        onClick={() => setShowStudioSubmenu(!showStudioSubmenu)}
                        className="relative group/studio group-data-[collapsible=icon]:w-12 group-data-[collapsible=icon]:h-12 group-data-[collapsible=icon]:p-0 group-data-[collapsible=icon]:justify-center"
                      >
                        <feature.icon className="w-5 h-5" />
                        <span className="group-data-[collapsible=icon]:hidden text-sm">{feature.title}</span>
                        <ChevronDown className={`ml-auto h-4 w-4 transition-transform group-data-[collapsible=icon]:hidden ${showStudioSubmenu ? 'rotate-180' : ''}`} />
                      </SidebarMenuButton>
                      {showStudioSubmenu && (
                        <SidebarMenuSub>
                          {feature.subItems.map((subItem) => (
                            <SidebarMenuSubItem key={subItem.title}>
                              <SidebarMenuSubButton asChild>
                                <Link href={subItem.href}>
                                  <span>{subItem.title}</span>
                                </Link>
                              </SidebarMenuSubButton>
                            </SidebarMenuSubItem>
                          ))}
                        </SidebarMenuSub>
                      )}
                    </>
                  ) : (
                    <SidebarMenuButton
                      asChild
                      tooltip={feature.description}
                      className="relative group-data-[collapsible=icon]:w-12 group-data-[collapsible=icon]:h-12 group-data-[collapsible=icon]:p-0 group-data-[collapsible=icon]:justify-center"
                    >
                      <Link href={feature.href}>
                        <feature.icon className="w-5 h-5" />
                        <span className="group-data-[collapsible=icon]:hidden text-sm">{feature.title}</span>
                      </Link>
                    </SidebarMenuButton>
                  )}
                </SidebarMenuItem>
              ))}
            </SidebarMenu>
          </SidebarGroupContent>
        </SidebarGroup>
      </SidebarContent>
      
      <SidebarFooter>
        <SidebarMenu>
          <SidebarMenuItem>
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <SidebarMenuButton
                  size="lg"
                  className="data-[state=open]:bg-sidebar-accent data-[state=open]:text-sidebar-accent-foreground"
                >
                  <Avatar className="h-8 w-8 rounded-lg">
                    <AvatarImage src={user?.imageUrl} alt={user?.fullName || "User Avatar"} />
                    <AvatarFallback className="rounded-lg">
                      {user?.fullName?.charAt(0)?.toUpperCase() ?? "U"}
                    </AvatarFallback>
                  </Avatar>
                  <div className="grid flex-1 text-left text-sm leading-tight">
                    <span className="truncate font-semibold">
                      {user?.fullName ?? "User Name"}
                    </span>
                    <span className="truncate text-xs">
                      {user?.primaryEmailAddress?.emailAddress ?? "<EMAIL>"}
                    </span>
                  </div>
                  <ChevronRight className="ml-auto size-4" />
                </SidebarMenuButton>
              </DropdownMenuTrigger>
              <DropdownMenuContent
                className="w-[--radix-dropdown-menu-trigger-width] min-w-56 rounded-lg"
                side="bottom"
                align="end"
                sideOffset={4}
              >
                <DropdownMenuItem asChild>
                  <Link href="/studio">
                    <Home className="mr-2 h-4 w-4" />
                    <span>Studio</span>
                  </Link>
                </DropdownMenuItem>
                <DropdownMenuItem asChild>
                  <Link href="/billing">
                    <CreditCard className="mr-2 h-4 w-4" />
                    <span>Billing</span>
                  </Link>
                </DropdownMenuItem>
                <DropdownMenuItem asChild>
                  <Link href="/settings">
                    <Settings className="mr-2 h-4 w-4" />
                    <span>Settings</span>
                  </Link>
                </DropdownMenuItem>
                <DropdownMenuItem asChild>
                  <Link href="/help">
                    <HelpCircle className="mr-2 h-4 w-4" />
                    <span>Help</span>
                  </Link>
                </DropdownMenuItem>
                <DropdownMenuSeparator />
                <DropdownMenuItem onClick={() => signOut()}>
                  <LogOut className="mr-2 h-4 w-4" />
                  <span>Log out</span>
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </SidebarMenuItem>
        </SidebarMenu>
      </SidebarFooter>
      <SidebarRail />
    </Sidebar>
  )
}

function DashboardHeader() {
  const pathname = usePathname()
  const searchParams = useSearchParams()
  const page = searchParams.get('page')

  // Determine page type
  const isAdLibrary = pathname === '/ad-library'
  const isFirstPageOfAdLibrary = isAdLibrary && (!page || page === '1')
  const isAdDetail = pathname.startsWith('/ad/') && pathname !== '/ad-library'

  return (
    <header className="flex h-16 shrink-0 items-center gap-2 transition-[width,height] ease-linear group-has-[[data-collapsible=icon]]/sidebar-wrapper:h-12">
      <div className="flex items-center justify-between w-full px-4">
        {/* Left side - Logo */}
        <div className="flex items-center gap-2">
          <Link href="/ad-library" className="flex items-center gap-2 hover:opacity-80 transition-opacity md:hidden">
            <Image alt="Breakdown.ad Logo" src="/logo.png" width={40} height={40} className="w-6 h-6 rounded-sm" />
            <span className="text-lg font-gegola tracking-wider bg-gradient-to-r from-blue-500 to-purple-500 bg-clip-text text-transparent">
              breakdown
            </span>
          </Link>

          {/* Desktop breadcrumbs */}
          <div className="hidden md:flex items-center gap-2">
            <SidebarTrigger className="-ml-1 w-6 h-6" />
            <Separator orientation="vertical" className="mr-2 h-4" />
            <Breadcrumb>
              <BreadcrumbList>
                <BreadcrumbItem>
                  {isAdLibrary ? (
                    <BreadcrumbPage>Ad Library</BreadcrumbPage>
                  ) : (
                    <BreadcrumbLink asChild>
                      <Link href="/ad-library">Ad Library</Link>
                    </BreadcrumbLink>
                  )}
                </BreadcrumbItem>
                {isAdDetail && (
                  <>
                    <BreadcrumbSeparator />
                    <BreadcrumbItem>
                      <BreadcrumbPage>Ad Analysis</BreadcrumbPage>
                    </BreadcrumbItem>
                  </>
                )}
              </BreadcrumbList>
            </Breadcrumb>
          </div>
        </div>

        {/* Right side - Action buttons 
        <div className="flex items-center gap-1">
          {isFirstPageOfAdLibrary ? (
            <>
              <Button
                size="sm"
                className="bg-black hover:bg-gray-500 text-white px-2 py-1 rounded-md h-8"
                onClick={() => {
                  // We'll implement this overlay functionality
                  const event = new CustomEvent('openSubmitAdModal')
                  window.dispatchEvent(event)
                }}
              >
 
                <span className="hidden sm:inline">Submit Ad</span>
                <span className="sm:hidden">Submit Ad</span>
              </Button>
              <MobileMenuButton className="md:hidden" />
            </>
          ) : isAdDetail ? (
            <>
              <Button variant="outline" asChild>
                <Link href="/ad-library">
                  <Library className="h-4 w-4 mr-1" />
                  <span className="hidden sm:inline">Ad Library</span>
                  <span className="sm:hidden">Library</span>
                </Link>
              </Button>
              <MobileMenuButton className="md:hidden" />
            </>
          ) : (
            <MobileMenuButton className="md:hidden" />
          )}
        </div>
        */}
      </div>
    </header>
  )
}

export default function DashboardLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <SidebarProvider defaultOpen={false}>
      <DashboardSidebar />
      <SidebarInset className="overflow-hidden">
        <Suspense>
          <DashboardHeader />
        </Suspense>
        <div className="flex flex-1 flex-col gap-4 p-2 md:p-4 pt-0 min-w-0 overflow-hidden">
          {children}
        </div>
      </SidebarInset>
    </SidebarProvider>
  )
}
