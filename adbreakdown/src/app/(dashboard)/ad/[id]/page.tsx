import { createServerSupabaseClient } from '@/lib/supabase'
import { Metadata } from 'next'
import SharedAnalysisPage from '@/components/analysis/SharedAnalysisPage'

type Props = {
  params: Promise<{ id: string }>
}

export async function generateMetadata({ params }: Props): Promise<Metadata> {
  try {
    const { id } = await params
    const supabase = createServerSupabaseClient()
    
    // Check if the id is a UUID (contains hyphens in UUID format) or a slug
    const isUUID = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(id)
    
    const { data: analysis, error } = await supabase
      .from('ad_analyses')
      .select('title, inferred_brand, is_public')
      .eq(isUUID ? 'id' : 'slug', id)
      .single()

    if (error || !analysis) {
      console.log(`Metadata: Analysis not found for ${isUUID ? 'ID' : 'slug'}: ${id}`, error)
      return {
        title: 'Analysis Not Found',
        description: 'The requested ad analysis could not be found.',
        robots: {
          index: false,
          follow: false,
        },
      }
    }

    // Allow metadata generation for both public and private analyses
    // The component will handle access control
    const pageTitle = `${analysis.title || 'Ad Analysis'} - ${analysis.inferred_brand || 'Brand'} | breakdown.ad`
    const description = `AI-powered breakdown of the ${analysis.inferred_brand || 'brand'} ad "${analysis.title || 'advertisement'}". Get marketing insights, sentiment analysis, and more.`

    return {
      title: pageTitle,
      description: description,
      robots: {
        index: analysis.is_public ? true : false,
        follow: analysis.is_public ? true : false,
      },
      openGraph: {
        title: pageTitle,
        description: description,
        type: 'article',
        url: `https://breakdown.ad/ad/${id}`,
      },
      twitter: {
        card: 'summary_large_image',
        title: pageTitle,
        description: description,
      },
    }
  } catch (error) {
    console.error('Error generating metadata:', error)
    return {
      title: 'Ad Analysis | breakdown.ad',
      description: 'AI-powered ad analysis platform',
      robots: {
        index: false,
        follow: false,
      },
    }
  }
}

export default async function AdAnalysisPage({ params }: Props) {
  const { id } = await params
  return (
    <div className="flex flex-1 flex-col">
      <SharedAnalysisPage 
        analysisId={id}
        isFeaturedMode={false}
      />
    </div>
  )
}
