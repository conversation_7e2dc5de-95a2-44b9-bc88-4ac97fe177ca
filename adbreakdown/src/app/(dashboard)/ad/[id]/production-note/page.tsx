import { createServerSupabaseClient } from '@/lib/supabase'
import { Metadata } from 'next'
import { notFound } from 'next/navigation'
import ProductionNotePage from '@/components/analysis/ProductionNotePage'

type Props = {
  params: Promise<{ id: string }>
}

export async function generateMetadata({ params }: Props): Promise<Metadata> {
  try {
    const { id } = await params
    const supabase = createServerSupabaseClient()
    
    // Check if the id is a UUID (contains hyphens in UUID format) or a slug
    const isUUID = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(id)
    
    const { data: analysis, error } = await supabase
      .from('ad_analyses')
      .select('title, inferred_brand, is_public')
      .eq(isUUID ? 'id' : 'slug', id)
      .single()

    if (error || !analysis) {
      return {
        title: 'Production Note Not Found',
        description: 'The requested production note could not be found.',
        robots: {
          index: false,
          follow: false,
        },
      }
    }

    const pageTitle = `${analysis.title || 'Ad Analysis'} - Production Note | breakdown.ad`
    const description = `Detailed production notes for the ${analysis.inferred_brand || 'brand'} ad "${analysis.title || 'advertisement'}".`

    return {
      title: pageTitle,
      description: description,
      robots: {
        index: analysis.is_public ? true : false,
        follow: analysis.is_public ? true : false,
      },
      openGraph: {
        title: pageTitle,
        description: description,
        type: 'article',
        url: `https://breakdown.ad/ad/${id}/production-note`,
      },
      twitter: {
        card: 'summary_large_image',
        title: pageTitle,
        description: description,
      },
    }
  } catch (error) {
    console.error('Error generating metadata:', error)
    return {
      title: 'Production Note | breakdown.ad',
      description: 'Detailed production notes and analysis',
      robots: {
        index: false,
        follow: false,
      },
    }
  }
}

export default async function ProductionNotePageRoute({ params }: Props) {
  const { id } = await params
  
  return (
    <div className="min-h-screen bg-gray-50">
      <ProductionNotePage analysisId={id} />
    </div>
  )
}