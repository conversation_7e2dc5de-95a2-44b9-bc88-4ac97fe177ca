'use client'

import { useEffect, useState } from 'react'
import { useAuth } from '@/hooks/useAuth'
import { useCredits } from '@/hooks/useCredits'
import PrivateAnalysisCard from '@/components/optimise/PrivateAnalysisCard'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { TestTube2 } from 'lucide-react'

export default function PreLaunchTestingPage() {
  const { isAuthenticated, loading: authLoading } = useAuth()
  const { profile, loading: creditsLoading } = useCredits()
  const [creditsRemaining, setCreditsRemaining] = useState(0)

  useEffect(() => {
    if (profile && !creditsLoading) {
      setCreditsRemaining(profile.credits_remaining)
    }
  }, [profile, creditsLoading])

  // Show loading while auth or credits are loading
  if (authLoading || creditsLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  // Redirect to sign-in if not authenticated
  if (!isAuthenticated) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <Card className="max-w-md">
          <CardHeader>
            <CardTitle>Authentication Required</CardTitle>
            <CardDescription>
              Please sign in to access the pre-launch testing page.
            </CardDescription>
          </CardHeader>
        </Card>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="container mx-auto px-4 py-8 max-w-4xl">
        {/* Page Header */}
        <div className="mb-8">
          <div className="flex items-center gap-3 mb-4">
            <div className="p-2 bg-blue-100 rounded-lg">
              <TestTube2 className="h-6 w-6 text-blue-600" />
            </div>
            <div>
              <h1 className="text-2xl font-bold text-gray-900">Pre-Launch Testing</h1>
              <p className="text-gray-600">Private video analysis with optimization insights</p>
            </div>
          </div>
          
        </div>

        {/* Main Content */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Private Analysis Card */}
          <div className="lg:col-span-1">
            <PrivateAnalysisCard creditsRemaining={creditsRemaining} />
          </div>

          {/* Analysis Information */}
          <div className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Supported Features</CardTitle>
                <CardDescription>
                  Available video input modes
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-3">
                  <div className="flex items-start gap-3">
                    <div className="w-2 h-2 bg-green-500 rounded-full mt-2"></div>
                    <div>
                      <p className="font-medium text-sm">Public YouTube Videos</p>
                      <p className="text-xs text-gray-600">Analyze public YouTube videos</p>
                    </div>
                  </div>

                  <div className="flex items-start gap-3">
                    <div className="w-2 h-2 bg-green-500 rounded-full mt-2"></div>
                    <div>
                      <p className="font-medium text-sm">Video File Upload</p>
                      <p className="text-xs text-gray-600">Upload video files directly for analysis (up to 100MB)</p>
                    </div>
                  </div>

                  <div className="flex items-start gap-3">
                    <div className="w-2 h-2 bg-red-500 rounded-full mt-2"></div>
                    <div>
                      <p className="font-medium text-sm">Private YouTube Videos</p>
                      <p className="text-xs text-gray-600">DO NOT upload unlisted and private YouTube videos</p>
                    </div>
                  </div>

                  <div className="flex items-start gap-3">
                    <div className="w-2 h-2 bg-red-500 rounded-full mt-2"></div>
                    <div>
                      <p className="font-medium text-sm">Instagram Reels</p>
                      <p className="text-xs text-gray-600">DO NOT upload public and priavte Instagram reels</p>
                    </div>
                  </div>

                  <div className="flex items-start gap-3">
                    <div className="w-2 h-2 bg-red-500 rounded-full mt-2"></div>
                    <div>
                      <p className="font-medium text-sm">Facebook Videos</p>
                      <p className="text-xs text-gray-600">DO NOT upload public and private Facebook videos</p>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  )
}