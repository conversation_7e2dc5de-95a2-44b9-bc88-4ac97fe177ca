import { Suspense } from 'react'
import { createClient } from '@supabase/supabase-js'
import Link from 'next/link'
import Image from 'next/image'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Tag, ExternalLink, TrendingUp } from 'lucide-react'

export const revalidate = 300

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!
const supabase = createClient(supabaseUrl, supabaseServiceKey)

interface CollectionData {
  tag: string
  count: number
  latest_ad: string
  sample_ads: Array<{
    id: string
    title: string
    thumbnail_url: string
    slug: string
  }>
}

async function getCollectionsData() {
  try {
    // Fetch all analyses with collection_tags to aggregate stats
    const { data: analyses, error } = await supabase
      .from('ad_analyses')
      .select('collection_tags, title, thumbnail_url, slug, created_at, id')
      .eq('status', 'completed')
      .eq('is_public', true)
      .not('collection_tags', 'is', null)

    if (error) {
      throw new Error(error.message)
    }

    // Aggregate collection statistics with sample ads
    const collectionStats: Record<string, { 
      count: number
      latest_ad: string
      latest_date: string
      sample_ads: Array<{ id: string, title: string, thumbnail_url: string, slug: string }>
    }> = {}

    analyses?.forEach(analysis => {
      if (analysis.collection_tags && Array.isArray(analysis.collection_tags)) {
        analysis.collection_tags.forEach(tag => {
          if (!collectionStats[tag]) {
            collectionStats[tag] = {
              count: 0,
              latest_ad: analysis.title,
              latest_date: analysis.created_at,
              sample_ads: []
            }
          }
          
          collectionStats[tag].count++
          
          // Add to sample ads (max 4 per collection)
          if (collectionStats[tag].sample_ads.length < 4) {
            collectionStats[tag].sample_ads.push({
              id: analysis.id,
              title: analysis.title,
              thumbnail_url: analysis.thumbnail_url,
              slug: analysis.slug
            })
          }
          
          // Update latest ad if this one is newer
          if (new Date(analysis.created_at) > new Date(collectionStats[tag].latest_date)) {
            collectionStats[tag].latest_ad = analysis.title
            collectionStats[tag].latest_date = analysis.created_at
          }
        })
      }
    })

    // Convert to array and sort by count (most popular first)
    const collections: CollectionData[] = Object.entries(collectionStats)
      .map(([tag, stats]) => ({
        tag,
        count: stats.count,
        latest_ad: stats.latest_ad,
        sample_ads: stats.sample_ads
      }))
      .sort((a, b) => b.count - a.count)

    return {
      collections,
      totalCollections: collections.length,
      totalAds: analyses?.length || 0
    }

  } catch (error) {
    console.error('Error fetching collections data:', error)
    return {
      collections: [],
      totalCollections: 0,
      totalAds: 0
    }
  }
}

export default async function CollectionsPage() {
  const data = await getCollectionsData()

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900 mb-2 flex items-center gap-2">
          <Tag className="h-8 w-8" />
          All Collections
        </h1>
        <p className="text-gray-600">
          Browse curated collections of ads organized by themes, events, and campaigns. 
          Discover {data.totalCollections} collections across {data.totalAds} analyzed ads.
        </p>
      </div>

      {/* Collections Grid */}
      <Suspense fallback={<div>Loading collections...</div>}>
        {data.collections.length > 0 ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {data.collections.map((collection) => (
              <Card key={collection.tag} className="hover:shadow-lg transition-shadow group">
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <CardTitle className="flex items-center gap-2 text-lg">
                      <Tag className="h-5 w-5" />
                      <span className="capitalize">{collection.tag.replace(/-/g, ' ')}</span>
                    </CardTitle>
                    <Badge variant="secondary" className="flex items-center gap-1">
                      <TrendingUp className="h-3 w-3" />
                      {collection.count}
                    </Badge>
                  </div>
                  <CardDescription>
                    Latest: {collection.latest_ad.substring(0, 60)}
                    {collection.latest_ad.length > 60 && '...'}
                  </CardDescription>
                </CardHeader>
                
                <CardContent>
                  {/* Sample Ad Thumbnails */}
                  <div className="grid grid-cols-2 gap-2 mb-4">
                    {collection.sample_ads.slice(0, 4).map((ad, index) => (
                      <div key={ad.id} className="aspect-video bg-gray-100 rounded-md overflow-hidden">
                        {ad.thumbnail_url ? (
                          <Image
                            src={ad.thumbnail_url}
                            alt={ad.title}
                            width={200}
                            height={112}
                            className="w-full h-full object-cover"
                          />
                        ) : (
                          <div className="w-full h-full flex items-center justify-center bg-gray-200">
                            <Tag className="h-4 w-4 text-gray-400" />
                          </div>
                        )}
                      </div>
                    ))}
                    
                    {/* Fill remaining slots with placeholders if needed */}
                    {Array.from({ length: Math.max(0, 4 - collection.sample_ads.length) }).map((_, index) => (
                      <div key={`placeholder-${index}`} className="aspect-video bg-gray-50 rounded-md border-2 border-dashed border-gray-200 flex items-center justify-center">
                        <Tag className="h-4 w-4 text-gray-300" />
                      </div>
                    ))}
                  </div>
                  
                  {/* View Collection Button */}
                  <Link 
                    href={`/ad-library?collection_tags=${encodeURIComponent(collection.tag)}`}
                    className="w-full bg-blue-50 hover:bg-blue-100 text-blue-700 rounded-lg p-3 flex items-center justify-between transition-colors group-hover:bg-blue-100"
                  >
                    <span className="font-medium">View Collection</span>
                    <ExternalLink className="h-4 w-4" />
                  </Link>
                </CardContent>
              </Card>
            ))}
          </div>
        ) : (
          <div className="text-center py-16">
            <Tag className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">No Collections Yet</h3>
            <p className="text-gray-600 mb-6">
              Collections will appear here once ads are tagged by administrators.
            </p>
            <Link 
              href="/ad-library" 
              className="inline-flex items-center gap-2 bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md transition-colors"
            >
              Browse Ad Library
              <ExternalLink className="h-4 w-4" />
            </Link>
          </div>
        )}
      </Suspense>
    </div>
  )
}