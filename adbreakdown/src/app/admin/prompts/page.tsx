'use client'

import React, { useState, useEffect } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Textarea } from '@/components/ui/textarea'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Edit, Save, X, Eye, Info } from 'lucide-react'
import { useAuth } from '@/hooks/useAuth'
import Link from 'next/link'

// Simple syntax highlighter component
const SyntaxHighlighter = ({ content, language, preview = false }: { content: string, language: string, preview?: boolean }) => {
  const highlightContent = (text: string) => {
    if (language === 'markdown') {
      return text
        .split('\n')
        .map((line) => {
          let highlightedLine = line
          
          // Headers
          if (line.startsWith('###')) {
            highlightedLine = `<span class="text-blue-400 font-bold">${line}</span>`
          } else if (line.startsWith('##')) {
            highlightedLine = `<span class="text-blue-300 font-bold">${line}</span>`
          } else if (line.startsWith('#')) {
            highlightedLine = `<span class="text-blue-200 font-bold">${line}</span>`
          }
          // Bold text
          else if (line.includes('**')) {
            highlightedLine = line.replace(/\*\*(.*?)\*\*/g, '<span class="text-yellow-300 font-bold">**$1**</span>')
          }
          // Variables/placeholders
          else if (line.includes('{{') || line.includes('}}')) {
            highlightedLine = line.replace(/\{\{(.*?)\}\}/g, '<span class="text-green-400">{{$1}}</span>')
          }
          // JSON-like structures
          else if (line.includes(':') && (line.includes('"') || line.includes("'") || line.includes('{'))) {
            highlightedLine = line
              .replace(/"(.*?)":/g, '<span class="text-purple-300">"$1"</span>:')
              .replace(/: "(.*?)"/g, ': <span class="text-green-300">"$1"</span>')
              .replace(/\{|\}/g, '<span class="text-gray-400">$&</span>')
          }
          // List items
          else if (line.match(/^\s*[-*+]\s/)) {
            highlightedLine = line.replace(/^(\s*[-*+]\s)/, '<span class="text-orange-400">$1</span>')
          }
          
          return `<div class="leading-relaxed">${highlightedLine}</div>`
        })
        .join('')
    }
    return text
  }

  return (
    <div className={`${preview ? 'max-h-32' : 'h-full'} overflow-auto`}>
      <pre 
        className="whitespace-pre-wrap text-sm text-gray-100"
        dangerouslySetInnerHTML={{ __html: highlightContent(content) }}
      />
    </div>
  )
}

interface Prompt {
  id: string
  name: string
  content: string
  content_preview?: string
  content_lines?: number
  content_stats?: {
    lines: number
    characters: number
    words: number
    sections: number
  }
  description: string
  version: number
  is_active: boolean
  last_updated?: string
  created?: string
}

export default function PromptsAdminPage() {
  const { isAuthenticated, loading } = useAuth()
  const [prompts, setPrompts] = useState<Prompt[]>([])
  const [selectedPrompt, setSelectedPrompt] = useState<Prompt | null>(null)
  const [editingPrompt, setEditingPrompt] = useState<Prompt | null>(null)
  const [loading1, setLoading1] = useState(true)
  const [saving, setSaving] = useState(false)
  const [error, setError] = useState('')
  const [success, setSuccess] = useState('')
  const [accessDenied, setAccessDenied] = useState(false)

  useEffect(() => {
    // Block access if not on admin subdomain
    if (typeof window !== 'undefined' && !window.location.hostname.startsWith('admin.')) {
      window.location.href = '/'
      return
    }

    if (isAuthenticated) {
      fetchPrompts()
    }
  }, [isAuthenticated])

  const fetchPrompts = async () => {
    try {
      const response = await fetch('/api/admin/prompts')
      if (response.status === 403) {
        setAccessDenied(true)
        setLoading1(false)
        return
      }
      if (!response.ok) throw new Error('Failed to fetch prompts')
      const data = await response.json()
      setPrompts(data.prompts)
    } catch (err) {
      setError('Failed to load prompts')
    } finally {
      setLoading1(false)
    }
  }

  const fetchFullPrompt = async (name: string) => {
    try {
      const response = await fetch(`/api/admin/prompts/${name}`)
      if (!response.ok) throw new Error('Failed to fetch prompt')
      const data = await response.json()
      setSelectedPrompt(data.prompt)
    } catch (err) {
      setError('Failed to load prompt details')
    }
  }

  const savePrompt = async () => {
    if (!editingPrompt) return
    
    setSaving(true)
    try {
      const response = await fetch('/api/admin/prompts', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          name: editingPrompt.name,
          content: editingPrompt.content,
          description: editingPrompt.description
        })
      })

      if (!response.ok) throw new Error('Failed to save prompt')
      
      setSuccess('Prompt saved successfully!')
      setEditingPrompt(null)
      fetchPrompts()
      if (selectedPrompt?.name === editingPrompt.name) {
        fetchFullPrompt(editingPrompt.name)
      }
    } catch (err) {
      setError('Failed to save prompt')
    } finally {
      setSaving(false)
    }
  }

  if (loading) {
    return <div className="p-8">Loading...</div>
  }

  if (!isAuthenticated) {
    return (
      <div className="p-8">
        <Card>
          <CardHeader>
            <CardTitle>Access Denied</CardTitle>
            <CardDescription>Please sign in to access the admin panel.</CardDescription>
          </CardHeader>
          <CardContent>
            <Link href="/sign-in">
              <Button>Sign In</Button>
            </Link>
          </CardContent>
        </Card>
      </div>
    )
  }
  
  if (accessDenied) {
    return (
      <div className="p-8">
        <Card>
          <CardHeader>
            <CardTitle>Admin Access Required</CardTitle>
            <CardDescription>
              {process.env.NODE_ENV === 'development' 
                ? 'This should not happen in development. Check your authentication.' 
                : 'You need administrator privileges to access this page.'}
            </CardDescription>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-gray-600 mb-4">
              Contact your system administrator for access to prompt management.
            </p>
            <Link href="/studio">
              <Button>Return to Dashboard</Button>
            </Link>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className="p-6 max-w-7xl mx-auto">
      <div className="mb-6">
        <h1 className="text-2xl font-bold mb-2">Prompt Management</h1>
        <p className="text-gray-600">
          Manage AI prompts without deployments
          {process.env.NODE_ENV === 'development' && (
            <span className="ml-2 px-2 py-1 bg-yellow-100 text-yellow-800 text-xs rounded-full">
              DEV MODE - Full Access
            </span>
          )}
        </p>
      </div>

      {error && (
        <div className="mb-4 p-4 bg-red-50 border border-red-200 rounded-md text-red-700">
          {error}
        </div>
      )}

      {success && (
        <div className="mb-4 p-4 bg-green-50 border border-green-200 rounded-md text-green-700">
          {success}
        </div>
      )}

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Prompts List - 1/3 width */}
        <div className="lg:col-span-1">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Info className="w-5 h-5" />
                Available Prompts
              </CardTitle>
            </CardHeader>
            <CardContent>
              {loading1 ? (
                <p>Loading prompts...</p>
              ) : (
                <div className="space-y-3">
                  {prompts.map((prompt) => (
                    <div
                      key={prompt.id}
                      className="p-4 border rounded-lg hover:bg-gray-50 cursor-pointer"
                      onClick={() => fetchFullPrompt(prompt.name)}
                    >
                      <div className="flex items-center justify-between mb-2">
                        <h3 className="font-medium">{prompt.name}</h3>
                        <div className="flex gap-2">
                          <Badge variant={prompt.is_active ? "default" : "secondary"}>
                            v{prompt.version}
                          </Badge>
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={(e) => {
                              e.stopPropagation()
                              fetchFullPrompt(prompt.name)
                              setEditingPrompt({ ...prompt })
                            }}
                          >
                            <Edit className="w-4 h-4" />
                          </Button>
                        </div>
                      </div>
                      <p className="text-sm text-gray-600 mb-2">{prompt.description}</p>
                      <div className="text-xs text-gray-500">
                        {prompt.content_lines} lines • Updated {prompt.last_updated}
                      </div>
                      <p className="text-sm text-gray-700 mt-2 line-clamp-2">
                        {prompt.content_preview}
                      </p>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </div>

        {/* Prompt Details/Editor - 2/3 width */}
        <div className="lg:col-span-2">
          {editingPrompt ? (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center justify-between">
                  <span>Edit Prompt: {editingPrompt.name}</span>
                  <div className="flex gap-2">
                    <Button
                      onClick={savePrompt}
                      disabled={saving}
                      size="sm"
                    >
                      <Save className="w-4 h-4 mr-2" />
                      {saving ? 'Saving...' : 'Save'}
                    </Button>
                    <Button
                      onClick={() => setEditingPrompt(null)}
                      variant="outline"
                      size="sm"
                    >
                      <X className="w-4 h-4" />
                    </Button>
                  </div>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <Label htmlFor="description">Description</Label>
                  <Input
                    id="description"
                    value={editingPrompt.description || ''}
                    onChange={(e) => setEditingPrompt({
                      ...editingPrompt,
                      description: e.target.value
                    })}
                    placeholder="Prompt description"
                  />
                </div>
                <div>
                  <Label htmlFor="content">Content</Label>
                  <Textarea
                    id="content"
                    value={editingPrompt.content}
                    onChange={(e) => setEditingPrompt({
                      ...editingPrompt,
                      content: e.target.value
                    })}
                    className="font-mono font-base text-sm bg-gray-900 text-gray-100 border-gray-700 h-[calc(100vh-250px)] resize-none"
                    placeholder="Prompt content..."
                  />
                </div>
                <div className="text-sm text-gray-500">
                  {editingPrompt.content.split('\n').length} lines • {editingPrompt.content.length} characters
                </div>
              </CardContent>
            </Card>
          ) : selectedPrompt ? (
            <div className="h-full">
              {/* Minimal header */}
              <div className="flex items-center justify-between mb-3 p-3 bg-gray-50 rounded-md">
                <div className="flex items-center gap-4">
                  <h3 className="font-medium">{selectedPrompt.name}</h3>
                  <Badge variant={selectedPrompt.is_active ? "default" : "secondary"}>
                    v{selectedPrompt.version}
                  </Badge>
                  {selectedPrompt.content_stats && (
                    <span className="text-sm text-gray-600">
                      {selectedPrompt.content_stats.lines} lines • {selectedPrompt.content_stats.characters} chars
                    </span>
                  )}
                </div>
                <Button
                  onClick={() => setEditingPrompt({ ...selectedPrompt })}
                  size="sm"
                >
                  <Edit className="w-4 h-4 mr-2" />
                  Edit
                </Button>
              </div>
              
              {/* Full-height code display */}
              <div className="bg-gray-900 rounded-md p-4 h-[calc(100vh-200px)] overflow-auto">
                <SyntaxHighlighter 
                  content={selectedPrompt.content} 
                  language="markdown" 
                />
              </div>
            </div>
          ) : (
            <Card>
              <CardContent className="flex items-center justify-center h-64">
                <div className="text-center text-gray-500">
                  <Eye className="w-12 h-12 mx-auto mb-4 opacity-50" />
                  <p>Select a prompt to view or edit</p>
                </div>
              </CardContent>
            </Card>
          )}
        </div>
      </div>
    </div>
  )
}