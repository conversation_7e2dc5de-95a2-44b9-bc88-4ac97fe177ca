'use client'

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { But<PERSON> } from '@/components/ui/button'
import { Calendar, Clock, CalendarDays, Target, BarChart3, Bell, Users, Zap } from 'lucide-react'

export default function PlanPage() {
  return (
    <div className="flex flex-1 flex-col gap-6 p-6">
      {/* Header */}
      <div className="flex items-center gap-4 mb-4">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Plan</h1>
          <p className="text-gray-600">Schedule and plan your ad campaigns</p>
        </div>
      </div>

      {/* Coming Soon Card */}
      <Card className="max-w-4xl bg-gradient-to-br from-violet-50 to-purple-50 border-violet-200/60 shadow-lg">
        <CardHeader className="text-center pb-4">
          <div className="flex justify-center mb-4">
            <div className="p-4 bg-violet-100 rounded-full">
              <Calendar className="h-12 w-12 text-violet-600" />
            </div>
          </div>
          <CardTitle className="text-2xl text-violet-900">Campaign Calendar</CardTitle>
          <CardDescription className="text-lg text-violet-700">
            Advanced campaign planning and scheduling tools
          </CardDescription>
          <div className="flex justify-center mt-4">
            <div className="px-4 py-2 bg-violet-100 text-violet-800 rounded-full text-sm font-semibold">
              COMING SOON
            </div>
          </div>
        </CardHeader>
        
        <CardContent className="pt-4">
          {/* Feature Preview */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
            <div className="flex items-start gap-3 p-4 bg-white/60 rounded-lg">
              <div className="p-2 bg-violet-100 rounded-lg">
                <CalendarDays className="h-5 w-5 text-violet-600" />
              </div>
              <div>
                <h3 className="font-semibold text-violet-900">Campaign Scheduling</h3>
                <p className="text-sm text-violet-700">Plan and schedule your ad campaigns with drag-and-drop calendar interface</p>
              </div>
            </div>
            
            <div className="flex items-start gap-3 p-4 bg-white/60 rounded-lg">
              <div className="p-2 bg-violet-100 rounded-lg">
                <BarChart3 className="h-5 w-5 text-violet-600" />
              </div>
              <div>
                <h3 className="font-semibold text-violet-900">Performance Timeline</h3>
                <p className="text-sm text-violet-700">Track campaign performance over time with integrated analytics</p>
              </div>
            </div>
            
            <div className="flex items-start gap-3 p-4 bg-white/60 rounded-lg">
              <div className="p-2 bg-violet-100 rounded-lg">
                <Target className="h-5 w-5 text-violet-600" />
              </div>
              <div>
                <h3 className="font-semibold text-violet-900">Campaign Templates</h3>
                <p className="text-sm text-violet-700">Pre-built templates for common campaign types and industries</p>
              </div>
            </div>
            
            <div className="flex items-start gap-3 p-4 bg-white/60 rounded-lg">
              <div className="p-2 bg-violet-100 rounded-lg">
                <Bell className="h-5 w-5 text-violet-600" />
              </div>
              <div>
                <h3 className="font-semibold text-violet-900">Smart Reminders</h3>
                <p className="text-sm text-violet-700">Automated notifications for campaign milestones and deadlines</p>
              </div>
            </div>
            
            <div className="flex items-start gap-3 p-4 bg-white/60 rounded-lg">
              <div className="p-2 bg-violet-100 rounded-lg">
                <Users className="h-5 w-5 text-violet-600" />
              </div>
              <div>
                <h3 className="font-semibold text-violet-900">Team Collaboration</h3>
                <p className="text-sm text-violet-700">Share calendars and collaborate with your marketing team</p>
              </div>
            </div>
            
            <div className="flex items-start gap-3 p-4 bg-white/60 rounded-lg">
              <div className="p-2 bg-violet-100 rounded-lg">
                <Zap className="h-5 w-5 text-violet-600" />
              </div>
              <div>
                <h3 className="font-semibold text-violet-900">AI Recommendations</h3>
                <p className="text-sm text-violet-700">Get AI-powered suggestions for optimal campaign timing</p>
              </div>
            </div>
          </div>

          {/* Coming Soon Message */}
          <div className="text-center p-6 bg-white/60 rounded-lg">
            <Clock className="h-8 w-8 text-violet-600 mx-auto mb-3" />
            <h3 className="text-lg font-semibold text-violet-900 mb-2">
              We&apos;re Building Something Amazing
            </h3>
            <p className="text-violet-700 mb-4">
              Our Campaign Calendar is currently in development. This powerful tool will help you plan, 
              schedule, and track your ad campaigns like never before.
            </p>
            <p className="text-sm text-violet-600">
              Want to be notified when it&apos;s ready? Follow us for updates!
            </p>
          </div>
        </CardContent>
      </Card>

      {/* CTA Section */}
      <div className="mt-8 text-center">
        <h2 className="text-xl font-semibold text-gray-900 mb-4">
          Meanwhile, explore your analyses
        </h2>
        <div className="flex flex-wrap justify-center gap-4">
          <Button variant="outline" className="bg-white" onClick={() => window.location.href = '/studio'}>
            Back to Studio
          </Button>
          <Button className="bg-violet-600 hover:bg-violet-700" onClick={() => window.location.href = '/ad-library'}>
            Browse Ad Library
          </Button>
        </div>
      </div>
    </div>
  )
}