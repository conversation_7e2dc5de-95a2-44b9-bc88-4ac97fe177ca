'use client';

import { useState, useEffect } from 'react';
import { useUser } from '@clerk/nextjs';
import Image from 'next/image';
import { 
  Image as ImageIcon, 
  Video, 
  Music, 
  FileText, 
  Download, 
  Heart, 
  Search, 
  Filter,
  Grid3X3,
  List,
  Calendar,
  Tag,
  Trash2,
  ExternalLink
} from 'lucide-react';

interface GeneratedAsset {
  id: string;
  asset_type: 'image' | 'video' | 'audio' | 'text';
  asset_name: string;
  asset_url: string;
  generation_type: string;
  prompt: string;
  negative_prompt?: string;
  generation_parameters: any;
  mime_type: string;
  is_favorite: boolean;
  tags: string[];
  created_at: string;
  file_size?: number;
  duration_seconds?: number;
  dimensions?: { width: number; height: number };
}

type AssetFilter = 'all' | 'image' | 'video' | 'audio' | 'text';
type ViewMode = 'grid' | 'list';

const AssetsPage = () => {
  const { user } = useUser();
  const [assets, setAssets] = useState<GeneratedAsset[]>([]);
  const [filteredAssets, setFilteredAssets] = useState<GeneratedAsset[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [activeFilter, setActiveFilter] = useState<AssetFilter>('all');
  const [viewMode, setViewMode] = useState<ViewMode>('grid');
  const [searchQuery, setSearchQuery] = useState('');

  // Fetch assets from API
  useEffect(() => {
    if (!user) return;

    const fetchAssets = async () => {
      try {
        setLoading(true);
        const response = await fetch('/api/v1/assets');
        
        if (!response.ok) {
          throw new Error('Failed to fetch assets');
        }

        const data = await response.json();
        setAssets(data.assets || []);
      } catch (err) {
        console.error('Error fetching assets:', err);
        setError(err instanceof Error ? err.message : 'Failed to load assets');
      } finally {
        setLoading(false);
      }
    };

    fetchAssets();
  }, [user]);

  // Filter and search assets
  useEffect(() => {
    let filtered = assets;

    // Apply type filter
    if (activeFilter !== 'all') {
      filtered = filtered.filter(asset => asset.asset_type === activeFilter);
    }

    // Apply search filter
    if (searchQuery.trim()) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(asset => 
        asset.asset_name.toLowerCase().includes(query) ||
        asset.prompt.toLowerCase().includes(query) ||
        asset.tags.some(tag => tag.toLowerCase().includes(query))
      );
    }

    setFilteredAssets(filtered);
  }, [assets, activeFilter, searchQuery]);

  const getAssetIcon = (type: string) => {
    switch (type) {
      case 'image': return <ImageIcon size={20} />;
      case 'video': return <Video size={20} />;
      case 'audio': return <Music size={20} />;
      case 'text': return <FileText size={20} />;
      default: return <FileText size={20} />;
    }
  };

  const getAssetTypeColor = (type: string) => {
    switch (type) {
      case 'image': return 'bg-blue-100 text-blue-800';
      case 'video': return 'bg-purple-100 text-purple-800';
      case 'audio': return 'bg-green-100 text-green-800';
      case 'text': return 'bg-orange-100 text-orange-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const formatFileSize = (bytes?: number) => {
    if (!bytes) return 'Unknown size';
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(1024));
    return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];
  };

  const formatDuration = (seconds?: number) => {
    if (!seconds) return null;
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  const handleDownload = async (asset: GeneratedAsset) => {
    try {
      const response = await fetch(asset.asset_url);
      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = asset.asset_name || `asset-${asset.id}`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);
    } catch (error) {
      console.error('Download failed:', error);
    }
  };

  const toggleFavorite = async (assetId: string) => {
    // TODO: Implement favorite toggle API
    console.log('Toggle favorite for asset:', assetId);
  };

  const filterButtons = [
    { key: 'all' as AssetFilter, label: 'All', icon: Filter },
    { key: 'image' as AssetFilter, label: 'Images', icon: ImageIcon },
    { key: 'video' as AssetFilter, label: 'Videos', icon: Video },
    { key: 'audio' as AssetFilter, label: 'Audio', icon: Music },
    { key: 'text' as AssetFilter, label: 'Text', icon: FileText },
  ];

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading your assets...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="text-red-600 mb-4">
            <FileText size={48} className="mx-auto mb-2" />
          </div>
          <h2 className="text-xl font-semibold text-gray-900 mb-2">Failed to Load Assets</h2>
          <p className="text-gray-600 mb-4">{error}</p>
          <button 
            onClick={() => window.location.reload()}
            className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors"
          >
            Try Again
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">My Assets</h1>
          <p className="text-gray-600">Manage your AI-generated content</p>
        </div>

        {/* Controls */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
          <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
            {/* Search */}
            <div className="relative flex-1 max-w-md">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={20} />
              <input
                type="text"
                placeholder="Search assets..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>

            {/* Filter Buttons */}
            <div className="flex flex-wrap gap-2">
              {filterButtons.map((filter) => {
                const IconComponent = filter.icon;
                return (
                  <button
                    key={filter.key}
                    onClick={() => setActiveFilter(filter.key)}
                    className={`flex items-center gap-2 px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
                      activeFilter === filter.key
                        ? 'bg-blue-600 text-white'
                        : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                    }`}
                  >
                    <IconComponent size={16} />
                    {filter.label}
                  </button>
                );
              })}
            </div>

            {/* View Mode Toggle */}
            <div className="flex bg-gray-100 rounded-lg p-1">
              <button
                onClick={() => setViewMode('grid')}
                className={`p-2 rounded-md transition-colors ${
                  viewMode === 'grid' ? 'bg-white shadow-sm' : 'hover:bg-gray-200'
                }`}
              >
                <Grid3X3 size={16} />
              </button>
              <button
                onClick={() => setViewMode('list')}
                className={`p-2 rounded-md transition-colors ${
                  viewMode === 'list' ? 'bg-white shadow-sm' : 'hover:bg-gray-200'
                }`}
              >
                <List size={16} />
              </button>
            </div>
          </div>
        </div>

        {/* Assets Grid/List */}
        {filteredAssets.length === 0 ? (
          <div className="text-center py-12">
            <div className="text-gray-400 mb-4">
              {getAssetIcon(activeFilter === 'all' ? 'image' : activeFilter)}
            </div>
            <h3 className="text-lg font-medium text-gray-900 mb-2">No assets found</h3>
            <p className="text-gray-600 mb-4">
              {searchQuery ? 'Try adjusting your search terms' : 'Start creating some amazing content!'}
            </p>
            <button 
              onClick={() => window.location.href = '/studio'}
              className="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors"
            >
              Create New Asset
            </button>
          </div>
        ) : (
          <div className={viewMode === 'grid' ? 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6' : 'space-y-4'}>
            {filteredAssets.map((asset) => (
              <div key={asset.id} className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden hover:shadow-md transition-shadow">
                {viewMode === 'grid' ? (
                  // Grid View
                  <>
                    {/* Asset Preview */}
                    <div className="aspect-square bg-gray-100 relative">
                      {asset.asset_type === 'image' ? (
                        <Image 
                          src={asset.asset_url} 
                          alt={asset.asset_name}
                          fill
                          className="object-cover"
                        />
                      ) : (
                        <div className="w-full h-full flex items-center justify-center">
                          {getAssetIcon(asset.asset_type)}
                        </div>
                      )}
                      
                      {/* Overlay Actions */}
                      <div className="absolute inset-0 bg-black bg-opacity-0 hover:bg-opacity-50 transition-all flex items-center justify-center opacity-0 hover:opacity-100">
                        <div className="flex gap-2">
                          <button
                            onClick={() => handleDownload(asset)}
                            className="bg-white text-gray-900 p-2 rounded-full hover:bg-gray-100 transition-colors"
                          >
                            <Download size={16} />
                          </button>
                          <button
                            onClick={() => window.open(asset.asset_url, '_blank')}
                            className="bg-white text-gray-900 p-2 rounded-full hover:bg-gray-100 transition-colors"
                          >
                            <ExternalLink size={16} />
                          </button>
                          <button
                            onClick={() => toggleFavorite(asset.id)}
                            className={`p-2 rounded-full transition-colors ${
                              asset.is_favorite 
                                ? 'bg-red-500 text-white' 
                                : 'bg-white text-gray-900 hover:bg-gray-100'
                            }`}
                          >
                            <Heart size={16} />
                          </button>
                        </div>
                      </div>
                    </div>

                    {/* Asset Info */}
                    <div className="p-4">
                      <div className="flex items-center justify-between mb-2">
                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${getAssetTypeColor(asset.asset_type)}`}>
                          {asset.asset_type}
                        </span>
                        <span className="text-xs text-gray-500">
                          {new Date(asset.created_at).toLocaleDateString()}
                        </span>
                      </div>
                      
                      <h3 className="font-medium text-gray-900 mb-1 truncate">{asset.asset_name}</h3>
                      <p className="text-sm text-gray-600 line-clamp-2 mb-2">{asset.prompt}</p>
                      
                      <div className="flex items-center justify-between text-xs text-gray-500">
                        <span>{formatFileSize(asset.file_size)}</span>
                        {asset.duration_seconds && (
                          <span>{formatDuration(asset.duration_seconds)}</span>
                        )}
                      </div>
                    </div>
                  </>
                ) : (
                  // List View
                  <div className="p-4 flex items-center gap-4">
                    <div className="w-16 h-16 bg-gray-100 rounded-lg flex items-center justify-center flex-shrink-0">
                      {asset.asset_type === 'image' ? (
                        <Image 
                          src={asset.asset_url} 
                          alt={asset.asset_name}
                          width={64}
                          height={64}
                          className="object-cover rounded-lg"
                        />
                      ) : (
                        getAssetIcon(asset.asset_type)
                      )}
                    </div>
                    
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center gap-2 mb-1">
                        <h3 className="font-medium text-gray-900 truncate">{asset.asset_name}</h3>
                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${getAssetTypeColor(asset.asset_type)}`}>
                          {asset.asset_type}
                        </span>
                      </div>
                      <p className="text-sm text-gray-600 line-clamp-1 mb-1">{asset.prompt}</p>
                      <div className="flex items-center gap-4 text-xs text-gray-500">
                        <span>{formatFileSize(asset.file_size)}</span>
                        {asset.duration_seconds && <span>{formatDuration(asset.duration_seconds)}</span>}
                        <span>{new Date(asset.created_at).toLocaleDateString()}</span>
                      </div>
                    </div>
                    
                    <div className="flex items-center gap-2">
                      <button
                        onClick={() => handleDownload(asset)}
                        className="p-2 text-gray-400 hover:text-gray-600 transition-colors"
                      >
                        <Download size={16} />
                      </button>
                      <button
                        onClick={() => window.open(asset.asset_url, '_blank')}
                        className="p-2 text-gray-400 hover:text-gray-600 transition-colors"
                      >
                        <ExternalLink size={16} />
                      </button>
                      <button
                        onClick={() => toggleFavorite(asset.id)}
                        className={`p-2 transition-colors ${
                          asset.is_favorite 
                            ? 'text-red-500' 
                            : 'text-gray-400 hover:text-gray-600'
                        }`}
                      >
                        <Heart size={16} />
                      </button>
                    </div>
                  </div>
                )}
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

export default AssetsPage;
