'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Building2, Plus, Edit, Trash2, Globe, Palette, Calendar, TrendingUp, MoreVertical } from 'lucide-react'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import { useRouter } from 'next/navigation'
import Image from 'next/image'

interface BrandProfile {
  id: string
  brand_name: string
  slug: string
  tagline?: string
  logo_url?: string
  website_url?: string
  industry_category: string
  completion_percentage: number
  creation_method: string
  created_at: string
  updated_at: string
  last_analysis_date?: string
}

export default function BrandPage() {
  const [brands, setBrands] = useState<BrandProfile[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState('')
  const router = useRouter()

  useEffect(() => {
    fetchBrands()
  }, [])

  const fetchBrands = async () => {
    try {
      const response = await fetch('/api/brands')
      if (!response.ok) {
        throw new Error('Failed to fetch brands')
      }
      const data = await response.json()
      setBrands(data.brands || [])
    } catch (err: any) {
      setError(err.message || 'Failed to load brands')
    } finally {
      setLoading(false)
    }
  }

  const handleDeleteBrand = async (brandId: string) => {
    if (!confirm('Are you sure you want to delete this brand profile? This action cannot be undone.')) {
      return
    }

    try {
      const response = await fetch(`/api/brands/${brandId}`, {
        method: 'DELETE'
      })

      if (!response.ok) {
        throw new Error('Failed to delete brand')
      }

      // Remove from local state
      setBrands(brands.filter(brand => brand.id !== brandId))
    } catch (err: any) {
      alert(err.message || 'Failed to delete brand')
    }
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    })
  }

  const getCompletionColor = (percentage: number) => {
    if (percentage >= 80) return 'bg-green-100 text-green-800'
    if (percentage >= 50) return 'bg-yellow-100 text-yellow-800'
    return 'bg-red-100 text-red-800'
  }

  if (loading) {
    return (
      <div className="flex flex-1 flex-col gap-6 p-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Brand Center</h1>
            <p className="text-gray-600">Manage your brand profiles and guidelines</p>
          </div>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {[1, 2, 3].map((i) => (
            <Card key={i} className="animate-pulse">
              <CardContent className="p-6">
                <div className="h-4 bg-gray-200 rounded mb-4"></div>
                <div className="h-6 bg-gray-200 rounded mb-2"></div>
                <div className="h-4 bg-gray-200 rounded w-3/4"></div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    )
  }

  return (
    <div className="flex flex-1 flex-col gap-6 p-6">
      {/* Page Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Brand Center</h1>
          <p className="text-gray-600">Manage your brand profiles and guidelines</p>
        </div>
        <Button onClick={() => router.push('/studio/brand/create')} className="bg-rose-600 hover:bg-rose-700">
          <Plus className="h-4 w-4 mr-2" />
          Create Brand
        </Button>
      </div>

      {error && (
        <Card className="border-red-200 bg-red-50">
          <CardContent className="p-4">
            <p className="text-red-600">{error}</p>
          </CardContent>
        </Card>
      )}

      {/* Brands Grid */}
      {brands.length === 0 ? (
        <Card className="max-w-4xl bg-gradient-to-br from-rose-50 to-pink-50 border-rose-200/60 shadow-lg">
          <CardHeader className="text-center pb-4">
            <div className="flex justify-center mb-4">
              <div className="p-4 bg-rose-100 rounded-full">
                <Building2 className="h-12 w-12 text-rose-600" />
              </div>
            </div>
            <CardTitle className="text-2xl text-rose-900">Create Your First Brand</CardTitle>
            <CardDescription className="text-lg text-rose-700">
              Build comprehensive brand profiles to ensure consistent messaging across all your campaigns
            </CardDescription>
          </CardHeader>
          
          <CardContent className="pt-4">
            {/* Feature Preview */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
              <div className="flex items-start gap-3 p-4 bg-white/60 rounded-lg">
                <div className="p-2 bg-rose-100 rounded-lg">
                  <Palette className="h-5 w-5 text-rose-600" />
                </div>
                <div>
                  <h3 className="font-semibold text-rose-900">Brand Guidelines</h3>
                  <p className="text-sm text-rose-700">Store colors, fonts, logos, and style guidelines</p>
                </div>
              </div>
              
              <div className="flex items-start gap-3 p-4 bg-white/60 rounded-lg">
                <div className="p-2 bg-rose-100 rounded-lg">
                  <TrendingUp className="h-5 w-5 text-rose-600" />
                </div>
                <div>
                  <h3 className="font-semibold text-rose-900">Brand Voice Analysis</h3>
                  <p className="text-sm text-rose-700">AI-powered analysis for consistent messaging</p>
                </div>
              </div>
              
              <div className="flex items-start gap-3 p-4 bg-white/60 rounded-lg">
                <div className="p-2 bg-rose-100 rounded-lg">
                  <Building2 className="h-5 w-5 text-rose-600" />
                </div>
                <div>
                  <h3 className="font-semibold text-rose-900">Brand Compliance</h3>
                  <p className="text-sm text-rose-700">Automated checks against brand standards</p>
                </div>
              </div>
            </div>

            <div className="text-center">
              <Button 
                onClick={() => router.push('/studio/brand/create')} 
                className="bg-rose-600 hover:bg-rose-700"
                size="lg"
              >
                <Plus className="h-5 w-5 mr-2" />
                Create Your First Brand
              </Button>
            </div>
          </CardContent>
        </Card>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {brands.map((brand) => (
            <Card key={brand.id} className="hover:shadow-lg transition-shadow">
              <CardHeader className="pb-3">
                <div className="flex items-start justify-between">
                  <div className="flex items-center gap-3">
                    {brand.logo_url ? (
                      <div className="relative w-10 h-10">
                        <Image
                          src={brand.logo_url}
                          alt={`${brand.brand_name} logo`}
                          fill
                          className="rounded-lg object-cover"
                        />
                      </div>
                    ) : (
                      <div className="w-10 h-10 bg-rose-100 rounded-lg flex items-center justify-center">
                        <Building2 className="h-5 w-5 text-rose-600" />
                      </div>
                    )}
                    <div className="min-w-0">
                      <CardTitle className="text-lg leading-tight truncate">{brand.brand_name}</CardTitle>
                      {brand.tagline && (
                        <CardDescription className="text-sm mt-1 truncate">
                          {brand.tagline}
                        </CardDescription>
                      )}
                    </div>
                  </div>
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                        <MoreVertical className="h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuItem onClick={() => router.push(`/studio/brand/${brand.id}`)}>
                        <Edit className="h-4 w-4 mr-2" />
                        Edit
                      </DropdownMenuItem>
                      {brand.website_url && (
                        <DropdownMenuItem onClick={() => window.open(brand.website_url, '_blank')}>
                          <Globe className="h-4 w-4 mr-2" />
                          Visit Website
                        </DropdownMenuItem>
                      )}
                      <DropdownMenuItem 
                        onClick={() => handleDeleteBrand(brand.id)}
                        className="text-red-600"
                      >
                        <Trash2 className="h-4 w-4 mr-2" />
                        Delete
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </div>
              </CardHeader>
              
              <CardContent className="pt-0">
                <div className="space-y-3">
                  <div className="flex items-center justify-between text-sm">
                    <span className="text-gray-600">Industry</span>
                    <Badge variant="secondary" className="text-xs">
                      {brand.industry_category}
                    </Badge>
                  </div>
                  
                  <div className="flex items-center justify-between text-sm">
                    <span className="text-gray-600">Completion</span>
                    <Badge className={`text-xs ${getCompletionColor(brand.completion_percentage)}`}>
                      {brand.completion_percentage}%
                    </Badge>
                  </div>
                  
                  <div className="flex items-center justify-between text-sm">
                    <span className="text-gray-600">Created</span>
                    <span className="text-gray-900 flex items-center">
                      <Calendar className="h-3 w-3 mr-1" />
                      {formatDate(brand.created_at)}
                    </span>
                  </div>
                  
                  <div className="pt-2">
                    <Button 
                      variant="outline" 
                      size="sm" 
                      className="w-full"
                      onClick={() => router.push(`/studio/brand/${brand.id}`)}
                    >
                      View Details
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}

      {/* CTA Section */}
      {brands.length > 0 && (
        <div className="mt-8 text-center">
          <h2 className="text-xl font-semibold text-gray-900 mb-4">
            Analyze your brand performance
          </h2>
          <div className="flex flex-wrap justify-center gap-4">
            <Button variant="outline" className="bg-white" onClick={() => router.push('/studio/optimise')}>
              Analyze Your Ads
            </Button>
            <Button className="bg-rose-600 hover:bg-rose-700" onClick={() => router.push('/ad-library')}>
              Browse Brand Examples
            </Button>
          </div>
        </div>
      )}
    </div>
  )
}