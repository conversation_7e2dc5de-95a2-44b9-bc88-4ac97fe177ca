'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { useAuth } from '@/hooks/useAuth'
import { useRouter } from 'next/navigation'
import { 
  Sparkles, 
  BarChart3, 
  Target, 
  Zap, 
  CheckCircle, 
  Clock,
  ArrowLeft,
  FolderOpen
} from 'lucide-react'

const studioFeatures = [
  {
    icon: BarChart3,
    title: "Optimise",
    description: "Optimise your ad performance with AI insights and comprehensive analysis before launching your campaigns.",
    gradient: "from-blue-500 to-blue-600",
    available: true
  },
  {
    icon: Sparkles,
    title: "Create",
    description: "AI-powered ad creation and optimization tools to generate compelling content for your campaigns.",
    gradient: "from-purple-500 to-purple-600",
    beta: true,
    available: true
  },
  {
    icon: Target,
    title: "Brand Center",
    description: "Manage your brand identity, guidelines, and maintain consistency across all your marketing materials.",
    gradient: "from-rose-500 to-rose-600",
    available: true
  },
  {
    icon: Clock,
    title: "Assets Library",
    description: "View and manage your AI-generated content, organize your creative assets, and track performance.",
    gradient: "from-emerald-500 to-emerald-600",
    available: true
  },
  {
    icon: Zap,
    title: "Campaign Planning",
    description: "Schedule and plan your ad campaigns with strategic insights and automated optimization recommendations.",
    gradient: "from-violet-500 to-violet-600",
    comingSoon: true
  },
  {
    icon: CheckCircle,
    title: "Market Explorer",
    description: "Discover trends and market insights to stay ahead of the competition and identify new opportunities.",
    gradient: "from-teal-500 to-teal-600",
    comingSoon: true
  }
]

export default function StudioWaitlistPage() {
  const { user, loading } = useAuth()
  const router = useRouter()
  const [isRequesting, setIsRequesting] = useState(false)
  const [requestStatus, setRequestStatus] = useState<'idle' | 'success' | 'already-requested' | 'error'>('idle')

  useEffect(() => {
    // Check if user is already on waitlist
    const checkWaitlistStatus = async () => {
      if (!user) return

      try {
        const response = await fetch('/api/studio/waitlist/check')
        if (response.ok) {
          const { isOnWaitlist } = await response.json()
          if (isOnWaitlist) {
            setRequestStatus('already-requested')
          }
        }
      } catch (error) {
        console.error('Error checking waitlist status:', error)
      }
    }

    checkWaitlistStatus()
  }, [user])

  const handleRequestAccess = async () => {
    if (!user) return

    setIsRequesting(true)
    
    try {
      const response = await fetch('/api/studio/waitlist/request', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email: user.emailAddresses?.[0]?.emailAddress
        })
      })

      if (response.ok) {
        setRequestStatus('success')
      } else {
        const errorData = await response.json()
        if (response.status === 409) {
          setRequestStatus('already-requested')
        } else {
          throw new Error(errorData.error || 'Failed to request access')
        }
      }
    } catch (error) {
      console.error('Error requesting studio access:', error)
      setRequestStatus('error')
    } finally {
      setIsRequesting(false)
    }
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-50 via-white to-slate-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-white to-slate-50">
      {/* Animated background elements */}
      <div className="fixed inset-0 overflow-hidden pointer-events-none">
        <div className="absolute -top-40 -right-40 w-80 h-80 bg-gradient-to-br from-blue-400/20 to-purple-600/20 rounded-full blur-3xl animate-pulse"></div>
        <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-gradient-to-br from-pink-400/20 to-orange-600/20 rounded-full blur-3xl animate-pulse delay-1000"></div>
      </div>

      <div className="relative container mx-auto px-6 py-12">
        {/* Header */}
        <div className="flex items-center mb-8">
          <Button 
            variant="ghost" 
            size="sm"
            onClick={() => router.push('/')}
            className="mr-4 text-gray-600 hover:text-gray-900"
          >
            <ArrowLeft className="w-4 h-4 mr-2" />
            Back to Home
          </Button>
        </div>

        <div className="max-w-4xl mx-auto">
          {/* Hero Section */}
          <div className="text-center mb-16">
            <Badge 
              variant="secondary" 
              className="mb-4 px-4 py-2 bg-gradient-to-r from-blue-50 to-purple-50 text-blue-700 border border-blue-200/50"
            >
              <Sparkles className="w-4 h-4 mr-2" />
              Coming Soon
            </Badge>
            
            <h1 className="text-4xl md:text-5xl font-bold mb-6 bg-gradient-to-r from-gray-900 via-blue-800 to-purple-800 bg-clip-text text-transparent">
              Studio Access
            </h1>
            
            <p className="text-xl text-gray-600 mb-8 max-w-2xl mx-auto">
              Get early access to our comprehensive ad creation and optimization studio with AI-powered tools, 
              brand management, and advanced analytics to supercharge your campaigns.
            </p>

            {/* Request Access Card */}
            <Card className="bg-white/80 backdrop-blur-sm border border-gray-200/50 shadow-xl max-w-md mx-auto">
              <CardHeader className="text-center">
                <CardTitle className="text-2xl font-bold text-gray-900">
                  {requestStatus === 'success' ? 'Request Submitted!' : 
                   requestStatus === 'already-requested' ? 'Already on Waitlist' : 
                   'Request Early Access'}
                </CardTitle>
              </CardHeader>
              <CardContent className="text-center space-y-4">
                {requestStatus === 'success' && (
                  <div className="space-y-3">
                    <CheckCircle className="w-16 h-16 text-green-500 mx-auto" />
                    <p className="text-gray-600">
                      We&apos;ve added you to our waitlist! You&apos;ll be notified via email when Studio access is available.
                    </p>
                    <Badge variant="secondary" className="bg-green-50 text-green-700 border-green-200">
                      <Clock className="w-3 h-3 mr-1" />
                      Waitlist Confirmed
                    </Badge>
                  </div>
                )}
                
                {requestStatus === 'already-requested' && (
                  <div className="space-y-3">
                    <Clock className="w-16 h-16 text-blue-500 mx-auto" />
                    <p className="text-gray-600">
                      You&apos;re already on our waitlist! We&apos;ll notify you as soon as Studio access is available.
                    </p>
                    <Badge variant="secondary" className="bg-blue-50 text-blue-700 border-blue-200">
                      <Clock className="w-3 h-3 mr-1" />
                      On Waitlist
                    </Badge>
                  </div>
                )}
                
                {(requestStatus === 'idle' || requestStatus === 'error') && (
                  <div className="space-y-4">
                    <p className="text-gray-600">
                      Be among the first to experience our comprehensive studio with AI-powered ad creation, optimization tools, and brand management features.
                    </p>
                    
                    {requestStatus === 'error' && (
                      <div className="text-red-600 text-sm bg-red-50 border border-red-200 rounded-lg p-3">
                        Failed to submit request. Please try again.
                      </div>
                    )}
                    
                    <Button 
                      onClick={handleRequestAccess}
                      disabled={isRequesting || !user}
                      className="w-full bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white py-3 font-semibold rounded-lg shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-300"
                    >
                      {isRequesting ? (
                        <>
                          <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                          Submitting...
                        </>
                      ) : (
                        'Request Access'
                      )}
                    </Button>
                    
                    {user && (
                      <p className="text-sm text-gray-500">
                        Requesting as: {user.emailAddresses?.[0]?.emailAddress}
                      </p>
                    )}
                  </div>
                )}
              </CardContent>
            </Card>
          </div>

          {/* Features Grid */}
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
            {studioFeatures.map((feature, index) => {
              const IconComponent = feature.icon
              return (
                <Card key={index} className="bg-white/60 backdrop-blur-sm border border-gray-200/50 rounded-xl p-6 transition-all duration-300">
                  <CardHeader className="p-0 mb-4">
                    <div className="flex items-center justify-between mb-4">
                      <div className={`w-12 h-12 bg-gradient-to-br ${feature.gradient} flex items-center justify-center rounded-lg shadow-lg`}>
                        <IconComponent className="w-6 h-6 text-white" />
                      </div>
                      <div className="flex flex-col gap-1">
                        {feature.available && (
                          <Badge className="text-xs px-2 py-1 bg-green-50 text-green-700 border-green-200">
                            Available
                          </Badge>
                        )}
                        {feature.beta && (
                          <Badge className="text-xs px-2 py-1 bg-blue-50 text-blue-700 border-blue-200">
                            Beta
                          </Badge>
                        )}
                        {feature.comingSoon && (
                          <Badge className="text-xs px-2 py-1 bg-orange-50 text-orange-700 border-orange-200">
                            Coming Soon
                          </Badge>
                        )}
                      </div>
                    </div>
                    <CardTitle className="text-lg font-bold text-gray-900 mb-3">
                      {feature.title}
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="p-0">
                    <p className="text-gray-600 leading-relaxed text-sm">
                      {feature.description}
                    </p>
                  </CardContent>
                </Card>
              )
            })}
          </div>

          {/* Bottom CTA */}
          <div className="text-center mt-16 p-8 bg-gradient-to-r from-blue-50 via-purple-50 to-pink-50 rounded-2xl border border-gray-200/50">
            <h3 className="text-2xl font-bold text-gray-900 mb-4">
              Want to learn more?
            </h3>
            <p className="text-gray-600 mb-6">
              Explore our current features while you wait for Studio access.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button 
                variant="outline" 
                onClick={() => router.push('/ad-library')}
                className="border-gray-300 hover:border-gray-400"
              >
                Browse Ad Library
              </Button>
              <Button 
                variant="outline"
                onClick={() => router.push('/featured')}
                className="border-gray-300 hover:border-gray-400"
              >
                View Featured Analysis
              </Button>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}