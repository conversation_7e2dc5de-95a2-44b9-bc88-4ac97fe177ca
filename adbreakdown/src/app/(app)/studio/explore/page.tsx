'use client'

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Compass, Sparkles, TrendingUp, Search, Filter, Globe } from 'lucide-react'

export default function ExplorePage() {
  return (
    <div className="flex flex-1 flex-col gap-6 p-6">
      {/* Header */}
      <div className="flex items-center gap-4 mb-4">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Explore</h1>
          <p className="text-gray-600">Discover trends, insights, and opportunities in video advertising</p>
        </div>
      </div>

      {/* Coming Soon Card */}
      <Card className="max-w-4xl bg-gradient-to-br from-teal-50 to-cyan-50 border-teal-200/60 shadow-lg">
        <CardHeader className="text-center pb-4">
          <div className="flex justify-center mb-4">
            <div className="p-4 bg-teal-100 rounded-full">
              <Compass className="h-12 w-12 text-teal-600" />
            </div>
          </div>
          <CardTitle className="text-2xl text-teal-900">Explore Hub</CardTitle>
          <CardDescription className="text-lg text-teal-700">
            Discover advertising trends and market insights
          </CardDescription>
          <div className="flex justify-center mt-4">
            <div className="px-4 py-2 bg-teal-100 text-teal-800 rounded-full text-sm font-semibold">
              COMING SOON
            </div>
          </div>
        </CardHeader>
        
        <CardContent className="pt-4">
          {/* Feature Preview */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
            <div className="flex items-start gap-3 p-4 bg-white/60 rounded-lg">
              <div className="p-2 bg-teal-100 rounded-lg">
                <TrendingUp className="h-5 w-5 text-teal-600" />
              </div>
              <div>
                <h3 className="font-semibold text-teal-900">Trending Analysis</h3>
                <p className="text-sm text-teal-700">Discover what types of ads are performing best in your industry</p>
              </div>
            </div>
            
            <div className="flex items-start gap-3 p-4 bg-white/60 rounded-lg">
              <div className="p-2 bg-teal-100 rounded-lg">
                <Search className="h-5 w-5 text-teal-600" />
              </div>
              <div>
                <h3 className="font-semibold text-teal-900">Smart Discovery</h3>
                <p className="text-sm text-teal-700">AI-powered recommendations for ads similar to your successful campaigns</p>
              </div>
            </div>
            
            <div className="flex items-start gap-3 p-4 bg-white/60 rounded-lg">
              <div className="p-2 bg-teal-100 rounded-lg">
                <Filter className="h-5 w-5 text-teal-600" />
              </div>
              <div>
                <h3 className="font-semibold text-teal-900">Advanced Filtering</h3>
                <p className="text-sm text-teal-700">Filter by performance metrics, demographics, and campaign objectives</p>
              </div>
            </div>
            
            <div className="flex items-start gap-3 p-4 bg-white/60 rounded-lg">
              <div className="p-2 bg-teal-100 rounded-lg">
                <Globe className="h-5 w-5 text-teal-600" />
              </div>
              <div>
                <h3 className="font-semibold text-teal-900">Market Intelligence</h3>
                <p className="text-sm text-teal-700">Global advertising trends and regional performance insights</p>
              </div>
            </div>
            
            <div className="flex items-start gap-3 p-4 bg-white/60 rounded-lg">
              <div className="p-2 bg-teal-100 rounded-lg">
                <Sparkles className="h-5 w-5 text-teal-600" />
              </div>
              <div>
                <h3 className="font-semibold text-teal-900">Inspiration Feed</h3>
                <p className="text-sm text-teal-700">Curated collection of creative campaigns and innovative ad formats</p>
              </div>
            </div>
          </div>

          {/* Coming Soon Message */}
          <div className="text-center p-6 bg-white/60 rounded-lg">
            <Sparkles className="h-8 w-8 text-teal-600 mx-auto mb-3" />
            <h3 className="text-lg font-semibold text-teal-900 mb-2">
              Discover What Works
            </h3>
            <p className="text-teal-700 mb-4">
              The Explore Hub will be your gateway to understanding advertising trends, 
              discovering high-performing creative strategies, and finding inspiration for your next campaign.
            </p>
            <p className="text-sm text-teal-600">
              Stay tuned for powerful discovery and trend analysis tools!
            </p>
          </div>
        </CardContent>
      </Card>

      {/* CTA Section */}
      <div className="mt-8 text-center">
        <h2 className="text-xl font-semibold text-gray-900 mb-4">
          Start exploring with what&apos;s available now
        </h2>
        <div className="flex flex-wrap justify-center gap-4">
          <Button variant="outline" className="bg-white" onClick={() => window.location.href = '/ad-library'}>
            Browse Ad Library
          </Button>
          <Button className="bg-teal-600 hover:bg-teal-700" onClick={() => window.location.href = '/featured'}>
            Featured Analyses
          </Button>
        </div>
      </div>
    </div>
  )
}