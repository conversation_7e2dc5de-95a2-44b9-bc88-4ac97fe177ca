'use client'

import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card'
import PrivateAnalysisCard from '@/components/optimise/PrivateAnalysisCard'
import { useCredits } from '@/hooks/useCredits'
export default function AnalysePage() {
  const { profile } = useCredits()
  const creditsRemaining = profile?.credits_remaining || 0

  return (
    <div className="flex flex-1 flex-col gap-6 p-6">
      {/* Private Analysis Card */}
      <div className="max-w-4xl">
        <PrivateAnalysisCard creditsRemaining={creditsRemaining} />
      </div>
    </div>
  )
}