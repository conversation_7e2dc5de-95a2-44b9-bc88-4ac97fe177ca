interface VideoMetadata {
  title: string
  thumbnail: string
  duration: string
  inferredBrandName: string
}

interface YouTubeMetadata {
  title: string
  description: string
  channelTitle: string
  publishedAt: string
  viewCount: string
  likeCount: string
  commentCount: string
  tags: string[]
  categoryId: string
  defaultLanguage?: string
  duration: string
  definition: string
}

interface DetailedAnalysisData {
  overallSentiment: number
  emotions: Record<string, number>
  scriptAnalysis: {
    transcript: string
    keyThemes: string[]
    sentimentTimeline: Array<{ time: number; sentiment: number }>
  }
  visualAnalysis: {
    scenes: Array<{ time: number; description: string; objects: string[] }>
    colorPalette: string[]
    visualAppeal: number
  }
  audioAnalysis: {
    musicMood: string
    voiceTone: string
    audioQuality: number
    soundEffects: string[]
  }
  targetingRecommendations: {
    demographics: string[]
    interests: string[]
    behaviors: string[]
  }
  competitorComparison: Array<{ name: string; sentiment: number; effectiveness: number }>
}

interface Report {
  id: string;
  report_types: { name: string };
  content: any;
  status: string;
}

export interface AdAnalysis {
  id: string;
  created_at: string;
  updated_at: string;
  user_id: string;
  video_url: string;
  video_title: string;
  video_thumbnail_url: string;
  video_duration: number;
  status: 'pending' | 'processing' | 'completed' | 'failed' | 'generated';
  overall_sentiment: number;
  emotions: Record<string, number>;
  transcript: string;
  summary: string;
  key_themes: string[];
  inferred_brand: string;
  marketing_analysis: string;
  marketing_copy: string;
  social_media_posts: string;
  marketing_scorecard: string;
  seo_keywords: string;
  content_suggestions: string;
  is_public: boolean;
  slug: string;
  youtube_video_id: string;
  reports?: Report[];
  deciphered_script?: { enhanced_analysis: string; enhanced_analysis_model: string; enhanced_analysis_generated_at: string };
  sentiment_timeline?: Array<{ time: number; sentiment: number }>;
  scenes?: Array<{ time: number; description: string; objects: string[] }>;
  color_palette?: string[];
  visual_appeal?: number;
  music_mood?: string;
  voice_tone?: string;
  audio_quality?: number;
  sound_effects?: string[];
  target_demographics?: string[];
  target_interests?: string[];
  target_behaviors?: string[];
  competitor_comparison?: Array<{ name: string; sentiment: number; effectiveness: number }>;
  
  // Celebrity and campaign fields (now arrays, with backward compatibility)
  celebrity?: string[] | string | null;  // Can be array (new) or string (legacy)
  celebrity_context?: string | null;
  campaign_category?: string[] | string | null;  // Can be array (new) or string (legacy)
  
  // Deprecated field names (will be removed after migration)
  celebrity_names?: string[] | null;
  campaign_categories?: string[] | null;
  
  // Other metadata fields
  brand?: string | null;
  product_category?: string | null;
  geography?: string | null;
  agency?: string | null;
  
  // Collection tags for organizing ads
  collection_tags?: string[] | null;
}

// Public analysis interface for ad library
export interface PublicAnalysis {
  id: string
  slug: string
  title: string
  brand: string
  inferred_brand: string
  thumbnail_url: string
  video_thumbnail_url: string
  duration_seconds: number
  duration_formatted: string
  created_at: string
  analysis_completed_at: string
  youtube_video_id: string
  is_public: boolean
  
  // Celebrity and campaign fields (now arrays, with backward compatibility)
  celebrity?: string[] | string | null  // Can be array (new) or string (legacy)
  celebrity_context?: string | null
  campaign_category?: string[] | string | null  // Can be array (new) or string (legacy)
  
  // Deprecated field names (will be removed after migration)
  celebrity_names?: string[] | null
  campaign_categories?: string[] | null
  
  // Other metadata fields
  product_category?: string | null
  geography?: string | null
  agency?: string | null
  marketing_analysis?: string | null
  
  // Collection tags for organizing ads
  collection_tags?: string[] | null
}

// Helper functions for backward compatibility
export const getCelebrityDisplay = (analysis: PublicAnalysis | AdAnalysis): string => {
  // Handle new array format in celebrity field
  if (analysis.celebrity && Array.isArray(analysis.celebrity)) {
    return analysis.celebrity.join(', ')
  }
  
  // Handle deprecated celebrity_names field
  if (analysis.celebrity_names && analysis.celebrity_names.length > 0) {
    return analysis.celebrity_names.join(', ')
  }
  
  // Handle legacy string format
  if (analysis.celebrity && typeof analysis.celebrity === 'string') {
    return analysis.celebrity
  }
  
  return ''
}

export const getCelebrityArray = (analysis: PublicAnalysis | AdAnalysis): string[] => {
  // First check new array format in celebrity field
  if (analysis.celebrity && Array.isArray(analysis.celebrity)) {
    return analysis.celebrity.filter(Boolean)
  }
  
  // Check deprecated celebrity_names field
  if (analysis.celebrity_names && analysis.celebrity_names.length > 0) {
    return analysis.celebrity_names
  }
  
  // Handle legacy string format
  if (analysis.celebrity && typeof analysis.celebrity === 'string') {
    return analysis.celebrity.split(',').map(name => name.trim()).filter(Boolean)
  }
  
  return []
}

export const getCampaignCategoriesDisplay = (analysis: PublicAnalysis | AdAnalysis): string => {
  // Handle new array format in campaign_category field
  if (analysis.campaign_category && Array.isArray(analysis.campaign_category)) {
    return analysis.campaign_category.join(', ')
  }
  
  // Handle deprecated campaign_categories field  
  if (analysis.campaign_categories && analysis.campaign_categories.length > 0) {
    return analysis.campaign_categories.join(', ')
  }
  
  // Handle legacy string format
  if (analysis.campaign_category && typeof analysis.campaign_category === 'string') {
    return analysis.campaign_category
  }
  
  return ''
}

export const getCampaignCategoriesArray = (analysis: PublicAnalysis | AdAnalysis): string[] => {
  // First check new array format in campaign_category field
  if (analysis.campaign_category && Array.isArray(analysis.campaign_category)) {
    return analysis.campaign_category.filter(Boolean)
  }
  
  // Check deprecated campaign_categories field
  if (analysis.campaign_categories && analysis.campaign_categories.length > 0) {
    return analysis.campaign_categories
  }
  
  // Handle legacy string format
  if (analysis.campaign_category && typeof analysis.campaign_category === 'string') {
    return analysis.campaign_category
      .split(/[,/]/)
      .map(cat => cat.trim())
      .filter(Boolean)
  }
  
  return []
}

export const getCelebrityWithContext = (analysis: PublicAnalysis | AdAnalysis): string => {
  const names = getCelebrityArray(analysis)
  
  // If there are actual celebrity names, show them with context
  if (names.length > 0) {
    const displayNames = names.join(', ')
    if (analysis.celebrity_context) {
      return `${displayNames} (${analysis.celebrity_context})`
    }
    return displayNames
  }
  
  // If no celebrity names but there's context (e.g., "Rajinikanth (caricature)"), show just the context
  if (analysis.celebrity_context) {
    return analysis.celebrity_context
  }
  
  return ''
}

export const hasCelebrityConnection = (analysis: PublicAnalysis | AdAnalysis): boolean => {
  return getCelebrityArray(analysis).length > 0 || !!analysis.celebrity_context
}
