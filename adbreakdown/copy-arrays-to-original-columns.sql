-- Copy cleaned array data from new columns to original columns
-- This preserves all the array formatting work we did

BEGIN;

-- Step 1: Show what we're about to change
SELECT 
  'Before Copy' as status,
  COUNT(*) as total_records,
  COUNT(celebrity) as has_celebrity_old,
  COUNT(celebrity_names) as has_celebrity_names,
  COUNT(campaign_category) as has_campaign_category_old,
  COUNT(campaign_categories) as has_campaign_categories
FROM ad_analyses;

-- Step 2: Copy celebrity_names to celebrity (overwrite old string data with cleaned arrays)
UPDATE ad_analyses 
SET celebrity = celebrity_names 
WHERE celebrity_names IS NOT NULL;

-- Step 3: Copy campaign_categories to campaign_category (overwrite old string data with cleaned arrays)
UPDATE ad_analyses 
SET campaign_category = campaign_categories 
WHERE campaign_categories IS NOT NULL;

-- Step 4: Show what changed
SELECT 
  'After Copy' as status,
  COUNT(*) as total_records,
  COUNT(celebrity) as has_celebrity_arrays,
  COUNT(campaign_category) as has_campaign_category_arrays,
  COUNT(CASE WHEN array_length(celebrity, 1) > 1 THEN 1 END) as multi_celebrity_records,
  COUNT(CASE WHEN array_length(campaign_category, 1) > 1 THEN 1 END) as multi_category_records,
  MAX(array_length(celebrity, 1)) as max_celebrities,
  MAX(array_length(campaign_category, 1)) as max_categories
FROM ad_analyses;

-- Step 5: Show sample of converted data
SELECT 
  'Sample Data' as info,
  id,
  celebrity,
  celebrity_context,
  campaign_category
FROM ad_analyses 
WHERE celebrity IS NOT NULL 
  OR campaign_category IS NOT NULL
ORDER BY array_length(celebrity, 1) DESC NULLS LAST
LIMIT 5;

-- Step 6: Drop the redundant columns
ALTER TABLE ad_analyses DROP COLUMN IF EXISTS celebrity_names;
ALTER TABLE ad_analyses DROP COLUMN IF EXISTS campaign_categories;

-- Step 7: Final verification
SELECT 
  'Final State' as status,
  COUNT(*) as total_records,
  COUNT(celebrity) as records_with_celebrity_arrays,
  COUNT(campaign_category) as records_with_category_arrays
FROM ad_analyses;

COMMIT;

-- Show success message
SELECT 
  '✅ Migration Complete!' as message,
  'celebrity and campaign_category columns now contain properly formatted arrays' as description;